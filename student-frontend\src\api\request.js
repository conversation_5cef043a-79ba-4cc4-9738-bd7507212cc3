import axios from 'axios'
import { showToast } from 'vant'
import { useAuthStore } from '@/stores/auth'

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const authStore = useAuthStore()
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          showToast('未授权，请重新登录')
          authStore.logout()
          window.location.href = '/login'
          break
        case 403:
          showToast('权限不足')
          break
        case 404:
          showToast('请求的资源不存在')
          break
        case 500:
          showToast('服务器内部错误')
          break
        default:
          showToast(data?.message || '请求失败')
      }
    } else if (error.request) {
      showToast('网络错误，请检查网络连接')
    } else {
      showToast('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default request
