package com.lait.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 飞书文件DTO
 */
@Data
public class FeishuFileDto {
    
    /**
     * 文件token
     */
    private String token;
    
    /**
     * 文件名称
     */
    private String name;
    
    /**
     * 文件类型
     */
    private String type;
    
    /**
     * 父文件夹token
     */
    private String parentToken;
    
    /**
     * 文件URL
     */
    private String url;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
    
    /**
     * 文件大小（字节）
     */
    private Long size;
    
    /**
     * 创建者ID
     */
    private String creatorId;
    
    /**
     * 创建者名称
     */
    private String creatorName;
    
    /**
     * 修改者ID
     */
    private String modifierId;
    
    /**
     * 修改者名称
     */
    private String modifierName;
    
    /**
     * 是否为文件夹
     */
    private Boolean isFolder;
    
    /**
     * 文件权限
     */
    private String permission;
    
    /**
     * 文件状态
     */
    private String status;
    
    /**
     * 文件描述
     */
    private String description;
    
    /**
     * 文件标签
     */
    private String[] tags;
    
    /**
     * 扩展信息
     */
    private Object extra;
}
