package com.lait.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Coze Token管理实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "coze_tokens")
public class CozeToken extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Token名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * Token值
     */
    @Column(nullable = false, length = 500)
    private String token;

    /**
     * Token类型 (API_KEY, OAUTH_TOKEN, etc.)
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private TokenType tokenType;

    /**
     * 应用ID
     */
    @Column(length = 100)
    private String appId;

    /**
     * 应用名称
     */
    @Column(length = 100)
    private String appName;

    /**
     * Token状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private TokenStatus status;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime expiresAt;

    /**
     * 最后使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime lastUsedAt;

    /**
     * 使用次数
     */
    @Column(nullable = false)
    private Long usageCount = 0L;

    /**
     * 每日限制次数
     */
    private Integer dailyLimit;

    /**
     * 每月限制次数
     */
    private Integer monthlyLimit;

    /**
     * 描述
     */
    @Column(length = 500)
    private String description;

    /**
     * 配置信息 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String config;

    /**
     * 是否为默认Token
     */
    @Column(nullable = false)
    private Boolean isDefault = false;

    /**
     * Token类型枚举
     */
    public enum TokenType {
        API_KEY("API密钥"),
        OAUTH_TOKEN("OAuth令牌"),
        WEBHOOK_TOKEN("Webhook令牌"),
        BOT_TOKEN("机器人令牌");

        private final String description;

        TokenType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * Token状态枚举
     */
    public enum TokenStatus {
        ACTIVE("激活"),
        INACTIVE("停用"),
        EXPIRED("已过期"),
        REVOKED("已撤销");

        private final String description;

        TokenStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
