-- 创建Coze工作流表
CREATE TABLE coze_workflows (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '工作流名称',
    description VARCHAR(500) COMMENT '工作流描述',
    workflow_id VARCHAR(100) COMMENT '工作流ID (Coze平台的工作流ID)',
    type VARCHAR(50) NOT NULL COMMENT '工作流类型',
    status VARCHAR(20) NOT NULL COMMENT '工作流状态',
    config TEXT COMMENT '工作流配置 (JSON格式)',
    input_config TEXT COMMENT '输入参数配置 (JSON格式)',
    output_config TEXT COMMENT '输出参数配置 (JSON格式)',
    token_id BIGINT COMMENT '关联的Token ID',
    creator_id BIGINT COMMENT '创建者ID',
    creator_name VARCHAR(50) COMMENT '创建者名称',
    category VARCHAR(50) COMMENT '分类',
    tags VARCHAR(200) COMMENT '标签 (逗号分隔)',
    execution_count BIGINT NOT NULL DEFAULT 0 COMMENT '执行次数',
    last_executed_at DATETIME COMMENT '最后执行时间',
    avg_execution_time BIGINT COMMENT '平均执行时间 (毫秒)',
    success_count BIGINT NOT NULL DEFAULT 0 COMMENT '成功执行次数',
    failure_count BIGINT NOT NULL DEFAULT 0 COMMENT '失败执行次数',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
    version INT NOT NULL DEFAULT 1 COMMENT '版本号',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_creator_id (creator_id),
    INDEX idx_category (category),
    INDEX idx_created_time (created_time),
    INDEX idx_execution_count (execution_count),
    INDEX idx_last_executed_at (last_executed_at),
    
    UNIQUE KEY uk_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Coze工作流表';

-- 创建Coze工作流执行记录表
CREATE TABLE coze_workflow_executions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    workflow_id BIGINT NOT NULL COMMENT '工作流ID',
    execution_id VARCHAR(100) COMMENT '执行ID (Coze平台的执行ID)',
    status VARCHAR(20) NOT NULL COMMENT '执行状态',
    input_data TEXT COMMENT '输入参数 (JSON格式)',
    output_data TEXT COMMENT '输出结果 (JSON格式)',
    error_message TEXT COMMENT '错误信息',
    start_time DATETIME COMMENT '执行开始时间',
    end_time DATETIME COMMENT '执行结束时间',
    duration BIGINT COMMENT '执行时长 (毫秒)',
    executor_id BIGINT COMMENT '执行者ID',
    executor_name VARCHAR(50) COMMENT '执行者名称',
    execution_type VARCHAR(20) NOT NULL COMMENT '执行类型',
    trigger_type VARCHAR(20) NOT NULL COMMENT '触发方式',
    remarks VARCHAR(500) COMMENT '备注',
    execution_log TEXT COMMENT '执行日志 (JSON格式)',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_status (status),
    INDEX idx_executor_id (executor_id),
    INDEX idx_start_time (start_time),
    INDEX idx_execution_type (execution_type),
    INDEX idx_trigger_type (trigger_type),
    INDEX idx_created_time (created_time),
    
    FOREIGN KEY (workflow_id) REFERENCES coze_workflows(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Coze工作流执行记录表';

-- 插入示例数据
INSERT INTO coze_workflows (
    name, description, type, status, config, input_config, output_config,
    creator_name, category, tags, is_public
) VALUES 
(
    '题目生成工作流',
    '基于学科和难度自动生成题目',
    'QUESTION_GENERATION',
    'ACTIVE',
    '{"model": "gpt-3.5-turbo", "temperature": 0.7, "max_tokens": 1000}',
    '{"fields": [{"name": "subject", "type": "string", "label": "学科", "required": true}, {"name": "difficulty", "type": "select", "label": "难度", "options": [{"value": "easy", "label": "简单"}, {"value": "medium", "label": "中等"}, {"value": "hard", "label": "困难"}], "required": true}, {"name": "count", "type": "number", "label": "题目数量", "defaultValue": 5, "required": true}]}',
    '{"fields": [{"name": "questions", "type": "array", "label": "生成的题目"}, {"name": "total", "type": "number", "label": "总数量"}]}',
    'admin',
    '教育',
    '题目生成,AI,教育',
    true
),
(
    '内容分析工作流',
    '分析学习内容和学生表现',
    'CONTENT_ANALYSIS',
    'ACTIVE',
    '{"model": "gpt-4", "temperature": 0.3, "max_tokens": 2000}',
    '{"fields": [{"name": "content", "type": "textarea", "label": "学习内容", "required": true}, {"name": "student_data", "type": "textarea", "label": "学生数据", "required": true}]}',
    '{"fields": [{"name": "analysis", "type": "object", "label": "分析结果"}, {"name": "recommendations", "type": "array", "label": "建议"}]}',
    'admin',
    '分析',
    '内容分析,学习分析,AI',
    true
),
(
    '学习指导工作流',
    '为学生提供个性化学习指导',
    'STUDY_GUIDANCE',
    'DRAFT',
    '{"model": "gpt-3.5-turbo", "temperature": 0.5, "max_tokens": 1500}',
    '{"fields": [{"name": "student_profile", "type": "object", "label": "学生档案", "required": true}, {"name": "learning_goals", "type": "array", "label": "学习目标", "required": true}]}',
    '{"fields": [{"name": "guidance", "type": "string", "label": "学习指导"}, {"name": "study_plan", "type": "object", "label": "学习计划"}]}',
    'admin',
    '指导',
    '学习指导,个性化,AI',
    false
);

-- 插入示例执行记录
INSERT INTO coze_workflow_executions (
    workflow_id, status, input_data, output_data, start_time, end_time, duration,
    executor_name, execution_type, trigger_type, remarks
) VALUES 
(
    1,
    'SUCCESS',
    '{"subject": "数学", "difficulty": "medium", "count": 5}',
    '{"questions": [{"id": 1, "content": "计算 2+3=?", "answer": "5"}, {"id": 2, "content": "求解方程 x+1=3", "answer": "x=2"}], "total": 2}',
    '2024-01-15 10:00:00',
    '2024-01-15 10:00:15',
    15000,
    'admin',
    'MANUAL',
    'USER',
    '测试执行'
),
(
    2,
    'SUCCESS',
    '{"content": "线性代数基础知识", "student_data": "学生A在矩阵运算方面较弱"}',
    '{"analysis": {"strengths": ["理论理解"], "weaknesses": ["实际应用"]}, "recommendations": ["多做练习题", "加强概念理解"]}',
    '2024-01-15 11:00:00',
    '2024-01-15 11:00:25',
    25000,
    'admin',
    'MANUAL',
    'USER',
    '内容分析测试'
),
(
    1,
    'FAILED',
    '{"subject": "物理", "difficulty": "hard", "count": 10}',
    NULL,
    '2024-01-15 12:00:00',
    '2024-01-15 12:00:30',
    30000,
    'admin',
    'MANUAL',
    'USER',
    '执行失败测试'
);

-- 更新工作流统计数据
UPDATE coze_workflows SET 
    execution_count = (SELECT COUNT(*) FROM coze_workflow_executions WHERE workflow_id = coze_workflows.id),
    success_count = (SELECT COUNT(*) FROM coze_workflow_executions WHERE workflow_id = coze_workflows.id AND status = 'SUCCESS'),
    failure_count = (SELECT COUNT(*) FROM coze_workflow_executions WHERE workflow_id = coze_workflows.id AND status = 'FAILED'),
    last_executed_at = (SELECT MAX(start_time) FROM coze_workflow_executions WHERE workflow_id = coze_workflows.id),
    avg_execution_time = (SELECT AVG(duration) FROM coze_workflow_executions WHERE workflow_id = coze_workflows.id AND duration IS NOT NULL);
