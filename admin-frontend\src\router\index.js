import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘', icon: 'Dashboard' }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/Users.vue'),
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: 'subjects',
        name: 'Subjects',
        component: () => import('@/views/Subjects.vue'),
        meta: { title: '学科管理', icon: 'Reading' }
      },
      {
        path: 'questions',
        name: 'Questions',
        component: () => import('@/views/Questions.vue'),
        meta: { title: '题目管理', icon: 'EditPen' }
      },
      {
        path: 'grades',
        name: 'Grades',
        component: () => import('@/views/Grades.vue'),
        meta: { title: '成绩管理', icon: 'TrendCharts' }
      },
      {
        path: 'wrong-questions',
        name: 'WrongQuestions',
        component: () => import('@/views/WrongQuestions.vue'),
        meta: { title: '错题管理', icon: 'Warning' }
      },
      {
        path: 'notes',
        name: 'Notes',
        component: () => import('@/views/Notes.vue'),
        meta: { title: '笔记管理', icon: 'Document' }
      },
      {
        path: 'coze-tokens',
        name: 'CozeTokens',
        component: () => import('@/views/CozeTokens.vue'),
        meta: { title: 'Coze Token管理', icon: 'Key' }
      },
      {
        path: 'coze-workflows',
        name: 'CozeWorkflows',
        component: () => import('@/views/CozeWorkflow.vue'),
        meta: { title: 'Coze工作流', icon: 'Operation' }
      },
      {
        path: 'points',
        name: 'Points',
        component: () => import('@/views/Points.vue'),
        meta: { title: '积分管理', icon: 'Coin' }
      },
      {
        path: 'feishu',
        name: 'Feishu',
        component: () => import('@/views/Feishu.vue'),
        meta: { title: '飞书文档', icon: 'Document' }
      },
      {
        path: 'feishu/view/:id',
        name: 'FeishuDocumentView',
        component: () => import('@/views/FeishuDocumentView.vue'),
        meta: { title: '查看文档', hidden: true }
      },
      {
        path: 'feishu/edit/:id',
        name: 'FeishuDocumentEdit',
        component: () => import('@/views/FeishuDocumentEdit.vue'),
        meta: { title: '编辑文档', hidden: true }
      },
      {
        path: 'feishu-api',
        name: 'FeishuApiManager',
        component: () => import('@/views/FeishuApiManager.vue'),
        meta: { title: '飞书API管理', icon: 'Connection' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
