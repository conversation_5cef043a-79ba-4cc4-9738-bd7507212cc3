package com.lait.service;

import com.lait.dto.QuestionDTO;
import com.lait.entity.Question;

import java.util.List;

/**
 * Coze AI API服务接口
 */
public interface CozeApiService {

    /**
     * 生成题目
     */
    List<QuestionDTO> generateQuestions(GenerateQuestionRequest request);

    /**
     * 分析学生答题情况
     */
    AnalysisResult analyzeStudentPerformance(AnalysisRequest request);

    /**
     * 生成学习建议
     */
    String generateStudyAdvice(StudyAdviceRequest request);

    /**
     * 解释题目答案
     */
    String explainAnswer(ExplainAnswerRequest request);

    /**
     * 生成题目请求
     */
    class GenerateQuestionRequest {
        private Long subjectId;
        private String subjectName;
        private Question.QuestionType questionType;
        private Question.DifficultyLevel difficulty;
        private Integer gradeLevel;
        private String topic;
        private Integer count;
        private String requirements;

        // Getters and Setters
        public Long getSubjectId() { return subjectId; }
        public void setSubjectId(Long subjectId) { this.subjectId = subjectId; }

        public String getSubjectName() { return subjectName; }
        public void setSubjectName(String subjectName) { this.subjectName = subjectName; }

        public Question.QuestionType getQuestionType() { return questionType; }
        public void setQuestionType(Question.QuestionType questionType) { this.questionType = questionType; }

        public Question.DifficultyLevel getDifficulty() { return difficulty; }
        public void setDifficulty(Question.DifficultyLevel difficulty) { this.difficulty = difficulty; }

        public Integer getGradeLevel() { return gradeLevel; }
        public void setGradeLevel(Integer gradeLevel) { this.gradeLevel = gradeLevel; }

        public String getTopic() { return topic; }
        public void setTopic(String topic) { this.topic = topic; }

        public Integer getCount() { return count; }
        public void setCount(Integer count) { this.count = count; }

        public String getRequirements() { return requirements; }
        public void setRequirements(String requirements) { this.requirements = requirements; }
    }

    /**
     * 分析请求
     */
    class AnalysisRequest {
        private Long studentId;
        private Long subjectId;
        private List<QuestionAnswerPair> questionAnswers;
        private String analysisType;

        // Getters and Setters
        public Long getStudentId() { return studentId; }
        public void setStudentId(Long studentId) { this.studentId = studentId; }

        public Long getSubjectId() { return subjectId; }
        public void setSubjectId(Long subjectId) { this.subjectId = subjectId; }

        public List<QuestionAnswerPair> getQuestionAnswers() { return questionAnswers; }
        public void setQuestionAnswers(List<QuestionAnswerPair> questionAnswers) { this.questionAnswers = questionAnswers; }

        public String getAnalysisType() { return analysisType; }
        public void setAnalysisType(String analysisType) { this.analysisType = analysisType; }
    }

    /**
     * 题目答案对
     */
    class QuestionAnswerPair {
        private Long questionId;
        private String questionContent;
        private String correctAnswer;
        private String studentAnswer;
        private boolean isCorrect;

        // Getters and Setters
        public Long getQuestionId() { return questionId; }
        public void setQuestionId(Long questionId) { this.questionId = questionId; }

        public String getQuestionContent() { return questionContent; }
        public void setQuestionContent(String questionContent) { this.questionContent = questionContent; }

        public String getCorrectAnswer() { return correctAnswer; }
        public void setCorrectAnswer(String correctAnswer) { this.correctAnswer = correctAnswer; }

        public String getStudentAnswer() { return studentAnswer; }
        public void setStudentAnswer(String studentAnswer) { this.studentAnswer = studentAnswer; }

        public boolean isCorrect() { return isCorrect; }
        public void setCorrect(boolean correct) { isCorrect = correct; }
    }

    /**
     * 分析结果
     */
    class AnalysisResult {
        private String overallPerformance;
        private List<String> strengths;
        private List<String> weaknesses;
        private List<String> recommendations;
        private Double accuracyRate;
        private String difficultyAssessment;

        // Getters and Setters
        public String getOverallPerformance() { return overallPerformance; }
        public void setOverallPerformance(String overallPerformance) { this.overallPerformance = overallPerformance; }

        public List<String> getStrengths() { return strengths; }
        public void setStrengths(List<String> strengths) { this.strengths = strengths; }

        public List<String> getWeaknesses() { return weaknesses; }
        public void setWeaknesses(List<String> weaknesses) { this.weaknesses = weaknesses; }

        public List<String> getRecommendations() { return recommendations; }
        public void setRecommendations(List<String> recommendations) { this.recommendations = recommendations; }

        public Double getAccuracyRate() { return accuracyRate; }
        public void setAccuracyRate(Double accuracyRate) { this.accuracyRate = accuracyRate; }

        public String getDifficultyAssessment() { return difficultyAssessment; }
        public void setDifficultyAssessment(String difficultyAssessment) { this.difficultyAssessment = difficultyAssessment; }
    }

    /**
     * 学习建议请求
     */
    class StudyAdviceRequest {
        private Long studentId;
        private Long subjectId;
        private List<String> weakTopics;
        private String currentLevel;
        private String targetLevel;
        private Integer studyTimePerDay;

        // Getters and Setters
        public Long getStudentId() { return studentId; }
        public void setStudentId(Long studentId) { this.studentId = studentId; }

        public Long getSubjectId() { return subjectId; }
        public void setSubjectId(Long subjectId) { this.subjectId = subjectId; }

        public List<String> getWeakTopics() { return weakTopics; }
        public void setWeakTopics(List<String> weakTopics) { this.weakTopics = weakTopics; }

        public String getCurrentLevel() { return currentLevel; }
        public void setCurrentLevel(String currentLevel) { this.currentLevel = currentLevel; }

        public String getTargetLevel() { return targetLevel; }
        public void setTargetLevel(String targetLevel) { this.targetLevel = targetLevel; }

        public Integer getStudyTimePerDay() { return studyTimePerDay; }
        public void setStudyTimePerDay(Integer studyTimePerDay) { this.studyTimePerDay = studyTimePerDay; }
    }

    /**
     * 解释答案请求
     */
    class ExplainAnswerRequest {
        private Long questionId;
        private String questionContent;
        private String correctAnswer;
        private String studentAnswer;
        private String explanation;

        // Getters and Setters
        public Long getQuestionId() { return questionId; }
        public void setQuestionId(Long questionId) { this.questionId = questionId; }

        public String getQuestionContent() { return questionContent; }
        public void setQuestionContent(String questionContent) { this.questionContent = questionContent; }

        public String getCorrectAnswer() { return correctAnswer; }
        public void setCorrectAnswer(String correctAnswer) { this.correctAnswer = correctAnswer; }

        public String getStudentAnswer() { return studentAnswer; }
        public void setStudentAnswer(String studentAnswer) { this.studentAnswer = studentAnswer; }

        public String getExplanation() { return explanation; }
        public void setExplanation(String explanation) { this.explanation = explanation; }
    }
}
