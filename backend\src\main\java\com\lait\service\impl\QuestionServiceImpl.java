package com.lait.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lait.dto.QuestionDTO;
import com.lait.entity.Question;
import com.lait.entity.Subject;
import com.lait.repository.QuestionRepository;
import com.lait.repository.SubjectRepository;
import com.lait.service.QuestionService;
import com.lait.service.FeishuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 题目服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuestionServiceImpl implements QuestionService {

    private final QuestionRepository questionRepository;
    private final SubjectRepository subjectRepository;
    private final ObjectMapper objectMapper;
    private final FeishuService feishuService;

    @Override
    @Transactional
    public QuestionDTO createQuestion(QuestionDTO.CreateQuestionRequest request) {
        log.info("创建题目: {}", request.getContent());

        // 验证学科是否存在
        Subject subject = subjectRepository.findById(request.getSubjectId())
                .orElseThrow(() -> new RuntimeException("学科不存在"));

        Question question = new Question();
        BeanUtils.copyProperties(request, question);

        // 处理选项JSON
        if (request.getOptions() != null && !request.getOptions().isEmpty()) {
            try {
                question.setOptions(objectMapper.writeValueAsString(request.getOptions()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException("选项格式错误", e);
            }
        }

        Question savedQuestion = questionRepository.save(question);
        return convertToDTO(savedQuestion);
    }

    @Override
    public QuestionDTO getQuestionById(Long id) {
        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("题目不存在"));
        return convertToDTO(question);
    }

    @Override
    @Transactional
    public QuestionDTO updateQuestion(Long id, QuestionDTO.UpdateQuestionRequest request) {
        log.info("更新题目信息: {}", id);

        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        BeanUtils.copyProperties(request, question, "id", "subjectId", "usageCount", "correctCount");

        // 处理选项JSON
        if (request.getOptions() != null) {
            try {
                question.setOptions(objectMapper.writeValueAsString(request.getOptions()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException("选项格式错误", e);
            }
        }

        Question savedQuestion = questionRepository.save(question);
        return convertToDTO(savedQuestion);
    }

    @Override
    @Transactional
    public void deleteQuestion(Long id) {
        log.info("删除题目: {}", id);

        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        question.setIsDeleted(true);
        questionRepository.save(question);
    }

    @Override
    public Page<QuestionDTO> getQuestions(Pageable pageable) {
        Page<Question> questions = questionRepository.findAllActive(pageable);
        return questions.map(this::convertToDTO);
    }

    @Override
    public Page<QuestionDTO> getQuestionsBySubjectId(Long subjectId, Pageable pageable) {
        Page<Question> questions = questionRepository.findBySubjectIdAndNotDeleted(subjectId, pageable);
        return questions.map(this::convertToDTO);
    }

    @Override
    public Page<QuestionDTO> searchQuestions(QuestionDTO.QuestionQueryRequest request, Pageable pageable) {
        // 根据不同条件进行查询
        if (request.getSubjectId() != null) {
            Page<Question> questions = questionRepository.findBySubjectIdAndNotDeleted(request.getSubjectId(), pageable);
            return questions.map(this::convertToDTO);
        }

        if (request.getType() != null) {
            return getQuestionsByType(request.getType(), pageable);
        }

        if (request.getDifficulty() != null) {
            return getQuestionsByDifficulty(request.getDifficulty(), pageable);
        }

        if (request.getGradeLevel() != null) {
            return getQuestionsByGradeLevel(request.getGradeLevel(), pageable);
        }

        if (StringUtils.hasText(request.getKeyword())) {
            Page<Question> questions = questionRepository.searchQuestions(request.getKeyword(), pageable);
            return questions.map(this::convertToDTO);
        }

        return getQuestions(pageable);
    }

    @Override
    public Page<QuestionDTO> getQuestionsByDifficulty(Question.DifficultyLevel difficulty, Pageable pageable) {
        Page<Question> questions = questionRepository.findByDifficultyAndNotDeleted(difficulty, pageable);
        return questions.map(this::convertToDTO);
    }

    @Override
    public Page<QuestionDTO> getQuestionsByType(Question.QuestionType type, Pageable pageable) {
        Page<Question> questions = questionRepository.findByQuestionTypeAndNotDeleted(type, pageable);
        return questions.map(this::convertToDTO);
    }

    @Override
    public Page<QuestionDTO> getQuestionsByGradeLevel(Integer gradeLevel, Pageable pageable) {
        Page<Question> questions = questionRepository.findByGradeLevelAndNotDeleted(gradeLevel, pageable);
        return questions.map(this::convertToDTO);
    }

    @Override
    public List<QuestionDTO> getRandomQuestions(Long subjectId, Question.DifficultyLevel difficulty, int count) {
        // 这里需要在Repository中添加相应的方法
        List<Question> questions = questionRepository.findBySubjectId(subjectId);
        return questions.stream()
                .filter(q -> q.getDifficulty() == difficulty)
                .limit(count)
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<QuestionDTO> batchImportQuestions(QuestionDTO.BatchImportRequest request) {
        log.info("批量导入题目，数量: {}", request.getQuestions().size());

        // 验证学科是否存在
        Subject subject = subjectRepository.findById(request.getSubjectId())
                .orElseThrow(() -> new RuntimeException("学科不存在"));

        return request.getQuestions().stream()
                .map(createRequest -> {
                    createRequest.setSubjectId(request.getSubjectId());
                    return createQuestion(createRequest);
                })
                .collect(Collectors.toList());
    }

    @Override
    public QuestionDTO.QuestionStatistics getQuestionStatistics(Long questionId) {
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        QuestionDTO.QuestionStatistics statistics = new QuestionDTO.QuestionStatistics();
        statistics.setQuestionId(questionId);
        statistics.setTitle(question.getContent());
        statistics.setUsageCount(question.getUsageCount());
        statistics.setCorrectCount(question.getCorrectCount());
        statistics.setDifficulty(question.getDifficulty());

        if (question.getUsageCount() > 0) {
            double correctRate = (double) question.getCorrectCount() / question.getUsageCount() * 100;
            statistics.setCorrectRate(correctRate);
        } else {
            statistics.setCorrectRate(0.0);
        }

        // 获取学科名称
        subjectRepository.findById(question.getSubjectId())
                .ifPresent(subject -> statistics.setSubjectName(subject.getName()));

        return statistics;
    }

    @Override
    @Transactional
    public void incrementUsageCount(Long questionId) {
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        question.setUsageCount(question.getUsageCount() + 1);
        questionRepository.save(question);
    }

    @Override
    @Transactional
    public void incrementCorrectCount(Long questionId) {
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        question.setCorrectCount(question.getCorrectCount() + 1);
        questionRepository.save(question);
    }

    @Override
    public List<QuestionDTO> getPopularQuestions(Long subjectId, int limit) {
        List<Question> questions = questionRepository.findBySubjectId(subjectId);
        return questions.stream()
                .sorted((q1, q2) -> Integer.compare(q2.getUsageCount(), q1.getUsageCount()))
                .limit(limit)
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<QuestionDTO> getMostWrongQuestions(Long subjectId, int limit) {
        List<Question> questions = questionRepository.findBySubjectId(subjectId);
        return questions.stream()
                .filter(q -> q.getUsageCount() > 0)
                .sorted((q1, q2) -> {
                    double rate1 = (double) q1.getCorrectCount() / q1.getUsageCount();
                    double rate2 = (double) q2.getCorrectCount() / q2.getUsageCount();
                    return Double.compare(rate1, rate2);
                })
                .limit(limit)
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<QuestionDTO> getQuestionsByTags(String tags, Pageable pageable) {
        // 这里需要在Repository中添加相应的方法
        return Page.empty(pageable);
    }

    @Override
    @Transactional
    public QuestionDTO duplicateQuestion(Long questionId) {
        Question originalQuestion = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        Question newQuestion = new Question();
        BeanUtils.copyProperties(originalQuestion, newQuestion, "id", "usageCount", "correctCount");
        newQuestion.setContent(originalQuestion.getContent() + " (副本)");

        Question savedQuestion = questionRepository.save(newQuestion);
        return convertToDTO(savedQuestion);
    }

    @Override
    @Transactional
    public void batchDeleteQuestions(Long[] questionIds) {
        for (Long questionId : questionIds) {
            deleteQuestion(questionId);
        }
    }

    @Override
    @Transactional
    public List<QuestionDTO> importQuestionsFromFeishuDocument(String docId, Long subjectId) {
        log.info("从飞书文档导入题目: docId={}, subjectId={}", docId, subjectId);

        try {
            // 获取飞书文档内容
            String content = feishuService.getDocumentContentByToken(docId);
            if (content == null || content.trim().isEmpty()) {
                throw new RuntimeException("无法获取文档内容或文档为空");
            }

            // 解析文档内容为题目
            List<QuestionDTO> questions = parseFeishuDocumentContent(content, subjectId);

            // 保存题目到数据库
            List<QuestionDTO> savedQuestions = new ArrayList<>();
            for (QuestionDTO questionDTO : questions) {
                try {
                    QuestionDTO.CreateQuestionRequest request = new QuestionDTO.CreateQuestionRequest();
                    request.setSubjectId(subjectId);
                    request.setTitle(questionDTO.getContent().length() > 50 ?
                                   questionDTO.getContent().substring(0, 50) + "..." :
                                   questionDTO.getContent());
                    request.setContent(questionDTO.getContent());
                    request.setType(questionDTO.getType());
                    request.setCorrectAnswer(questionDTO.getCorrectAnswer());
                    request.setExplanation(questionDTO.getExplanation());
                    request.setDifficulty(questionDTO.getDifficulty());
                    request.setGradeLevel(questionDTO.getGradeLevel());
                    request.setTags(questionDTO.getTags());

                    // 处理选项
                    if (questionDTO.getOptions() != null) {
                        try {
                            // 将选项字符串转换为JSON格式
                            String[] optionsArray = questionDTO.getOptions().split("\\|");
                            request.setOptions(objectMapper.writeValueAsString(optionsArray));
                        } catch (Exception e) {
                            log.warn("解析题目选项失败: {}", e.getMessage());
                        }
                    }

                    QuestionDTO savedQuestion = createQuestion(request);
                    savedQuestions.add(savedQuestion);
                } catch (Exception e) {
                    log.error("保存题目失败: {}", e.getMessage(), e);
                }
            }

            log.info("成功从飞书文档导入 {} 道题目", savedQuestions.size());
            return savedQuestions;

        } catch (Exception e) {
            log.error("从飞书文档导入题目失败", e);
            throw new RuntimeException("导入题目失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<QuestionDTO> parseFeishuDocumentContent(String content, Long subjectId) {
        log.info("解析飞书文档内容为题目，内容长度: {}", content.length());

        List<QuestionDTO> questions = new ArrayList<>();

        try {
            // 定义题目解析的正则表达式模式
            // 支持多种格式：
            // 1. 题目：选项A、B、C、D，答案：A，解析：...
            // 2. 1. 题目内容 A.选项1 B.选项2 C.选项3 D.选项4 答案：A
            // 3. 【题目】内容 【选项】A.xxx B.xxx 【答案】A 【解析】xxx

            // 按行分割内容
            String[] lines = content.split("\n");

            QuestionDTO currentQuestion = null;
            StringBuilder questionContent = new StringBuilder();
            List<String> options = new ArrayList<>();
            String correctAnswer = "";
            String explanation = "";
            Question.QuestionType questionType = Question.QuestionType.SINGLE_CHOICE;
            Question.DifficultyLevel difficulty = Question.DifficultyLevel.MEDIUM;

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // 检测题目开始的模式
                if (isQuestionStart(line)) {
                    // 保存上一道题目
                    if (currentQuestion != null) {
                        currentQuestion = finalizeQuestion(currentQuestion, questionContent.toString(),
                                                         options, correctAnswer, explanation, questionType, difficulty, subjectId);
                        if (currentQuestion != null) {
                            questions.add(currentQuestion);
                        }
                    }

                    // 开始新题目
                    currentQuestion = new QuestionDTO();
                    questionContent = new StringBuilder();
                    options = new ArrayList<>();
                    correctAnswer = "";
                    explanation = "";

                    // 提取题目内容
                    String extractedContent = extractQuestionContent(line);
                    questionContent.append(extractedContent);

                } else if (isOption(line)) {
                    // 解析选项
                    String option = extractOption(line);
                    if (!option.isEmpty()) {
                        options.add(option);
                    }

                } else if (isAnswer(line)) {
                    // 解析答案
                    correctAnswer = extractAnswer(line);

                } else if (isExplanation(line)) {
                    // 解析解析
                    explanation = extractExplanation(line);

                } else if (currentQuestion != null && questionContent.length() > 0) {
                    // 继续题目内容
                    if (questionContent.length() > 0) {
                        questionContent.append(" ");
                    }
                    questionContent.append(line);
                }
            }

            // 处理最后一道题目
            if (currentQuestion != null) {
                currentQuestion = finalizeQuestion(currentQuestion, questionContent.toString(),
                                                 options, correctAnswer, explanation, questionType, difficulty, subjectId);
                if (currentQuestion != null) {
                    questions.add(currentQuestion);
                }
            }

        } catch (Exception e) {
            log.error("解析飞书文档内容失败", e);
        }

        log.info("解析完成，共提取 {} 道题目", questions.size());
        return questions;
    }

    /**
     * 转换为DTO
     */
    private QuestionDTO convertToDTO(Question question) {
        QuestionDTO dto = new QuestionDTO();
        BeanUtils.copyProperties(question, dto);

        // 设置学科名称
        subjectRepository.findById(question.getSubjectId())
                .ifPresent(subject -> dto.setSubjectName(subject.getName()));

        // 处理选项JSON
        if (StringUtils.hasText(question.getOptions())) {
            try {
                dto.setOptions(question.getOptions());
            } catch (Exception e) {
                log.warn("解析题目选项失败: {}", e.getMessage());
            }
        }

        return dto;
    }

    // ========== 飞书文档解析辅助方法 ==========

    /**
     * 判断是否为题目开始行
     */
    private boolean isQuestionStart(String line) {
        // 匹配模式：数字开头、题目：、【题目】等
        return line.matches("^\\d+[.、].*") ||
               line.startsWith("题目：") ||
               line.startsWith("【题目】") ||
               line.matches("^\\d+\\s*、.*") ||
               line.matches("^第\\d+题.*");
    }

    /**
     * 判断是否为选项行
     */
    private boolean isOption(String line) {
        // 匹配模式：A. B. C. D. 或 A、B、C、D、
        return line.matches("^[A-Z][.、].*") ||
               line.matches("^[（(][A-Z][)）].*");
    }

    /**
     * 判断是否为答案行
     */
    private boolean isAnswer(String line) {
        return line.startsWith("答案：") ||
               line.startsWith("【答案】") ||
               line.startsWith("正确答案：") ||
               line.matches(".*答案[：:].*");
    }

    /**
     * 判断是否为解析行
     */
    private boolean isExplanation(String line) {
        return line.startsWith("解析：") ||
               line.startsWith("【解析】") ||
               line.startsWith("解释：") ||
               line.startsWith("说明：");
    }

    /**
     * 提取题目内容
     */
    private String extractQuestionContent(String line) {
        // 去除题目标识符
        String content = line;
        content = content.replaceFirst("^\\d+[.、]\\s*", "");
        content = content.replaceFirst("^题目：", "");
        content = content.replaceFirst("^【题目】", "");
        content = content.replaceFirst("^第\\d+题[：:]?\\s*", "");
        return content.trim();
    }

    /**
     * 提取选项内容
     */
    private String extractOption(String line) {
        // 去除选项标识符，保留选项内容
        String option = line;
        option = option.replaceFirst("^[A-Z][.、]\\s*", "");
        option = option.replaceFirst("^[（(][A-Z][)）]\\s*", "");
        return option.trim();
    }

    /**
     * 提取答案
     */
    private String extractAnswer(String line) {
        String answer = line;
        answer = answer.replaceFirst(".*答案[：:]\\s*", "");
        answer = answer.replaceFirst("^【答案】", "");
        answer = answer.replaceFirst("^正确答案：", "");
        // 提取第一个字母作为答案
        Pattern pattern = Pattern.compile("[A-Z]");
        Matcher matcher = pattern.matcher(answer);
        if (matcher.find()) {
            return matcher.group();
        }
        return answer.trim();
    }

    /**
     * 提取解析内容
     */
    private String extractExplanation(String line) {
        String explanation = line;
        explanation = explanation.replaceFirst("^解析：", "");
        explanation = explanation.replaceFirst("^【解析】", "");
        explanation = explanation.replaceFirst("^解释：", "");
        explanation = explanation.replaceFirst("^说明：", "");
        return explanation.trim();
    }

    /**
     * 完成题目构建
     */
    private QuestionDTO finalizeQuestion(QuestionDTO question, String content, List<String> options,
                                       String correctAnswer, String explanation,
                                       Question.QuestionType questionType, Question.DifficultyLevel difficulty,
                                       Long subjectId) {

        if (content.trim().isEmpty()) {
            return null;
        }

        question.setContent(content.trim());
        question.setType(questionType);
        question.setCorrectAnswer(correctAnswer);
        question.setExplanation(explanation);
        question.setDifficulty(difficulty);
        question.setGradeLevel(9); // 默认年级
        question.setSubjectId(subjectId);

        // 处理选项
        if (!options.isEmpty()) {
            question.setOptions(String.join("|", options));
        }

        // 根据选项数量判断题目类型
        if (options.size() >= 2) {
            question.setType(Question.QuestionType.SINGLE_CHOICE);
        } else if (content.contains("判断") || content.contains("对错") || content.contains("正确") || content.contains("错误")) {
            question.setType(Question.QuestionType.TRUE_FALSE);
        } else if (content.contains("填空") || content.contains("_____")) {
            question.setType(Question.QuestionType.FILL_BLANK);
        } else if (content.contains("简答") || content.contains("论述")) {
            question.setType(Question.QuestionType.SHORT_ANSWER);
        }

        return question;
    }
}
