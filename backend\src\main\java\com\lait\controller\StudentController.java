package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.dto.GradeDTO;
import com.lait.dto.NoteDTO;
import com.lait.dto.QuestionDTO;
import com.lait.entity.Question;
import com.lait.security.UserPrincipal;
import com.lait.service.GradeService;
import com.lait.service.NoteService;
import com.lait.service.QuestionService;
import com.lait.service.StudyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学生端API控制器
 */
@Slf4j
@RestController
@RequestMapping("/student")
@RequiredArgsConstructor
public class StudentController {

    private final QuestionService questionService;
    private final GradeService gradeService;
    private final NoteService noteService;
    private final StudyService studyService;

    // ========== 题目相关 ==========

    /**
     * 获取练习题目
     */
    @GetMapping("/questions/practice")
    public ResponseEntity<ApiResponse<Page<QuestionDTO>>> getPracticeQuestions(
            @RequestParam(required = false) Long subjectId,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String difficulty,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(0, size);

        Page<QuestionDTO> questions;
        if (subjectId != null) {
            questions = questionService.getQuestionsBySubjectId(subjectId, pageable);
        } else {
            questions = questionService.getQuestions(pageable);
        }

        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 获取随机题目
     */
    @GetMapping("/questions/random")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> getRandomQuestions(
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String difficulty,
            @RequestParam(required = false) Long subjectId,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        try {
            // 解析难度等级
            Question.DifficultyLevel difficultyLevel = null;
            if (difficulty != null && !difficulty.isEmpty()) {
                try {
                    difficultyLevel = Question.DifficultyLevel.valueOf(difficulty.toUpperCase());
                } catch (IllegalArgumentException e) {
                    log.warn("无效的难度等级: {}", difficulty);
                }
            }

            // 获取随机题目
            List<QuestionDTO> questions = questionService.getRandomQuestions(subjectId, difficultyLevel, size);
            return ResponseEntity.ok(ApiResponse.success(questions));
        } catch (Exception e) {
            log.error("获取随机题目失败", e);
            // 降级处理：获取普通题目列表
            Pageable pageable = PageRequest.of(0, size);
            Page<QuestionDTO> questionsPage = questionService.getQuestions(pageable);
            return ResponseEntity.ok(ApiResponse.success(questionsPage.getContent()));
        }
    }

    /**
     * 提交答案
     */
    @PostMapping("/questions/submit")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitAnswer(
            @Valid @RequestBody Map<String, Object> request,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Long questionId = Long.valueOf(request.get("questionId").toString());
        String answer = request.get("answer").toString();

        try {
            QuestionDTO question = questionService.getQuestionById(questionId);
            boolean isCorrect = answer.equals(question.getCorrectAnswer());

            Map<String, Object> result = new HashMap<>();
            result.put("isCorrect", isCorrect);
            result.put("correctAnswer", question.getCorrectAnswer());
            result.put("explanation", question.getExplanation());

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("提交答案失败", e);
            return ResponseEntity.ok(ApiResponse.error("提交答案失败"));
        }
    }

    // ========== 成绩相关 ==========

    /**
     * 获取我的成绩列表
     */
    @GetMapping("/grades/my")
    public ResponseEntity<ApiResponse<Page<GradeDTO>>> getMyGrades(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());

        Page<GradeDTO> grades = gradeService.getGradesByStudentId(userPrincipal.getId(), pageable);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }

    /**
     * 获取我的成绩统计
     */
    @GetMapping("/grades/my/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getMyGradeStats(
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        try {
            Map<String, Object> stats = gradeService.getStudentStatistics(userPrincipal.getId());
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取成绩统计失败", e);
            // 返回默认统计数据
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("totalExams", 0);
            defaultStats.put("averageScore", 0.0);
            defaultStats.put("highestScore", 0);
            defaultStats.put("lowestScore", 0);
            defaultStats.put("passRate", 0.0);
            return ResponseEntity.ok(ApiResponse.success(defaultStats));
        }
    }

    /**
     * 根据学科获取成绩
     */
    @GetMapping("/grades/subject/{subjectId}")
    public ResponseEntity<ApiResponse<List<GradeDTO>>> getGradesBySubject(
            @PathVariable Long subjectId,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        List<GradeDTO> grades = gradeService.getStudentSubjectGrades(userPrincipal.getId(), subjectId);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }

    // ========== 笔记相关 ==========

    /**
     * 获取我的笔记列表
     */
    @GetMapping("/notes/my")
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> getMyNotes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());

        Page<NoteDTO> notes = noteService.getNotesByStudentId(userPrincipal.getId(), pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 根据学科获取我的笔记
     */
    @GetMapping("/notes/my/subject/{subjectId}")
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> getMyNotesBySubject(
            @PathVariable Long subjectId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());

        Page<NoteDTO> notes = noteService.getNotesByStudentAndSubject(userPrincipal.getId(), subjectId, pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    // ========== 飞书文档题目导入 ==========

    /**
     * 从飞书文档导入题目
     */
    @PostMapping("/questions/import-from-feishu")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> importQuestionsFromFeishu(
            @Valid @RequestBody Map<String, Object> request,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        String docId = request.get("docId").toString();
        Long subjectId = Long.valueOf(request.get("subjectId").toString());

        try {
            List<QuestionDTO> questions = questionService.importQuestionsFromFeishuDocument(docId, subjectId);
            return ResponseEntity.ok(ApiResponse.success(questions));
        } catch (Exception e) {
            log.error("从飞书文档导入题目失败", e);
            return ResponseEntity.ok(ApiResponse.error("导入题目失败: " + e.getMessage()));
        }
    }

    /**
     * 解析飞书文档内容预览题目
     */
    @PostMapping("/questions/preview-feishu")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> previewFeishuQuestions(
            @Valid @RequestBody Map<String, Object> request,
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        String content = request.get("content").toString();
        Long subjectId = Long.valueOf(request.get("subjectId").toString());

        try {
            List<QuestionDTO> questions = questionService.parseFeishuDocumentContent(content, subjectId);
            return ResponseEntity.ok(ApiResponse.success(questions));
        } catch (Exception e) {
            log.error("解析飞书文档内容失败", e);
            return ResponseEntity.ok(ApiResponse.error("解析失败: " + e.getMessage()));
        }
    }

    // ========== 学习统计 ==========

    /**
     * 获取学习统计数据
     */
    @GetMapping("/study/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStudyStats(
            Authentication authentication) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        try {
            Map<String, Object> stats = studyService.getStudentStudyStatistics(userPrincipal.getId());
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取学习统计失败", e);
            // 返回默认统计数据
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("totalStudyTime", 0);
            defaultStats.put("questionsCompleted", 0);
            defaultStats.put("correctRate", 0.0);
            defaultStats.put("studyDays", 0);
            defaultStats.put("notesCount", 0);
            return ResponseEntity.ok(ApiResponse.success(defaultStats));
        }
    }
}
