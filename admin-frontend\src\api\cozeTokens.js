import request from './request'

// 获取Coze Token列表
export function getCozeTokens(params) {
  return request({
    url: '/admin/coze-tokens',
    method: 'get',
    params
  })
}

// 获取Coze Token详情
export function getCozeToken(id) {
  return request({
    url: `/admin/coze-tokens/${id}`,
    method: 'get'
  })
}

// 创建Coze Token
export function createCozeToken(data) {
  return request({
    url: '/admin/coze-tokens',
    method: 'post',
    data
  })
}

// 更新Coze Token
export function updateCozeToken(id, data) {
  return request({
    url: `/admin/coze-tokens/${id}`,
    method: 'put',
    data
  })
}

// 删除Coze Token
export function deleteCozeToken(id) {
  return request({
    url: `/admin/coze-tokens/${id}`,
    method: 'delete'
  })
}

// 获取所有激活的Token
export function getActiveCozeTokens() {
  return request({
    url: '/admin/coze-tokens/active',
    method: 'get'
  })
}

// 获取默认Token
export function getDefaultCozeToken() {
  return request({
    url: '/admin/coze-tokens/default',
    method: 'get'
  })
}

// 设置默认Token
export function setDefaultCozeToken(id) {
  return request({
    url: `/admin/coze-tokens/${id}/set-default`,
    method: 'put'
  })
}

// 验证Token
export function validateCozeToken(token) {
  return request({
    url: '/admin/coze-tokens/validate',
    method: 'post',
    data: { token }
  })
}

// 刷新Token状态
export function refreshCozeTokenStatus(id) {
  return request({
    url: `/admin/coze-tokens/${id}/refresh-status`,
    method: 'put'
  })
}

// 测试Token连接
export function testCozeTokenConnection(id) {
  return request({
    url: `/admin/coze-tokens/${id}/test`,
    method: 'post'
  })
}

// 获取Token统计信息
export function getCozeTokenStatistics() {
  return request({
    url: '/admin/coze-tokens/statistics',
    method: 'get'
  })
}

// 获取即将过期的Token
export function getExpiringCozeTokens(days = 7) {
  return request({
    url: '/admin/coze-tokens/expiring',
    method: 'get',
    params: { days }
  })
}

// 批量更新Token状态
export function batchUpdateCozeTokenStatus(ids, status) {
  return request({
    url: '/admin/coze-tokens/batch-update-status',
    method: 'put',
    data: { ids, status }
  })
}

// 导出Token配置
export function exportCozeTokenConfig(ids) {
  return request({
    url: '/admin/coze-tokens/export',
    method: 'post',
    data: { ids }
  })
}

// 导入Token配置
export function importCozeTokenConfig(config) {
  return request({
    url: '/admin/coze-tokens/import',
    method: 'post',
    data: { config }
  })
}
