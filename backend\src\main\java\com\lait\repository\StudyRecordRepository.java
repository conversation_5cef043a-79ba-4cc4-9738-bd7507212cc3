package com.lait.repository;

import com.lait.entity.StudyRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 学习记录数据访问层
 */
@Repository
public interface StudyRecordRepository extends JpaRepository<StudyRecord, Long> {

    /**
     * 根据用户ID查找学习记录
     */
    List<StudyRecord> findByUserIdOrderByStudyDateDesc(Long userId);

    /**
     * 分页查询用户学习记录
     */
    Page<StudyRecord> findByUserIdOrderByStudyDateDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和学科ID查找记录
     */
    List<StudyRecord> findByUserIdAndSubjectIdOrderByStudyDateDesc(Long userId, Long subjectId);

    /**
     * 根据记录类型查找
     */
    List<StudyRecord> findByRecordType(StudyRecord.RecordType recordType);

    /**
     * 查找用户错题记录
     */
    List<StudyRecord> findByUserIdAndIsCorrectFalseOrderByStudyDateDesc(Long userId);

    /**
     * 分页查询学习记录
     */
    @Query("SELECT sr FROM StudyRecord sr WHERE " +
           "(:userId IS NULL OR sr.userId = :userId) AND " +
           "(:subjectId IS NULL OR sr.subjectId = :subjectId) AND " +
           "(:recordType IS NULL OR sr.recordType = :recordType) AND " +
           "(:isCorrect IS NULL OR sr.isCorrect = :isCorrect) AND " +
           "(:startDate IS NULL OR sr.studyDate >= :startDate) AND " +
           "(:endDate IS NULL OR sr.studyDate <= :endDate) " +
           "ORDER BY sr.studyDate DESC")
    Page<StudyRecord> findRecordsWithFilters(@Param("userId") Long userId,
                                            @Param("subjectId") Long subjectId,
                                            @Param("recordType") StudyRecord.RecordType recordType,
                                            @Param("isCorrect") Boolean isCorrect,
                                            @Param("startDate") LocalDateTime startDate,
                                            @Param("endDate") LocalDateTime endDate,
                                            Pageable pageable);

    /**
     * 统计用户学习情况
     */
    @Query("SELECT COUNT(sr), SUM(CASE WHEN sr.isCorrect = true THEN 1 ELSE 0 END), " +
           "AVG(sr.timeSpent), SUM(sr.timeSpent) " +
           "FROM StudyRecord sr WHERE sr.userId = :userId")
    Object[] getStudyStatsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户在指定时间范围内的学习情况
     */
    @Query("SELECT COUNT(sr), SUM(CASE WHEN sr.isCorrect = true THEN 1 ELSE 0 END), " +
           "AVG(sr.timeSpent), SUM(sr.timeSpent) " +
           "FROM StudyRecord sr WHERE sr.userId = :userId AND sr.studyDate BETWEEN :startDate AND :endDate")
    Object[] getStudyStatsByUserIdAndDateRange(@Param("userId") Long userId,
                                              @Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate);

    /**
     * 统计用户每日学习情况
     */
    @Query("SELECT DATE(sr.studyDate), COUNT(sr), SUM(CASE WHEN sr.isCorrect = true THEN 1 ELSE 0 END), " +
           "SUM(sr.timeSpent) " +
           "FROM StudyRecord sr WHERE sr.userId = :userId AND sr.studyDate >= :startDate " +
           "GROUP BY DATE(sr.studyDate) ORDER BY DATE(sr.studyDate)")
    List<Object[]> getDailyStudyStats(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);

    /**
     * 统计各学科学习情况
     */
    @Query("SELECT s.name, COUNT(sr), SUM(CASE WHEN sr.isCorrect = true THEN 1 ELSE 0 END), " +
           "AVG(sr.timeSpent) " +
           "FROM StudyRecord sr LEFT JOIN sr.subject s WHERE sr.userId = :userId " +
           "GROUP BY s.name")
    List<Object[]> getSubjectStudyStats(@Param("userId") Long userId);

    /**
     * 查找用户最近的错题
     */
    @Query("SELECT sr FROM StudyRecord sr WHERE sr.userId = :userId AND sr.isCorrect = false " +
           "ORDER BY sr.studyDate DESC")
    List<StudyRecord> findRecentWrongAnswers(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找需要复习的题目
     */
    @Query("SELECT sr FROM StudyRecord sr WHERE sr.userId = :userId AND sr.isCorrect = false " +
           "AND sr.studyDate < :threshold ORDER BY sr.studyDate ASC")
    List<StudyRecord> findQuestionsForReview(@Param("userId") Long userId, 
                                            @Param("threshold") LocalDateTime threshold);

    /**
     * 统计用户连续学习天数
     */
    @Query(value = "SELECT COUNT(DISTINCT DATE(study_date)) FROM study_records " +
                   "WHERE user_id = :userId AND study_date >= :startDate", nativeQuery = true)
    Integer countStudyDays(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);

    /**
     * 查找学习活跃用户
     */
    @Query("SELECT sr.userId, COUNT(sr) as recordCount FROM StudyRecord sr " +
           "WHERE sr.studyDate >= :startDate " +
           "GROUP BY sr.userId ORDER BY recordCount DESC")
    List<Object[]> findActiveUsers(@Param("startDate") LocalDateTime startDate, Pageable pageable);

    /**
     * 统计各难度级别的答题情况
     */
    @Query("SELECT sr.difficultyLevel, COUNT(sr), SUM(CASE WHEN sr.isCorrect = true THEN 1 ELSE 0 END) " +
           "FROM StudyRecord sr WHERE sr.userId = :userId " +
           "GROUP BY sr.difficultyLevel")
    List<Object[]> getDifficultyStats(@Param("userId") Long userId);
}
