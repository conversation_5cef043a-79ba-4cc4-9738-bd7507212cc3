package com.lait.service.impl;

import com.lait.dto.GradeDTO;
import com.lait.entity.Grade;
import com.lait.entity.Subject;
import com.lait.entity.User;
import com.lait.repository.GradeRepository;
import com.lait.repository.SubjectRepository;
import com.lait.repository.UserRepository;
import com.lait.service.GradeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 成绩服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GradeServiceImpl implements GradeService {

    private final GradeRepository gradeRepository;
    private final SubjectRepository subjectRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public GradeDTO createGrade(GradeDTO.CreateGradeRequest request) {
        log.info("创建成绩记录: 学生ID={}, 学科ID={}, 分数={}",
                request.getStudentId(), request.getSubjectId(), request.getScore());

        // 验证学生是否存在
        User student = userRepository.findById(request.getStudentId())
                .orElseThrow(() -> new RuntimeException("学生不存在"));

        // 验证学科是否存在
        Subject subject = subjectRepository.findById(request.getSubjectId())
                .orElseThrow(() -> new RuntimeException("学科不存在"));

        Grade grade = new Grade();
        BeanUtils.copyProperties(request, grade);

        Grade savedGrade = gradeRepository.save(grade);
        return convertToDTO(savedGrade);
    }

    @Override
    public GradeDTO getGradeById(Long id) {
        Grade grade = gradeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("成绩记录不存在"));
        return convertToDTO(grade);
    }

    @Override
    @Transactional
    public GradeDTO updateGrade(Long id, GradeDTO.UpdateGradeRequest request) {
        log.info("更新成绩记录: {}", id);

        Grade grade = gradeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("成绩记录不存在"));

        BeanUtils.copyProperties(request, grade, "id", "studentId", "subjectId");
        Grade savedGrade = gradeRepository.save(grade);
        return convertToDTO(savedGrade);
    }

    @Override
    @Transactional
    public void deleteGrade(Long id) {
        log.info("删除成绩记录: {}", id);

        Grade grade = gradeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("成绩记录不存在"));

        grade.setIsDeleted(true);
        gradeRepository.save(grade);
    }

    @Override
    public Page<GradeDTO> getGrades(Pageable pageable) {
        Page<Grade> grades = gradeRepository.findAllActive(pageable);
        return grades.map(this::convertToDTO);
    }

    @Override
    public Page<GradeDTO> getGradesByStudentId(Long studentId, Pageable pageable) {
        Page<Grade> grades = gradeRepository.findByStudentIdAndNotDeleted(studentId, pageable);
        return grades.map(this::convertToDTO);
    }

    @Override
    public Page<GradeDTO> getGradesBySubjectId(Long subjectId, Pageable pageable) {
        Page<Grade> grades = gradeRepository.findBySubjectIdAndNotDeleted(subjectId, pageable);
        return grades.map(this::convertToDTO);
    }

    @Override
    public Page<GradeDTO> searchGrades(GradeDTO.GradeQueryRequest request, Pageable pageable) {
        // 这里可以根据request的条件构建复杂查询
        // 简化处理，实际应该在Repository中实现复杂查询
        if (request.getStudentId() != null) {
            return getGradesByStudentId(request.getStudentId(), pageable);
        } else if (request.getSubjectId() != null) {
            return getGradesBySubjectId(request.getSubjectId(), pageable);
        }
        return getGrades(pageable);
    }

    @Override
    public List<GradeDTO> getStudentSubjectGrades(Long studentId, Long subjectId) {
        List<Grade> grades = gradeRepository.findByStudentIdAndSubjectId(studentId, subjectId);
        return grades.stream()
                .filter(grade -> !grade.getIsDeleted())
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public GradeDTO.GradeStatistics getStudentGradeStatistics(Long studentId, Long subjectId) {
        User student = userRepository.findById(studentId)
                .orElseThrow(() -> new RuntimeException("学生不存在"));

        Subject subject = subjectRepository.findById(subjectId)
                .orElseThrow(() -> new RuntimeException("学科不存在"));

        List<Grade> grades = gradeRepository.findByStudentIdAndSubjectId(studentId, subjectId)
                .stream()
                .filter(grade -> !grade.getIsDeleted() && grade.getStatus() == Grade.GradeStatus.PUBLISHED)
                .collect(Collectors.toList());

        GradeDTO.GradeStatistics statistics = new GradeDTO.GradeStatistics();
        statistics.setStudentId(studentId);
        statistics.setStudentName(student.getRealName());
        statistics.setSubjectId(subjectId);
        statistics.setSubjectName(subject.getName());
        statistics.setTotalExams(grades.size());

        if (!grades.isEmpty()) {
            // 计算平均分
            BigDecimal totalScore = grades.stream()
                    .map(Grade::getScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.setAverageScore(totalScore.divide(BigDecimal.valueOf(grades.size()), 2, BigDecimal.ROUND_HALF_UP));

            // 计算最高分和最低分
            statistics.setHighestScore(grades.stream()
                    .map(Grade::getScore)
                    .max(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO));

            statistics.setLowestScore(grades.stream()
                    .map(Grade::getScore)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO));

            // 计算进步率（简化计算：最新成绩与第一次成绩的比较）
            if (grades.size() >= 2) {
                Grade firstGrade = grades.get(0);
                Grade lastGrade = grades.get(grades.size() - 1);
                if (firstGrade.getScore().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal improvement = lastGrade.getScore().subtract(firstGrade.getScore());
                    double improvementRate = improvement.divide(firstGrade.getScore(), 4, BigDecimal.ROUND_HALF_UP)
                            .multiply(BigDecimal.valueOf(100)).doubleValue();
                    statistics.setImprovementRate(improvementRate);
                }
            }
        } else {
            statistics.setAverageScore(BigDecimal.ZERO);
            statistics.setHighestScore(BigDecimal.ZERO);
            statistics.setLowestScore(BigDecimal.ZERO);
            statistics.setImprovementRate(0.0);
        }

        return statistics;
    }

    @Override
    public List<GradeDTO.GradeStatistics> getClassGradeStatistics(Integer gradeLevel, Long subjectId) {
        // 获取指定年级的所有学生
        List<User> students = userRepository.findByRole(User.UserRole.STUDENT);

        return students.stream()
                .map(student -> getStudentGradeStatistics(student.getId(), subjectId))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void publishGrade(Long id) {
        Grade grade = gradeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("成绩记录不存在"));

        grade.setStatus(Grade.GradeStatus.PUBLISHED);
        gradeRepository.save(grade);
    }

    @Override
    @Transactional
    public void batchPublishGrades(Long[] gradeIds) {
        for (Long gradeId : gradeIds) {
            publishGrade(gradeId);
        }
    }

    @Override
    @Transactional
    public void archiveGrade(Long id) {
        Grade grade = gradeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("成绩记录不存在"));

        grade.setStatus(Grade.GradeStatus.ARCHIVED);
        gradeRepository.save(grade);
    }

    @Override
    public List<GradeDTO> getStudentLatestGrades(Long studentId, int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdTime"));
        Page<Grade> grades = gradeRepository.findByStudentIdAndNotDeleted(studentId, pageable);
        return grades.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Double calculateStudentAverageScore(Long studentId, Long subjectId) {
        Double averageScore = gradeRepository.getAverageScoreByStudent(studentId);
        return averageScore != null ? averageScore : 0.0;
    }

    @Override
    public List<GradeDTO> getGradeTrend(Long studentId, Long subjectId, int months) {
        // 获取指定月份内的成绩数据
        LocalDateTime startDate = LocalDateTime.now().minusMonths(months);
        List<Grade> grades = gradeRepository.findByStudentIdAndSubjectId(studentId, subjectId)
                .stream()
                .filter(grade -> !grade.getIsDeleted() &&
                        grade.getCreatedTime().isAfter(startDate) &&
                        grade.getStatus() == Grade.GradeStatus.PUBLISHED)
                .sorted((g1, g2) -> g1.getCreatedTime().compareTo(g2.getCreatedTime()))
                .collect(Collectors.toList());

        return grades.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getStudentStatistics(Long studentId) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取学生所有成绩
            List<Grade> allGrades = gradeRepository.findByStudentIdAndNotDeleted(studentId,
                    PageRequest.of(0, Integer.MAX_VALUE)).getContent()
                    .stream()
                    .filter(grade -> grade.getStatus() == Grade.GradeStatus.PUBLISHED)
                    .collect(Collectors.toList());

            stats.put("totalExams", allGrades.size());

            if (!allGrades.isEmpty()) {
                // 计算平均分
                BigDecimal totalScore = allGrades.stream()
                        .map(Grade::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                double averageScore = totalScore.divide(BigDecimal.valueOf(allGrades.size()), 2, BigDecimal.ROUND_HALF_UP).doubleValue();
                stats.put("averageScore", averageScore);

                // 最高分
                BigDecimal highestScore = allGrades.stream()
                        .map(Grade::getScore)
                        .max(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);
                stats.put("highestScore", highestScore.intValue());

                // 最低分
                BigDecimal lowestScore = allGrades.stream()
                        .map(Grade::getScore)
                        .min(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);
                stats.put("lowestScore", lowestScore.intValue());

                // 及格率（假设60分及格）
                long passCount = allGrades.stream()
                        .mapToInt(grade -> grade.getScore().intValue())
                        .filter(score -> score >= 60)
                        .count();
                double passRate = (double) passCount / allGrades.size() * 100;
                stats.put("passRate", Math.round(passRate * 100.0) / 100.0);
            } else {
                stats.put("averageScore", 0.0);
                stats.put("highestScore", 0);
                stats.put("lowestScore", 0);
                stats.put("passRate", 0.0);
            }

        } catch (Exception e) {
            log.error("获取学生统计信息失败", e);
            stats.put("totalExams", 0);
            stats.put("averageScore", 0.0);
            stats.put("highestScore", 0);
            stats.put("lowestScore", 0);
            stats.put("passRate", 0.0);
        }

        return stats;
    }

    /**
     * 转换为DTO
     */
    private GradeDTO convertToDTO(Grade grade) {
        GradeDTO dto = new GradeDTO();
        BeanUtils.copyProperties(grade, dto);

        // 设置学生名称
        userRepository.findById(grade.getStudentId())
                .ifPresent(student -> dto.setStudentName(student.getRealName()));

        // 设置学科名称
        subjectRepository.findById(grade.getSubjectId())
                .ifPresent(subject -> dto.setSubjectName(subject.getName()));

        return dto;
    }
}
