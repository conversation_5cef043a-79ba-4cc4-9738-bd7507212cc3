import request from './request'

// 获取学习统计数据
export function getStudyStats() {
  return request({
    url: '/student/study/stats',
    method: 'get'
  })
}

// 获取学习进度
export function getStudyProgress(params) {
  return request({
    url: '/student/study/progress',
    method: 'get',
    params
  })
}

// 记录学习行为
export function recordStudy(data) {
  return request({
    url: '/student/study/record',
    method: 'post',
    data
  })
}

// 获取学习建议
export function getStudyAdvice(params) {
  return request({
    url: '/student/study/advice',
    method: 'get',
    params
  })
}

// 获取学习排行榜
export function getStudyLeaderboard(params) {
  return request({
    url: '/student/study/leaderboard',
    method: 'get',
    params
  })
}
