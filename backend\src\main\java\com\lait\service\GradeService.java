package com.lait.service;

import com.lait.dto.GradeDTO;
import com.lait.entity.Grade;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 成绩服务接口
 */
public interface GradeService {

    /**
     * 创建成绩
     */
    GradeDTO createGrade(GradeDTO.CreateGradeRequest request);

    /**
     * 根据ID获取成绩
     */
    GradeDTO getGradeById(Long id);

    /**
     * 更新成绩信息
     */
    GradeDTO updateGrade(Long id, GradeDTO.UpdateGradeRequest request);

    /**
     * 删除成绩
     */
    void deleteGrade(Long id);

    /**
     * 分页查询成绩
     */
    Page<GradeDTO> getGrades(Pageable pageable);

    /**
     * 根据学生ID分页查询成绩
     */
    Page<GradeDTO> getGradesByStudentId(Long studentId, Pageable pageable);

    /**
     * 根据学科ID分页查询成绩
     */
    Page<GradeDTO> getGradesBySubjectId(Long subjectId, Pageable pageable);

    /**
     * 根据条件查询成绩
     */
    Page<GradeDTO> searchGrades(GradeDTO.GradeQueryRequest request, Pageable pageable);

    /**
     * 获取学生某学科的所有成绩
     */
    List<GradeDTO> getStudentSubjectGrades(Long studentId, Long subjectId);

    /**
     * 获取学生成绩统计
     */
    GradeDTO.GradeStatistics getStudentGradeStatistics(Long studentId, Long subjectId);

    /**
     * 获取班级成绩统计
     */
    List<GradeDTO.GradeStatistics> getClassGradeStatistics(Integer gradeLevel, Long subjectId);

    /**
     * 发布成绩
     */
    void publishGrade(Long id);

    /**
     * 批量发布成绩
     */
    void batchPublishGrades(Long[] gradeIds);

    /**
     * 归档成绩
     */
    void archiveGrade(Long id);

    /**
     * 获取学生最新成绩
     */
    List<GradeDTO> getStudentLatestGrades(Long studentId, int limit);

    /**
     * 计算学生平均分
     */
    Double calculateStudentAverageScore(Long studentId, Long subjectId);

    /**
     * 获取成绩趋势数据
     */
    List<GradeDTO> getGradeTrend(Long studentId, Long subjectId, int months);

    /**
     * 获取学生统计信息
     */
    java.util.Map<String, Object> getStudentStatistics(Long studentId);
}
