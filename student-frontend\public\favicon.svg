<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="url(#grad1)" stroke="#fff" stroke-width="1"/>
  
  <!-- 书本图标 -->
  <g transform="translate(8, 6)">
    <!-- 书本主体 -->
    <rect x="2" y="4" width="12" height="16" rx="1" fill="#fff" opacity="0.9"/>
    <rect x="2" y="4" width="12" height="2" fill="#fff"/>
    
    <!-- 书页线条 -->
    <line x1="4" y1="8" x2="12" y2="8" stroke="#667eea" stroke-width="0.5"/>
    <line x1="4" y1="10" x2="12" y2="10" stroke="#667eea" stroke-width="0.5"/>
    <line x1="4" y1="12" x2="10" y2="12" stroke="#667eea" stroke-width="0.5"/>
    <line x1="4" y1="14" x2="12" y2="14" stroke="#667eea" stroke-width="0.5"/>
    <line x1="4" y1="16" x2="9" y2="16" stroke="#667eea" stroke-width="0.5"/>
    
    <!-- 智能标识 (小星星) -->
    <circle cx="12" cy="6" r="1.5" fill="#ffd700"/>
    <polygon points="12,5.2 12.3,5.8 12.9,5.8 12.5,6.2 12.6,6.8 12,6.5 11.4,6.8 11.5,6.2 11.1,5.8 11.7,5.8" fill="#fff"/>
  </g>
</svg>
