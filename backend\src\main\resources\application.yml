server:
  port: 8081
  servlet:
    context-path: /api

spring:
  application:
    name: lait-backend

  # 数据库配置
  datasource:
    url: ********************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# JWT配置
jwt:
  secret: lait-secret-key-for-jwt-token-generation
  expiration: 86400000 # 24小时

# Coze AI配置
coze:
  api:
    url: https://api.coze.cn/v1/chat
    token: ${COZE_API_TOKEN:}
  bot:
    id: ${COZE_BOT_ID:}

# 飞书API配置
feishu:
  api:
    # 飞书应用配置
    app-id: cli_a8b3e6f040fa5013
    app-secret: nasfWDD7V3EIyuznY2BQGbQHZBE0wgJj

    # API地址配置
    base-url: https://open.feishu.cn
    token-url: /open-apis/auth/v3/tenant_access_token/internal
    file-list-url: /open-apis/drive/v1/files
    file-content-url: /open-apis/docx/v1/documents/{document_id}/raw_content
    file-meta-url: /open-apis/drive/v1/metas/batch_query

    # 连接配置
    connect-timeout: 10000
    read-timeout: 30000
    token-cache-time: 7200

    # 功能配置
    enabled: true  # 默认关闭，需要配置App ID和Secret后启用
    default-folder-id: ""
    page-size: 50

    # 支持的文件类型
    supported-types:
      - doc
      - docx
      - sheet
      - bitable
      - mindnote
      - slides

    # OAuth配置
    redirect-uri: ${FEISHU_REDIRECT_URI:http://localhost:3000/feishu-oauth/callback}
    scopes:
      - "drive:drive:readonly"
      - "contact:user.id:readonly"
      - "contact:user.base:readonly"
    user-token-cache-time: 7200

# 日志配置
logging:
  level:
    com.lait: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
