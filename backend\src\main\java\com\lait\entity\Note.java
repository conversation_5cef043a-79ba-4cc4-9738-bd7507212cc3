package com.lait.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 笔记实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "notes")
public class Note extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "学生ID不能为空")
    @Column(name = "student_id", nullable = false)
    private Long studentId;

    @Column(name = "subject_id")
    private Long subjectId;

    @NotBlank(message = "笔记标题不能为空")
    @Size(max = 200, message = "笔记标题长度不能超过200个字符")
    @Column(nullable = false)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String content; // 笔记内容

    @Size(max = 200, message = "标签长度不能超过200个字符")
    private String tags; // 标签，逗号分隔

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NoteType noteType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private NoteStatus status;

    @Column(name = "feishu_doc_id")
    private String feishuDocId; // 飞书文档ID

    @Column(name = "is_shared")
    private Boolean isShared = false; // 是否共享

    @Column(name = "view_count")
    private Integer viewCount = 0; // 查看次数

    /**
     * 笔记类型枚举
     */
    public enum NoteType {
        STUDY("学习笔记"),
        REVIEW("复习笔记"),
        SUMMARY("总结笔记"),
        ERROR("错题笔记");

        private final String description;

        NoteType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 笔记状态枚举
     */
    public enum NoteStatus {
        DRAFT("草稿"),
        PUBLISHED("已发布"),
        ARCHIVED("已归档");

        private final String description;

        NoteStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
