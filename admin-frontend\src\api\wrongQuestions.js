import request from './request'

// 获取错题列表
export function getWrongQuestions(params) {
  return request({
    url: '/wrong-questions',
    method: 'get',
    params
  })
}

// 获取错题详情
export function getWrongQuestion(id) {
  return request({
    url: `/wrong-questions/${id}`,
    method: 'get'
  })
}

// 创建错题记录
export function createWrongQuestion(data) {
  return request({
    url: '/wrong-questions',
    method: 'post',
    data
  })
}

// 更新错题记录
export function updateWrongQuestion(id, data) {
  return request({
    url: `/wrong-questions/${id}`,
    method: 'put',
    data
  })
}

// 删除错题记录
export function deleteWrongQuestion(id) {
  return request({
    url: `/wrong-questions/${id}`,
    method: 'delete'
  })
}

// 批量删除错题记录
export function batchDeleteWrongQuestions(ids) {
  return request({
    url: '/wrong-questions/batch',
    method: 'delete',
    data: { ids }
  })
}

// 根据学生获取错题
export function getWrongQuestionsByStudent(studentId, params) {
  return request({
    url: `/wrong-questions/student/${studentId}`,
    method: 'get',
    params
  })
}

// 标记错题为已掌握
export function markAsMastered(id) {
  return request({
    url: `/wrong-questions/${id}/master`,
    method: 'put'
  })
}

// 获取错题统计信息
export function getWrongQuestionStats() {
  return request({
    url: '/wrong-questions/stats',
    method: 'get'
  })
}

// 获取错题分析报告
export function getWrongQuestionAnalysis(studentId) {
  return request({
    url: `/wrong-questions/analysis/${studentId}`,
    method: 'get'
  })
}
