# 飞书API管理功能访问指南

## 权限说明

飞书API管理功能现在已经对所有登录用户开放，不再需要管理员权限。

## 访问方式

### 1. 通过菜单访问
1. 登录系统
2. 在左侧导航菜单中找到"飞书API管理"
3. 点击进入飞书API管理页面

### 2. 直接URL访问
- 访问路径：`/feishu-api`
- 完整URL：`http://localhost:3000/feishu-api`（根据实际部署地址调整）

## 功能权限

### ✅ **所有用户可访问的功能**
- **API连接测试**: 测试飞书API配置和连接状态
- **配置状态查看**: 查看App ID和App Secret配置状态
- **文档列表获取**: 获取飞书个人空间文档列表
- **文档预览**: 预览飞书文档内容
- **文档同步**: 将飞书文档同步到本地数据库
- **支持类型查看**: 查看支持的文档类型
- **使用统计**: 查看API使用统计信息

### ❌ **管理员专用功能**（在飞书文档管理页面）
- **文档CRUD操作**: 创建、编辑、删除文档记录
- **权限管理**: 设置文档访问权限
- **批量操作**: 批量导入、同步文档
- **高级统计**: 详细的统计分析功能

## 技术实现

### 后端权限控制

#### 1. 新增控制器
创建了独立的`FeishuApiController`，不需要管理员权限：
```java
@RestController
@RequestMapping("/feishu-api")
@RequiredArgsConstructor
@Slf4j
public class FeishuApiController {
    // 无@PreAuthorize注解，所有登录用户可访问
}
```

#### 2. API路径
- **公开API**: `/feishu-api/*` - 所有登录用户可访问
- **管理员API**: `/admin/feishu/*` - 仅管理员可访问

### 前端路由配置

#### 1. 菜单路由
```javascript
{
  path: 'feishu-api',
  name: 'FeishuApiManager',
  component: () => import('@/views/FeishuApiManager.vue'),
  meta: { title: '飞书API管理', icon: 'Connection' }
}
```

#### 2. 权限要求
- 只需要登录认证（`requiresAuth: true`）
- 不需要特定角色权限

## 使用流程

### 1. 首次使用
1. **检查配置**: 页面会自动检查App ID和App Secret配置状态
2. **测试连接**: 点击"测试连接"验证API配置
3. **查看结果**: 根据测试结果进行相应配置

### 2. 日常使用
1. **获取文档**: 点击"获取文档列表"从飞书获取文档
2. **预览文档**: 点击"预览"查看文档内容
3. **同步文档**: 点击"同步"将文档保存到本地数据库

### 3. 配置要求
如果显示配置未完成，需要设置环境变量：
```bash
export FEISHU_APP_ID="your_app_id"
export FEISHU_APP_SECRET="your_app_secret"
```

## 功能对比

| 功能 | 飞书API管理 | 飞书文档管理 | 权限要求 |
|------|-------------|--------------|----------|
| API连接测试 | ✅ | ✅ | 登录用户 |
| 获取文档列表 | ✅ | ❌ | 登录用户 |
| 文档预览 | ✅ | ✅ | 登录用户 |
| 文档同步 | ✅ | ✅ | 登录用户 |
| 文档CRUD | ❌ | ✅ | 管理员 |
| 权限管理 | ❌ | ✅ | 管理员 |
| 批量操作 | 基础 | 高级 | 管理员 |
| 统计分析 | 基础 | 详细 | 管理员 |

## 安全考虑

### 1. 数据保护
- 不暴露敏感的配置信息（App Secret等）
- 只显示配置状态，不显示具体值
- API调用日志记录但不包含敏感信息

### 2. 权限隔离
- 读取操作对所有用户开放
- 写入操作仍需要适当权限
- 管理功能保持管理员专用

### 3. 错误处理
- 友好的错误提示
- 不暴露系统内部错误信息
- 安全的降级处理

## 故障排除

### 1. 无法访问页面
- 确认已登录系统
- 检查网络连接
- 清除浏览器缓存

### 2. API连接失败
- 检查App ID和App Secret配置
- 验证网络连接到飞书服务器
- 查看后端日志获取详细错误信息

### 3. 文档获取失败
- 确认飞书应用权限配置
- 检查用户在飞书中的权限
- 验证文档访问权限

## 更新说明

### 版本变更
- **之前**: 飞书功能仅管理员可访问
- **现在**: 飞书API管理对所有登录用户开放
- **保持**: 飞书文档管理仍需管理员权限

### 兼容性
- 现有管理员功能不受影响
- 新增的公开功能不影响安全性
- API路径向后兼容

通过这些改进，普通用户现在可以方便地使用飞书API功能，同时保持了系统的安全性和管理功能的专用性。
