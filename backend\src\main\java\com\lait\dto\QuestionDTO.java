package com.lait.dto;

import com.lait.entity.Question;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 题目数据传输对象
 */
@Data
public class QuestionDTO {
    
    private Long id;
    private Long subjectId;
    private String subjectName;
    private String title;
    private String content;
    private Question.QuestionType type;
    private String options;
    private String correctAnswer;
    private String explanation;
    private Question.DifficultyLevel difficulty;
    private Integer gradeLevel;
    private String tags;
    private Integer usageCount;
    private Integer correctCount;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * 创建题目请求
     */
    @Data
    public static class CreateQuestionRequest {
        
        @NotNull(message = "学科ID不能为空")
        private Long subjectId;
        
        @NotBlank(message = "题目标题不能为空")
        @Size(max = 200, message = "题目标题长度不能超过200个字符")
        private String title;
        
        @NotBlank(message = "题目内容不能为空")
        private String content;
        
        @NotNull(message = "题目类型不能为空")
        private Question.QuestionType type;
        
        private String options; // JSON格式存储选项
        
        @NotBlank(message = "正确答案不能为空")
        private String correctAnswer;
        
        private String explanation;
        
        @NotNull(message = "难度等级不能为空")
        private Question.DifficultyLevel difficulty;
        
        private Integer gradeLevel;
        
        @Size(max = 200, message = "标签长度不能超过200个字符")
        private String tags;
    }

    /**
     * 更新题目请求
     */
    @Data
    public static class UpdateQuestionRequest {
        
        @Size(max = 200, message = "题目标题长度不能超过200个字符")
        private String title;
        
        private String content;
        private Question.QuestionType type;
        private String options;
        private String correctAnswer;
        private String explanation;
        private Question.DifficultyLevel difficulty;
        private Integer gradeLevel;
        
        @Size(max = 200, message = "标签长度不能超过200个字符")
        private String tags;
    }

    /**
     * 题目查询请求
     */
    @Data
    public static class QuestionQueryRequest {
        private Long subjectId;
        private Question.QuestionType type;
        private Question.DifficultyLevel difficulty;
        private Integer gradeLevel;
        private String keyword;
        private String tags;
    }

    /**
     * 题目选项
     */
    @Data
    public static class QuestionOption {
        private String key;
        private String value;
        private Boolean isCorrect;
    }

    /**
     * 题目统计信息
     */
    @Data
    public static class QuestionStatistics {
        private Long questionId;
        private String title;
        private Integer usageCount;
        private Integer correctCount;
        private Double correctRate;
        private Question.DifficultyLevel difficulty;
        private String subjectName;
    }

    /**
     * 批量导入题目请求
     */
    @Data
    public static class BatchImportRequest {
        private Long subjectId;
        private List<CreateQuestionRequest> questions;
    }
}
