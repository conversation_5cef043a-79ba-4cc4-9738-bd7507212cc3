package com.lait.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lait.config.FeishuConfig;
import com.lait.dto.FeishuFileDto;
import com.lait.dto.FeishuTokenDto;
import com.lait.dto.FeishuUserTokenDto;
import com.lait.dto.FeishuUserInfoDto;
import com.lait.service.FeishuApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 飞书API服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeishuApiServiceImpl implements FeishuApiService {

    private final FeishuConfig feishuConfig;
    private final ObjectMapper objectMapper;

    // 令牌缓存
    private final Map<String, FeishuTokenDto> tokenCache = new ConcurrentHashMap<>();

    // 用户令牌缓存
    private final Map<String, FeishuUserTokenDto> userTokenCache = new ConcurrentHashMap<>();

    @Override
    public FeishuTokenDto getAppAccessToken() {
        String cacheKey = "app_access_token";
        FeishuTokenDto cachedToken = tokenCache.get(cacheKey);

        // 检查缓存的令牌是否有效
        if (cachedToken != null && !cachedToken.isExpired()) {
            log.debug("使用缓存的应用访问令牌");
            return cachedToken;
        }

        try {
            log.info("获取飞书应用访问令牌");

            // 构建请求
            String url = feishuConfig.getBaseUrl() + feishuConfig.getAppTokenUrl();

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("app_id", feishuConfig.getAppId());
            requestBody.put("app_secret", feishuConfig.getAppSecret());

            String jsonBody = objectMapper.writeValueAsString(requestBody);

            // 发送请求
            String response = sendPostRequest(url, jsonBody, null);

            // 解析响应
            JsonNode responseNode = objectMapper.readTree(response);

            if (responseNode.get("code").asInt() != 0) {
                String errorMsg = responseNode.get("msg").asText();
                throw new RuntimeException("获取应用访问令牌失败: " + errorMsg);
            }

            JsonNode dataNode = responseNode.get("app_access_token");
            if (dataNode == null) {
                throw new RuntimeException("响应中缺少应用访问令牌");
            }

            FeishuTokenDto token = new FeishuTokenDto();
            token.setAccessToken(dataNode.asText());
            token.setTokenType("Bearer");
            token.setExpiresIn(responseNode.get("expire").asInt());
            token.setTimestamp(System.currentTimeMillis() / 1000);

            // 缓存令牌
            tokenCache.put(cacheKey, token);

            log.info("成功获取飞书应用访问令牌，有效期: {} 秒", token.getExpiresIn());
            return token;

        } catch (Exception e) {
            log.error("获取飞书应用访问令牌失败", e);
            throw new RuntimeException("获取应用访问令牌失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String generateAuthUrl(String state) {
        try {
            log.info("生成飞书OAuth授权URL - state: {}", state);

            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(feishuConfig.getBaseUrl())
                     .append(feishuConfig.getAuthUrl())
                     .append("?app_id=").append(URLEncoder.encode(feishuConfig.getAppId(), "UTF-8"))
                     .append("&redirect_uri=").append(URLEncoder.encode(feishuConfig.getRedirectUri(), "UTF-8"))
                     .append("&scope=").append(URLEncoder.encode(String.join(" ", feishuConfig.getScopes()), "UTF-8"))
                     .append("&state=").append(URLEncoder.encode(state, "UTF-8"));

            String authUrl = urlBuilder.toString();
            log.info("生成的授权URL: {}", authUrl);
            return authUrl;

        } catch (UnsupportedEncodingException e) {
            log.error("生成授权URL失败", e);
            throw new RuntimeException("生成授权URL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FeishuUserTokenDto getUserAccessToken(String code) {
        try {
            log.info("通过授权码获取用户访问令牌 - code: {}", code);

            // 构建请求
            String url = feishuConfig.getBaseUrl() + feishuConfig.getUserTokenUrl();

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("grant_type", "authorization_code");
            requestBody.put("client_id", feishuConfig.getAppId());
            requestBody.put("client_secret", feishuConfig.getAppSecret());
            requestBody.put("code", code);
            requestBody.put("redirect_uri", feishuConfig.getRedirectUri());

            String jsonBody = objectMapper.writeValueAsString(requestBody);

            // 发送请求
            String response = sendPostRequest(url, jsonBody, null);

            // 解析响应
            return parseUserTokenResponse(response);

        } catch (Exception e) {
            log.error("获取用户访问令牌失败", e);
            throw new RuntimeException("获取用户访问令牌失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FeishuUserTokenDto refreshUserAccessToken(String refreshToken) {
        try {
            log.info("刷新用户访问令牌");

            // 构建请求
            String url = feishuConfig.getBaseUrl() + feishuConfig.getUserTokenUrl();

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("grant_type", "refresh_token");
            requestBody.put("refresh_token", refreshToken);

            String jsonBody = objectMapper.writeValueAsString(requestBody);

            // 发送请求
            String response = sendPostRequest(url, jsonBody, null);

            // 解析响应
            return parseUserTokenResponse(response);

        } catch (Exception e) {
            log.error("刷新用户访问令牌失败", e);
            throw new RuntimeException("刷新用户访问令牌失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FeishuUserInfoDto getUserInfo(String userAccessToken) {
        try {
            log.info("获取用户信息");

            // 构建请求URL
            String url = feishuConfig.getBaseUrl() + feishuConfig.getUserInfoUrl();

            // 发送请求
            String response = sendGetRequest(url, userAccessToken);

            // 解析响应
            return parseUserInfoResponse(response);

        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            throw new RuntimeException("获取用户信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FeishuTokenDto getTenantAccessToken() {
        String cacheKey = "tenant_access_token";
        FeishuTokenDto cachedToken = tokenCache.get(cacheKey);

        // 检查缓存的令牌是否有效
        if (cachedToken != null && !cachedToken.isExpired()) {
            log.debug("使用缓存的访问令牌");
            return cachedToken;
        }

        try {
            log.info("获取飞书租户访问令牌");

            // 构建请求
            String url = feishuConfig.getBaseUrl() + feishuConfig.getTenantTokenUrl();

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("app_id", feishuConfig.getAppId());
            requestBody.put("app_secret", feishuConfig.getAppSecret());

            String jsonBody = objectMapper.writeValueAsString(requestBody);

            // 发送请求
            String response = sendPostRequest(url, jsonBody, null);

            // 解析响应
            JsonNode responseNode = objectMapper.readTree(response);

            if (responseNode.get("code").asInt() != 0) {
                String errorMsg = responseNode.get("msg").asText();
                throw new RuntimeException("获取访问令牌失败: " + errorMsg);
            }

            JsonNode dataNode = responseNode.get("tenant_access_token");
            if (dataNode == null) {
                throw new RuntimeException("响应中缺少访问令牌");
            }

            FeishuTokenDto token = new FeishuTokenDto();
            token.setAccessToken(dataNode.asText());
            token.setTokenType("Bearer");
            token.setExpiresIn(responseNode.get("expire").asInt());
            token.setTimestamp(System.currentTimeMillis() / 1000);

            // 缓存令牌
            tokenCache.put(cacheKey, token);

            log.info("成功获取飞书访问令牌，有效期: {} 秒", token.getExpiresIn());
            return token;

        } catch (Exception e) {
            log.error("获取飞书访问令牌失败", e);
            throw new RuntimeException("获取访问令牌失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FeishuFileDto> getFileList(String folderId, String pageToken, Integer pageSize) {
        log.info("获取飞书文件列表 - folderId: {}, pageToken: {}, pageSize: {}",
                folderId, pageToken, pageSize);

        // 检查配置状态
        if (!feishuConfig.isEnabled()) {
            log.warn("飞书API未启用，返回模拟数据");
            return generateMockFileList();
        }

        // 检查必要的配置
        if (feishuConfig.getAppId() == null || feishuConfig.getAppId().isEmpty()) {
            log.warn("飞书App ID未配置，返回模拟数据");
            return generateMockFileList();
        }

        if (feishuConfig.getAppSecret() == null || feishuConfig.getAppSecret().isEmpty()) {
            log.warn("飞书App Secret未配置，返回模拟数据");
            return generateMockFileList();
        }

        try {
            log.info("开始调用飞书API获取文件列表");

            FeishuTokenDto token = getTenantAccessToken();

            // 构建请求URL
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(feishuConfig.getBaseUrl())
                     .append(feishuConfig.getFileListUrl());

            List<String> params = new ArrayList<>();
            if (folderId != null && !folderId.isEmpty()) {
                params.add("folder_token=" + folderId);
            }
            if (pageToken != null && !pageToken.isEmpty()) {
                params.add("page_token=" + pageToken);
            }
            if (pageSize != null) {
                params.add("page_size=" + pageSize);
            } else {
                params.add("page_size=" + feishuConfig.getPageSize());
            }

            if (!params.isEmpty()) {
                urlBuilder.append("?").append(String.join("&", params));
            }

            String url = urlBuilder.toString();
            log.info("请求URL: {}", url);

            // 发送请求
            String response = sendGetRequest(url, token.getAccessToken());
            log.info("收到响应，长度: {}", response.length());

            // 解析响应
            return parseFileListResponse(response);

        } catch (Exception e) {
            log.error("获取飞书文件列表失败，返回模拟数据", e);
            // 返回模拟数据作为降级处理
            return generateMockFileList();
        }
    }

    @Override
    public String getFileContent(String fileToken, String fileType) {
        if (!feishuConfig.isEnabled()) {
            log.warn("飞书API未启用，返回模拟内容");
            return generateMockContent(fileToken, fileType);
        }

        try {
            log.info("获取飞书文件内容 - fileToken: {}, fileType: {}", fileToken, fileType);

            FeishuTokenDto token = getTenantAccessToken();

            // 根据文件类型选择不同的API
            String url = buildFileContentUrl(fileToken, fileType);

            // 发送请求
            String response = sendGetRequest(url, token.getAccessToken());

            // 解析响应
            return parseFileContentResponse(response, fileType);

        } catch (Exception e) {
            log.error("获取飞书文件内容失败", e);
            // 返回模拟内容作为降级处理
            return generateMockContent(fileToken, fileType);
        }
    }

    @Override
    public Map<String, Object> getFileMetadata(List<String> fileTokens) {
        if (!feishuConfig.isEnabled()) {
            log.warn("飞书API未启用，返回空元信息");
            return new HashMap<>();
        }

        try {
            log.info("获取飞书文件元信息 - fileTokens: {}", fileTokens);

            FeishuTokenDto token = getTenantAccessToken();

            // 构建请求
            String url = feishuConfig.getBaseUrl() + feishuConfig.getFileMetaUrl();

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("request_docs", fileTokens.stream()
                    .map(token1 -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("doc_token", token1);
                        map.put("doc_type", "doc");
                        return map;
                    })
                    .toArray());

            String jsonBody = objectMapper.writeValueAsString(requestBody);

            // 发送请求
            String response = sendPostRequest(url, jsonBody, token.getAccessToken());

            // 解析响应
            return parseMetadataResponse(response);

        } catch (Exception e) {
            log.error("获取飞书文件元信息失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public List<FeishuFileDto> searchFiles(String query, String pageToken, Integer pageSize) {
        // 飞书搜索API需要特殊权限，这里先返回空列表
        log.info("搜索飞书文件 - query: {}", query);
        return new ArrayList<>();
    }

    @Override
    public boolean testConnection() {
        try {
            log.info("测试飞书API连接");

            // 检查基本配置
            log.info("检查飞书API配置状态:");
            log.info("- API启用状态: {}", feishuConfig.isEnabled());
            log.info("- App ID配置: {}", feishuConfig.getAppId() != null && !feishuConfig.getAppId().isEmpty() ? "已配置" : "未配置");
            log.info("- App Secret配置: {}", feishuConfig.getAppSecret() != null && !feishuConfig.getAppSecret().isEmpty() ? "已配置" : "未配置");
            log.info("- Base URL: {}", feishuConfig.getBaseUrl());

            if (!feishuConfig.isEnabled()) {
                log.warn("飞书API未启用");
                return false;
            }

            if (feishuConfig.getAppId() == null || feishuConfig.getAppId().isEmpty()) {
                log.warn("飞书App ID未配置");
                return false;
            }

            if (feishuConfig.getAppSecret() == null || feishuConfig.getAppSecret().isEmpty()) {
                log.warn("飞书App Secret未配置");
                return false;
            }

            log.info("开始获取访问令牌...");
            FeishuTokenDto token = getTenantAccessToken();
            boolean connected = token != null && token.getAccessToken() != null;

            if (connected) {
                log.info("飞书API连接测试成功 - 令牌长度: {}, 过期时间: {}秒",
                        token.getAccessToken().length(), token.getExpiresIn());
            } else {
                log.warn("飞书API连接测试失败 - 无法获取有效令牌");
            }

            return connected;

        } catch (Exception e) {
            log.error("飞书API连接测试失败", e);
            return false;
        }
    }


    // 私有方法

    private String sendGetRequest(String url, String accessToken) throws IOException {
        try (CloseableHttpClient httpClient = createHttpClient()) {
            HttpGet request = new HttpGet(url);

            // 设置请求头
            request.setHeader("Authorization", "Bearer " + accessToken);
            request.setHeader("Content-Type", "application/json; charset=utf-8");

            HttpResponse response = httpClient.execute(request);
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                return EntityUtils.toString(entity, "UTF-8");
            }

            throw new IOException("响应为空");
        }
    }

    private String sendPostRequest(String url, String jsonBody, String accessToken) throws IOException {
        try (CloseableHttpClient httpClient = createHttpClient()) {
            HttpPost request = new HttpPost(url);

            // 设置请求头
            request.setHeader("Content-Type", "application/json; charset=utf-8");
            if (accessToken != null) {
                request.setHeader("Authorization", "Bearer " + accessToken);
            }

            // 设置请求体
            StringEntity entity = new StringEntity(jsonBody, "UTF-8");
            request.setEntity(entity);

            HttpResponse response = httpClient.execute(request);
            HttpEntity responseEntity = response.getEntity();

            if (responseEntity != null) {
                return EntityUtils.toString(responseEntity, "UTF-8");
            }

            throw new IOException("响应为空");
        }
    }

    private CloseableHttpClient createHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(feishuConfig.getConnectTimeout())
                .setSocketTimeout(feishuConfig.getReadTimeout())
                .build();

        return HttpClients.custom()
                .setDefaultRequestConfig(config)
                .build();
    }

    private List<FeishuFileDto> parseFileListResponse(String response) throws Exception {
        JsonNode responseNode = objectMapper.readTree(response);

        if (responseNode.get("code").asInt() != 0) {
            String errorMsg = responseNode.get("msg").asText();
            throw new RuntimeException("获取文件列表失败: " + errorMsg);
        }

        List<FeishuFileDto> files = new ArrayList<>();
        JsonNode dataNode = responseNode.get("data");
        if (dataNode != null && dataNode.has("files")) {
            JsonNode filesNode = dataNode.get("files");

            for (JsonNode fileNode : filesNode) {
                FeishuFileDto file = new FeishuFileDto();
                file.setToken(fileNode.get("token").asText());
                file.setName(fileNode.get("name").asText());
                file.setType(fileNode.get("type").asText());

                if (fileNode.has("parent_token")) {
                    file.setParentToken(fileNode.get("parent_token").asText());
                }

                if (fileNode.has("url")) {
                    file.setUrl(fileNode.get("url").asText());
                }

                if (fileNode.has("created_time")) {
                    long timestamp = fileNode.get("created_time").asLong();
                    file.setCreateTime(LocalDateTime.ofInstant(
                            Instant.ofEpochSecond(timestamp), ZoneId.systemDefault()));
                }

                if (fileNode.has("modified_time")) {
                    long timestamp = fileNode.get("modified_time").asLong();
                    file.setModifyTime(LocalDateTime.ofInstant(
                            Instant.ofEpochSecond(timestamp), ZoneId.systemDefault()));
                }

                if (fileNode.has("size")) {
                    file.setSize(fileNode.get("size").asLong());
                }

                file.setIsFolder("folder".equals(file.getType()));

                files.add(file);
            }
        }

        log.info("解析到 {} 个文件", files.size());
        return files;
    }

    private String buildFileContentUrl(String fileToken, String fileType) {
        String baseUrl = feishuConfig.getBaseUrl();

        switch (fileType.toLowerCase()) {
            case "doc":
            case "docx":
                return baseUrl + feishuConfig.getFileContentUrl().replace("{document_id}", fileToken);
            case "sheet":
                return baseUrl + "/open-apis/sheets/v2/spreadsheets/" + fileToken + "/values_batch_get";
            case "bitable":
                return baseUrl + "/open-apis/bitable/v1/apps/" + fileToken + "/tables";
            default:
                return baseUrl + "/open-apis/drive/v1/files/" + fileToken + "/download";
        }
    }

    private String parseFileContentResponse(String response, String fileType) throws Exception {
        JsonNode responseNode = objectMapper.readTree(response);

        if (responseNode.get("code").asInt() != 0) {
            String errorMsg = responseNode.get("msg").asText();
            throw new RuntimeException("获取文件内容失败: " + errorMsg);
        }

        JsonNode dataNode = responseNode.get("data");
        if (dataNode == null) {
            return "";
        }

        switch (fileType.toLowerCase()) {
            case "doc":
            case "docx":
                return dataNode.has("content") ? dataNode.get("content").asText() : "";
            case "sheet":
                return parseSheetContent(dataNode);
            case "bitable":
                return parseBitableContent(dataNode);
            default:
                return dataNode.toString();
        }
    }

    private String parseSheetContent(JsonNode dataNode) {
        StringBuilder content = new StringBuilder();
        content.append("# 表格内容\n\n");

        if (dataNode.has("valueRanges")) {
            JsonNode ranges = dataNode.get("valueRanges");
            for (JsonNode range : ranges) {
                if (range.has("values")) {
                    JsonNode values = range.get("values");
                    for (JsonNode row : values) {
                        for (JsonNode cell : row) {
                            content.append(cell.asText()).append("\t");
                        }
                        content.append("\n");
                    }
                }
            }
        }

        return content.toString();
    }

    private String parseBitableContent(JsonNode dataNode) {
        StringBuilder content = new StringBuilder();
        content.append("# 多维表格内容\n\n");

        if (dataNode.has("items")) {
            JsonNode items = dataNode.get("items");
            for (JsonNode item : items) {
                if (item.has("name")) {
                    content.append("## ").append(item.get("name").asText()).append("\n\n");
                }
            }
        }

        return content.toString();
    }

    private Map<String, Object> parseMetadataResponse(String response) throws Exception {
        JsonNode responseNode = objectMapper.readTree(response);

        if (responseNode.get("code").asInt() != 0) {
            String errorMsg = responseNode.get("msg").asText();
            log.warn("获取文件元信息失败: {}", errorMsg);
            return new HashMap<>();
        }

        Map<String, Object> metadata = new HashMap<>();
        JsonNode dataNode = responseNode.get("data");
        if (dataNode != null) {
            metadata.put("docs", dataNode);
        }

        return metadata;
    }

    private String generateMockContent(String fileToken, String fileType) {
        StringBuilder content = new StringBuilder();
        content.append("# 飞书文档内容 (模拟)\n\n");
        content.append("**文件Token**: ").append(fileToken).append("\n");
        content.append("**文件类型**: ").append(fileType).append("\n\n");
        content.append("## 说明\n\n");
        content.append("这是模拟的飞书文档内容。在实际环境中，这里将显示从飞书API获取的真实文档内容。\n\n");
        content.append("要启用真实的飞书API集成，请：\n");
        content.append("1. 在飞书开放平台创建应用\n");
        content.append("2. 获取App ID和App Secret\n");
        content.append("3. 在application.yml中配置相关参数\n");
        content.append("4. 设置feishu.api.enabled=true\n\n");
        content.append("## 支持的文档类型\n\n");
        content.append("- **doc/docx**: 飞书文档\n");
        content.append("- **sheet**: 飞书表格\n");
        content.append("- **bitable**: 多维表格\n");
        content.append("- **mindnote**: 思维笔记\n");
        content.append("- **slides**: 飞书幻灯片\n\n");
        content.append("---\n\n");
        content.append("*生成时间: ").append(LocalDateTime.now()).append("*");

        return content.toString();
    }

    /**
     * 生成模拟文件列表
     */
    private List<FeishuFileDto> generateMockFileList() {
        List<FeishuFileDto> mockFiles = new ArrayList<>();

        // 模拟文档1
        FeishuFileDto doc1 = new FeishuFileDto();
        doc1.setToken("mock_doc_001");
        doc1.setName("项目需求文档 (模拟)");
        doc1.setType("doc");
        doc1.setCreateTime(LocalDateTime.now().minusDays(7));
        doc1.setModifyTime(LocalDateTime.now().minusDays(1));
        doc1.setSize(1024L * 50); // 50KB
        doc1.setCreatorName("张三");
        doc1.setModifierName("李四");
        doc1.setIsFolder(false);
        doc1.setUrl("https://feishu.cn/docs/mock_doc_001");
        mockFiles.add(doc1);

        // 模拟表格1
        FeishuFileDto sheet1 = new FeishuFileDto();
        sheet1.setToken("mock_sheet_001");
        sheet1.setName("数据统计表 (模拟)");
        sheet1.setType("sheet");
        sheet1.setCreateTime(LocalDateTime.now().minusDays(5));
        sheet1.setModifyTime(LocalDateTime.now().minusDays(2));
        sheet1.setSize(1024L * 120); // 120KB
        sheet1.setCreatorName("王五");
        sheet1.setModifierName("赵六");
        sheet1.setIsFolder(false);
        sheet1.setUrl("https://feishu.cn/sheets/mock_sheet_001");
        mockFiles.add(sheet1);

        // 模拟演示文稿1
        FeishuFileDto slides1 = new FeishuFileDto();
        slides1.setToken("mock_slides_001");
        slides1.setName("产品介绍PPT (模拟)");
        slides1.setType("slides");
        slides1.setCreateTime(LocalDateTime.now().minusDays(3));
        slides1.setModifyTime(LocalDateTime.now().minusHours(6));
        slides1.setSize(1024L * 1024 * 5); // 5MB
        slides1.setCreatorName("孙七");
        slides1.setModifierName("周八");
        slides1.setIsFolder(false);
        slides1.setUrl("https://feishu.cn/slides/mock_slides_001");
        mockFiles.add(slides1);

        // 模拟多维表格1
        FeishuFileDto bitable1 = new FeishuFileDto();
        bitable1.setToken("mock_bitable_001");
        bitable1.setName("任务管理表 (模拟)");
        bitable1.setType("bitable");
        bitable1.setCreateTime(LocalDateTime.now().minusDays(10));
        bitable1.setModifyTime(LocalDateTime.now().minusHours(2));
        bitable1.setSize(1024L * 200); // 200KB
        bitable1.setCreatorName("吴九");
        bitable1.setModifierName("郑十");
        bitable1.setIsFolder(false);
        bitable1.setUrl("https://feishu.cn/bitable/mock_bitable_001");
        mockFiles.add(bitable1);

        // 模拟思维笔记1
        FeishuFileDto mindnote1 = new FeishuFileDto();
        mindnote1.setToken("mock_mindnote_001");
        mindnote1.setName("产品规划思维导图 (模拟)");
        mindnote1.setType("mindnote");
        mindnote1.setCreateTime(LocalDateTime.now().minusDays(2));
        mindnote1.setModifyTime(LocalDateTime.now().minusHours(4));
        mindnote1.setSize(1024L * 80); // 80KB
        mindnote1.setCreatorName("钱十一");
        mindnote1.setModifierName("孙十二");
        mindnote1.setIsFolder(false);
        mindnote1.setUrl("https://feishu.cn/mindnote/mock_mindnote_001");
        mockFiles.add(mindnote1);

        log.info("生成了 {} 个模拟文件", mockFiles.size());
        return mockFiles;
    }

    /**
     * 解析用户令牌响应
     */
    private FeishuUserTokenDto parseUserTokenResponse(String response) throws Exception {
        JsonNode responseNode = objectMapper.readTree(response);

        if (responseNode.get("code").asInt() != 0) {
            String errorMsg = responseNode.get("msg").asText();
            throw new RuntimeException("获取用户访问令牌失败: " + errorMsg);
        }

        JsonNode dataNode = responseNode.get("data");
        if (dataNode == null) {
            throw new RuntimeException("响应中缺少用户令牌数据");
        }

        FeishuUserTokenDto userToken = new FeishuUserTokenDto();
        userToken.setAccessToken(dataNode.get("access_token").asText());
        userToken.setTokenType("Bearer");
        userToken.setExpiresIn(dataNode.get("expires_in").asInt());

        if (dataNode.has("refresh_token")) {
            userToken.setRefreshToken(dataNode.get("refresh_token").asText());
        }

        if (dataNode.has("scope")) {
            userToken.setScope(dataNode.get("scope").asText());
        }

        if (dataNode.has("open_id")) {
            userToken.setOpenId(dataNode.get("open_id").asText());
        }

        if (dataNode.has("user_id")) {
            userToken.setUserId(dataNode.get("user_id").asText());
        }

        userToken.setTimestamp(System.currentTimeMillis() / 1000);

        log.info("成功解析用户访问令牌，有效期: {} 秒", userToken.getExpiresIn());
        return userToken;
    }

    /**
     * 解析用户信息响应
     */
    private FeishuUserInfoDto parseUserInfoResponse(String response) throws Exception {
        JsonNode responseNode = objectMapper.readTree(response);

        if (responseNode.get("code").asInt() != 0) {
            String errorMsg = responseNode.get("msg").asText();
            throw new RuntimeException("获取用户信息失败: " + errorMsg);
        }

        JsonNode dataNode = responseNode.get("data");
        if (dataNode == null) {
            throw new RuntimeException("响应中缺少用户信息数据");
        }

        FeishuUserInfoDto userInfo = new FeishuUserInfoDto();

        if (dataNode.has("user_id")) {
            userInfo.setUserId(dataNode.get("user_id").asText());
        }

        if (dataNode.has("open_id")) {
            userInfo.setOpenId(dataNode.get("open_id").asText());
        }

        if (dataNode.has("union_id")) {
            userInfo.setUnionId(dataNode.get("union_id").asText());
        }

        if (dataNode.has("name")) {
            userInfo.setName(dataNode.get("name").asText());
        }

        if (dataNode.has("en_name")) {
            userInfo.setEnName(dataNode.get("en_name").asText());
        }

        if (dataNode.has("email")) {
            userInfo.setEmail(dataNode.get("email").asText());
        }

        if (dataNode.has("mobile")) {
            userInfo.setMobile(dataNode.get("mobile").asText());
        }

        if (dataNode.has("avatar_url")) {
            userInfo.setAvatarUrl(dataNode.get("avatar_url").asText());
        }

        if (dataNode.has("avatar_thumb")) {
            userInfo.setAvatarThumb(dataNode.get("avatar_thumb").asText());
        }

        if (dataNode.has("avatar_middle")) {
            userInfo.setAvatarMiddle(dataNode.get("avatar_middle").asText());
        }

        if (dataNode.has("avatar_big")) {
            userInfo.setAvatarBig(dataNode.get("avatar_big").asText());
        }

        if (dataNode.has("tenant_key")) {
            userInfo.setTenantKey(dataNode.get("tenant_key").asText());
        }

        if (dataNode.has("employee_id")) {
            userInfo.setEmployeeId(dataNode.get("employee_id").asText());
        }

        if (dataNode.has("employee_no")) {
            userInfo.setEmployeeNo(dataNode.get("employee_no").asText());
        }

        if (dataNode.has("work_station")) {
            userInfo.setWorkStation(dataNode.get("work_station").asText());
        }

        if (dataNode.has("country")) {
            userInfo.setCountry(dataNode.get("country").asText());
        }

        if (dataNode.has("time_zone")) {
            userInfo.setTimeZone(dataNode.get("time_zone").asText());
        }

        log.info("成功解析用户信息 - 用户名: {}, 邮箱: {}", userInfo.getName(), userInfo.getEmail());
        return userInfo;
    }

    @Override
    public String getDocumentContentByToken(String docToken) {
        String docType ="doccn";
        try {
            log.info("根据docToken获取文档内容 - docToken: {}", docToken);

            if (docToken == null || docToken.trim().isEmpty()) {
                throw new IllegalArgumentException("docToken不能为空");
            }

            // 检查配置状态
            if (!feishuConfig.isEnabled()) {
                log.warn("飞书API未启用，返回模拟内容");
                return generateMockContentByToken(docToken);
            }

            if (feishuConfig.getAppId() == null || feishuConfig.getAppId().isEmpty()) {
                log.warn("飞书App ID未配置，返回模拟内容");
                return generateMockContentByToken(docToken);
            }

            if (feishuConfig.getAppSecret() == null || feishuConfig.getAppSecret().isEmpty()) {
                log.warn("飞书App Secret未配置，返回模拟内容");
                return generateMockContentByToken(docToken);
            }

            // 根据docToken前缀确定文档类型和API端点
            String apiEndpoint = getApiEndpointByToken(docType);
            if (apiEndpoint == null) {
                throw new IllegalArgumentException("不支持的docToken格式: " + docToken);
            }

            // 获取访问令牌
            FeishuTokenDto token = getTenantAccessToken();

            // 构建请求URL
            String url = feishuConfig.getBaseUrl() + apiEndpoint.replace("{document_id}", docToken);
            log.info("请求URL: {}", url);

            // 发送请求
            String response = sendGetRequest(url, token.getAccessToken());
            log.info("收到响应，长度: {}", response.length());

            // 解析响应获取内容
            return parseDocumentContentResponse(response, docType);

        } catch (Exception e) {
            log.error("根据docToken获取文档内容失败，返回模拟内容", e);
            throw new RuntimeException("获取文档内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据docToken前缀确定API端点
     */
    private String getApiEndpointByToken(String docType) {
        if (docType.startsWith("doccn")) {
            // 飞书文档
            return "/open-apis/docx/v1/documents/{document_id}/raw_content";
        } else if (docType.startsWith("shtcn")) {
            // 飞书表格
            return "/open-apis/sheets/v1/spreadsheets/{document_id}/values_batch_get";
        } else if (docType.startsWith("sldcn")) {
            // 飞书演示文稿
            return "/open-apis/slides/v1/presentations/{document_id}";
        } else if (docType.startsWith("bmncn")) {
            // 思维笔记
            return "/open-apis/mindnote/v1/spaces/{document_id}/nodes";
        } else if (docType.startsWith("bascn")) {
            // 多维表格
            return "/open-apis/bitable/v1/apps/{document_id}/tables";
        } else if (docType.startsWith("wikicn")) {
            // 知识库
            return "/open-apis/wiki/v2/spaces/{document_id}/nodes";
        }

        return null;
    }

    /**
     * 解析文档内容响应
     */
    private String parseDocumentContentResponse(String response, String docToken) throws Exception {
        JsonNode responseNode = objectMapper.readTree(response);

        if (responseNode.get("code").asInt() != 0) {
            String errorMsg = responseNode.get("msg").asText();
            throw new RuntimeException("获取文档内容失败: " + errorMsg);
        }

        JsonNode dataNode = responseNode.get("data");
        if (dataNode == null) {
            throw new RuntimeException("响应中缺少文档内容数据");
        }

        // 根据文档类型解析不同的内容格式
        if (docToken.startsWith("doccn")) {
            // 飞书文档 - 直接返回raw_content
            JsonNode contentNode = dataNode.get("content");
            return contentNode != null ? contentNode.asText() : "";
        } else if (docToken.startsWith("shtcn")) {
            // 飞书表格 - 解析表格数据
            return parseSheetContentByToken(dataNode);
        } else if (docToken.startsWith("sldcn")) {
            // 飞书演示文稿 - 解析幻灯片内容
            return parseSlideContentByToken(dataNode);
        } else {
            // 其他类型 - 返回JSON格式
            return objectMapper.writeValueAsString(dataNode);
        }
    }

    /**
     * 解析表格内容（基于docToken）
     */
    private String parseSheetContentByToken(JsonNode dataNode) {
        StringBuilder content = new StringBuilder();
        content.append("# 飞书表格内容\n\n");

        if (dataNode.has("value_ranges")) {
            JsonNode valueRanges = dataNode.get("value_ranges");
            for (JsonNode range : valueRanges) {
                content.append("## 工作表: ").append(range.get("range").asText()).append("\n\n");

                if (range.has("values")) {
                    JsonNode values = range.get("values");
                    for (JsonNode row : values) {
                        content.append("| ");
                        for (JsonNode cell : row) {
                            content.append(cell.asText()).append(" | ");
                        }
                        content.append("\n");
                    }
                }
                content.append("\n");
            }
        }

        return content.toString();
    }

    /**
     * 解析演示文稿内容（基于docToken）
     */
    private String parseSlideContentByToken(JsonNode dataNode) {
        StringBuilder content = new StringBuilder();
        content.append("# 飞书演示文稿内容\n\n");

        if (dataNode.has("slides")) {
            JsonNode slides = dataNode.get("slides");
            int slideIndex = 1;
            for (JsonNode slide : slides) {
                content.append("## 幻灯片 ").append(slideIndex++).append("\n\n");

                if (slide.has("page_elements")) {
                    JsonNode elements = slide.get("page_elements");
                    for (JsonNode element : elements) {
                        if (element.has("text")) {
                            content.append(element.get("text").asText()).append("\n\n");
                        }
                    }
                }
            }
        }

        return content.toString();
    }

    /**
     * 生成基于docToken的模拟内容
     */
    private String generateMockContentByToken(String docToken) {
        StringBuilder content = new StringBuilder();
        content.append("# 飞书文档内容 (模拟)\n\n");
        content.append("**文档Token**: ").append(docToken).append("\n");
        content.append("**文档类型**: ").append(getDocTypeByToken(docToken)).append("\n\n");
        content.append("## 说明\n\n");
        content.append("这是基于docToken生成的模拟文档内容。在实际环境中，这里将显示从飞书API获取的真实文档内容。\n\n");
        content.append("当前docToken: `").append(docToken).append("`\n\n");
        content.append("要启用真实的飞书API集成，请：\n");
        content.append("1. 在飞书开放平台创建应用\n");
        content.append("2. 获取App ID和App Secret\n");
        content.append("3. 在application.yml中配置相关参数\n");
        content.append("4. 设置feishu.api.enabled=true\n\n");
        content.append("## 支持的文档类型\n\n");
        content.append("- **doccn**: 飞书文档\n");
        content.append("- **shtcn**: 飞书表格\n");
        content.append("- **sldcn**: 飞书演示文稿\n");
        content.append("- **bmncn**: 思维笔记\n");
        content.append("- **bascn**: 多维表格\n");
        content.append("- **wikicn**: 知识库\n\n");
        content.append("---\n\n");
        content.append("*生成时间: ").append(LocalDateTime.now()).append("*");

        return content.toString();
    }

    /**
     * 根据docToken获取文档类型描述
     */
    private String getDocTypeByToken(String docToken) {
        if (docToken.startsWith("doccn")) {
            return "飞书文档";
        } else if (docToken.startsWith("shtcn")) {
            return "飞书表格";
        } else if (docToken.startsWith("sldcn")) {
            return "飞书演示文稿";
        } else if (docToken.startsWith("bmncn")) {
            return "思维笔记";
        } else if (docToken.startsWith("bascn")) {
            return "多维表格";
        } else if (docToken.startsWith("wikicn")) {
            return "知识库";
        } else {
            return "未知类型";
        }
    }
}
