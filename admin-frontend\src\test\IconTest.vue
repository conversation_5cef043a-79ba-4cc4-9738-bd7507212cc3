<template>
  <div class="icon-test">
    <h2>图标测试</h2>
    <div class="icon-grid">
      <div class="icon-item">
        <el-icon><Refresh /></el-icon>
        <span>Refresh</span>
      </div>
      <div class="icon-item">
        <el-icon><Download /></el-icon>
        <span>Download</span>
      </div>
      <div class="icon-item">
        <el-icon><Search /></el-icon>
        <span>Search</span>
      </div>
      <div class="icon-item">
        <el-icon><SuccessFilled /></el-icon>
        <span>SuccessFilled</span>
      </div>
      <div class="icon-item">
        <el-icon><CircleCloseFilled /></el-icon>
        <span>CircleCloseFilled</span>
      </div>
      <div class="icon-item">
        <el-icon><Document /></el-icon>
        <span>Document</span>
      </div>
      <div class="icon-item">
        <el-icon><Grid /></el-icon>
        <span>Grid</span>
      </div>
      <div class="icon-item">
        <el-icon><Monitor /></el-icon>
        <span>Monitor</span>
      </div>
      <div class="icon-item">
        <el-icon><Histogram /></el-icon>
        <span>Histogram</span>
      </div>
      <div class="icon-item">
        <el-icon><Connection /></el-icon>
        <span>Connection</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { 
  Refresh, Download, Search, SuccessFilled, CircleCloseFilled,
  Document, Grid, Monitor, Histogram, Connection
} from '@element-plus/icons-vue'
</script>

<style scoped>
.icon-test {
  padding: 20px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 6px;
}

.icon-item span {
  font-size: 12px;
  color: #666;
}
</style>
