/* 平板浏览器优化样式 */

/* 基础变量 */
:root {
  /* 断点 */
  --tablet-min: 768px;
  --tablet-max: 1023px;
  --desktop-min: 1024px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
  
  /* 触摸目标 */
  --touch-target-min: 44px;
  --touch-target-comfortable: 54px;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

html {
  /* 防止iOS Safari缩放 */
  -webkit-text-size-adjust: 100%;
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

body {
  /* 防止橡皮筋效果 */
  overscroll-behavior: none;
  /* 优化触摸滚动 */
  -webkit-overflow-scrolling: touch;
  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 触摸优化 */
.touchable {
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.touchable:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 按钮优化 */
.btn-touch {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  border: none;
  background: #1989fa;
  color: white;
  font-size: var(--font-size-md);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  transition: all 0.2s ease;
}

.btn-touch:active {
  transform: scale(0.96);
  background: #1976d2;
}

.btn-touch:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-touch:disabled:active {
  transform: none;
}

/* 输入框优化 */
.input-touch {
  min-height: var(--touch-target-comfortable);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid #ddd;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  background: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.input-touch:focus {
  outline: none;
  border-color: #1989fa;
  box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.2);
}

/* 卡片组件 */
.card-tablet {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-tablet:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-tablet.clickable {
  cursor: pointer;
}

.card-tablet.clickable:active {
  transform: scale(0.98);
}

/* 列表优化 */
.list-tablet {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.list-item-tablet {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid #f0f0f0;
  min-height: var(--touch-target-comfortable);
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.list-item-tablet:last-child {
  border-bottom: none;
}

.list-item-tablet.clickable {
  cursor: pointer;
}

.list-item-tablet.clickable:active {
  background-color: #f5f5f5;
}

/* 网格布局 */
.grid-tablet {
  display: grid;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

/* 小平板 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .grid-tablet {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
  }
  
  .container-tablet {
    max-width: 768px;
    margin: 0 auto;
    padding: 0 var(--spacing-xl);
  }
  
  /* 字体大小调整 */
  .text-lg-tablet {
    font-size: var(--font-size-xl);
  }
  
  .text-xl-tablet {
    font-size: var(--font-size-xxl);
  }
}

/* 大平板 (1024px+) */
@media (min-width: 1024px) {
  .grid-tablet {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xxl);
    padding: var(--spacing-xxl);
  }
  
  .container-tablet {
    max-width: 1024px;
    margin: 0 auto;
    padding: 0 var(--spacing-xxl);
  }
  
  /* 更大的字体 */
  .text-lg-tablet {
    font-size: var(--font-size-xxl);
  }
  
  .text-xl-tablet {
    font-size: 28px;
  }
  
  /* 更大的按钮 */
  .btn-touch {
    min-height: 48px;
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
  }
  
  /* 更大的输入框 */
  .input-touch {
    min-height: 48px;
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
  }
}

/* 横屏优化 */
@media (orientation: landscape) and (max-height: 600px) {
  .popup-tablet {
    height: 95vh !important;
    max-height: 95vh !important;
  }
  
  .container-tablet {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .grid-tablet {
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #999999;
    --border-color: #404040;
  }
  
  .card-tablet {
    background: var(--bg-secondary);
    color: var(--text-primary);
  }
  
  .list-tablet {
    background: var(--bg-secondary);
  }
  
  .list-item-tablet {
    border-bottom-color: var(--border-color);
    color: var(--text-primary);
  }
  
  .list-item-tablet.clickable:active {
    background-color: var(--bg-tertiary);
  }
  
  .input-touch {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .btn-touch {
    border: 2px solid currentColor;
  }
  
  .input-touch {
    border-width: 2px;
  }
  
  .card-tablet {
    border: 1px solid #ccc;
  }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .touchable:active,
  .btn-touch:active,
  .card-tablet.clickable:active {
    transform: none;
  }
  
  html {
    scroll-behavior: auto;
  }
}

/* 工具类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  outline: 2px solid #1989fa;
  outline-offset: 2px;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
