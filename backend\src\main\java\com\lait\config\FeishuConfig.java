package com.lait.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 飞书API配置
 */
@Configuration
@ConfigurationProperties(prefix = "feishu.api")
@Data
public class FeishuConfig {

    /**
     * 飞书应用ID
     */
    private String appId;

    /**
     * 飞书应用密钥
     */
    private String appSecret;

    /**
     * 飞书API基础URL
     */
    private String baseUrl = "https://open.feishu.cn";

    /**
     * 获取租户访问令牌的URL
     */
    private String tenantTokenUrl = "/open-apis/auth/v3/tenant_access_token/internal";

    /**
     * 获取应用访问令牌的URL
     */
    private String appTokenUrl = "/open-apis/auth/v3/app_access_token/internal";

    /**
     * 获取用户访问令牌的URL
     */
    private String userTokenUrl = "/open-apis/authen/v1/access_token";

    /**
     * OAuth授权URL
     */
    private String authUrl = "/open-apis/authen/v1/authorize";

    /**
     * 获取文件列表的URL
     */
    private String fileListUrl = "/open-apis/drive/v1/files";

    /**
     * 获取文件内容的URL
     */
    private String fileContentUrl = "/open-apis/docx/v1/documents/{document_id}/raw_content";

    /**
     * 获取文件元信息的URL
     */
    private String fileMetaUrl = "/open-apis/drive/v1/metas/batch_query";

    /**
     * 获取用户信息的URL
     */
    private String userInfoUrl = "/open-apis/authen/v1/user_info";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;

    /**
     * 令牌缓存时间（秒）
     */
    private int tokenCacheTime = 7200;

    /**
     * 是否启用飞书API
     */
    private boolean enabled = false;

    /**
     * 默认文件夹ID（个人空间根目录）
     */
    private String defaultFolderId = "";

    /**
     * 每页获取的文件数量
     */
    private int pageSize = 50;

    /**
     * 支持的文件类型
     */
    private String[] supportedTypes = {
        "doc", "docx", "sheet", "bitable", "mindnote", "slides"
    };

    /**
     * OAuth重定向URI
     */
    private String redirectUri = "http://localhost:3000/feishu-oauth/callback";

    /**
     * OAuth授权范围
     */
    private String[] scopes = {
        "drive:drive:readonly",  // 云文档只读权限
        "contact:user.id:readonly",  // 用户ID只读权限
        "contact:user.base:readonly"  // 用户基本信息只读权限
    };

    /**
     * 用户令牌缓存时间（秒）
     */
    private int userTokenCacheTime = 7200;
}
