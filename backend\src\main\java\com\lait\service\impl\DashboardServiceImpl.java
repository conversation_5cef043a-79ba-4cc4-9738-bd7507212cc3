package com.lait.service.impl;

import com.lait.entity.FeishuDocument;
import com.lait.entity.Subject;
import com.lait.entity.User;
import com.lait.repository.FeishuDocumentRepository;
import com.lait.repository.SubjectRepository;
import com.lait.repository.UserRepository;
import com.lait.service.DashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪表盘服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DashboardServiceImpl implements DashboardService {

    private final UserRepository userRepository;
    private final SubjectRepository subjectRepository;
    private final FeishuDocumentRepository feishuDocumentRepository;

    @Override
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 基础统计
            long userCount = userRepository.count();
            long subjectCount = subjectRepository.count();
            long documentCount = feishuDocumentRepository.count();

            stats.put("userCount", userCount);
            stats.put("subjectCount", subjectCount);
            stats.put("documentCount", documentCount);
            stats.put("questionCount", 0); // 暂时设为0，等题目模块完成后再实现

            log.info("仪表盘基础统计 - 用户: {}, 学科: {}, 文档: {}", userCount, subjectCount, documentCount);

        } catch (Exception e) {
            log.error("获取仪表盘统计数据失败", e);
            // 返回默认值
            stats.put("userCount", 0);
            stats.put("subjectCount", 0);
            stats.put("documentCount", 0);
            stats.put("questionCount", 0);
        }

        return stats;
    }

    @Override
    public Map<String, Object> getRecentActivities() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> activities = new ArrayList<>();

        try {
            // 获取最近的用户注册活动
            List<User> recentUsers = userRepository.findTop5ByOrderByCreatedTimeDesc();
            for (User user : recentUsers) {
                Map<String, Object> activity = new HashMap<>();
                activity.put("id", "user_" + user.getId());
                activity.put("type", "primary");
                activity.put("content", "新用户 " + user.getUsername() + " 注册了账号");
                activity.put("time", formatDateTime(user.getCreatedTime()));
                activities.add(activity);
            }

            // 获取最近的文档创建活动
            List<FeishuDocument> recentDocs = feishuDocumentRepository.findTop5ByOrderByCreatedTimeDesc();
            for (FeishuDocument doc : recentDocs) {
                Map<String, Object> activity = new HashMap<>();
                activity.put("id", "doc_" + doc.getId());
                activity.put("type", "success");
                activity.put("content", "创建了文档 " + doc.getTitle());
                activity.put("time", formatDateTime(doc.getCreatedTime()));
                activities.add(activity);
            }

            // 按时间排序
            activities.sort((a, b) -> {
                String timeA = (String) a.get("time");
                String timeB = (String) b.get("time");
                return timeB.compareTo(timeA);
            });

            // 只保留最近10条
            if (activities.size() > 10) {
                activities = activities.subList(0, 10);
            }

        } catch (Exception e) {
            log.error("获取最近活动失败", e);
        }

        result.put("activities", activities);
        return result;
    }

    @Override
    public Map<String, Object> getUserRegistrationTrend(Integer days) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> trendData = new ArrayList<>();

        try {
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(days);

            // 获取指定时间范围内的用户注册数据
            List<User> users = userRepository.findByCreatedTimeBetween(startDate, endDate);

            // 按日期分组统计
            Map<String, Long> dailyCount = users.stream()
                .collect(Collectors.groupingBy(
                    user -> user.getCreatedTime().toLocalDate().toString(),
                    Collectors.counting()
                ));

            // 生成完整的日期序列
            for (int i = 0; i < days; i++) {
                LocalDateTime date = startDate.plusDays(i);
                String dateStr = date.toLocalDate().toString();

                Map<String, Object> dataPoint = new HashMap<>();
                dataPoint.put("date", dateStr);
                dataPoint.put("count", dailyCount.getOrDefault(dateStr, 0L));
                trendData.add(dataPoint);
            }

        } catch (Exception e) {
            log.error("获取用户注册趋势失败", e);
        }

        result.put("trendData", trendData);
        return result;
    }

    @Override
    public Map<String, Object> getSubjectDistribution() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> distribution = new ArrayList<>();

        try {
            List<Subject> subjects = subjectRepository.findAll();

            for (Subject subject : subjects) {
                // 统计每个学科的文档数量
                long docCount = feishuDocumentRepository.countBySubjectId(subject.getId());

                Map<String, Object> item = new HashMap<>();
                item.put("subjectId", subject.getId());
                item.put("subjectName", subject.getName());
                item.put("count", docCount);
                distribution.add(item);
            }

            // 按文档数量排序
            distribution.sort((a, b) -> {
                Long countA = (Long) a.get("count");
                Long countB = (Long) b.get("count");
                return countB.compareTo(countA);
            });

        } catch (Exception e) {
            log.error("获取学科分布失败", e);
        }

        result.put("distribution", distribution);
        return result;
    }

    @Override
    public Map<String, Object> getQuestionStats() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 暂时返回模拟数据，等题目模块完成后再实现
            result.put("totalQuestions", 0);
            result.put("easyQuestions", 0);
            result.put("mediumQuestions", 0);
            result.put("hardQuestions", 0);
            result.put("recentQuestions", new ArrayList<>());

        } catch (Exception e) {
            log.error("获取题目统计失败", e);
        }

        return result;
    }

    @Override
    public Map<String, Object> getNoteStats() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 暂时返回模拟数据，等笔记模块完成后再实现
            result.put("totalNotes", 0);
            result.put("publicNotes", 0);
            result.put("privateNotes", 0);
            result.put("recentNotes", new ArrayList<>());

        } catch (Exception e) {
            log.error("获取笔记统计失败", e);
        }

        return result;
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
