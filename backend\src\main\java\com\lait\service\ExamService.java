package com.lait.service;

import com.lait.entity.Exam;
import com.lait.entity.ExamQuestion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 考试服务接口
 */
public interface ExamService {

    // ========== 考试管理 ==========
    
    /**
     * 创建考试
     */
    Exam createExam(Exam exam);

    /**
     * 更新考试
     */
    Exam updateExam(Long id, Exam exam);

    /**
     * 删除考试
     */
    void deleteExam(Long id);

    /**
     * 根据ID获取考试
     */
    Exam getExamById(Long id);

    /**
     * 分页查询考试
     */
    Page<Exam> getExams(String title, Long subjectId, Exam.ExamType examType, 
                       Exam.ExamStatus status, Long creatorId, Pageable pageable);

    /**
     * 根据学科获取考试
     */
    List<Exam> getExamsBySubject(Long subjectId);

    /**
     * 根据创建者获取考试
     */
    List<Exam> getExamsByCreator(Long creatorId);

    /**
     * 获取正在进行的考试
     */
    List<Exam> getOngoingExams();

    /**
     * 获取即将开始的考试
     */
    List<Exam> getUpcomingExams(int hours);

    // ========== 考试题目管理 ==========
    
    /**
     * 添加题目到考试
     */
    void addQuestionToExam(Long examId, Long questionId, Integer points, Integer order);

    /**
     * 从考试中移除题目
     */
    void removeQuestionFromExam(Long examId, Long questionId);

    /**
     * 获取考试题目列表
     */
    List<ExamQuestion> getExamQuestions(Long examId);

    /**
     * 批量添加题目到考试
     */
    void addQuestionsToExam(Long examId, List<Long> questionIds);

    /**
     * 更新考试题目顺序
     */
    void updateQuestionOrder(Long examId, Map<Long, Integer> questionOrders);

    // ========== 考试状态管理 ==========
    
    /**
     * 发布考试
     */
    void publishExam(Long id);

    /**
     * 开始考试
     */
    void startExam(Long id);

    /**
     * 结束考试
     */
    void endExam(Long id);

    /**
     * 取消考试
     */
    void cancelExam(Long id);

    /**
     * 自动处理考试状态
     */
    void autoProcessExamStatus();

    // ========== 考试参与管理 ==========
    
    /**
     * 学生参加考试
     */
    Map<String, Object> joinExam(Long examId, Long studentId);

    /**
     * 提交考试答案
     */
    Map<String, Object> submitExam(Long examId, Long studentId, Map<Long, String> answers);

    /**
     * 获取考试参与者列表
     */
    List<Map<String, Object>> getExamParticipants(Long examId);

    /**
     * 检查学生是否可以参加考试
     */
    boolean canStudentJoinExam(Long examId, Long studentId);

    // ========== 考试统计 ==========
    
    /**
     * 获取考试统计信息
     */
    Map<String, Object> getExamStatistics();

    /**
     * 获取考试详细统计
     */
    Map<String, Object> getExamDetailStatistics(Long examId);

    /**
     * 获取用户考试统计
     */
    Map<String, Object> getUserExamStatistics(Long userId);

    /**
     * 获取热门考试
     */
    List<Exam> getPopularExams(int limit);

    /**
     * 获取最近创建的考试
     */
    List<Exam> getRecentExams(int limit);

    // ========== 考试模板 ==========
    
    /**
     * 创建考试模板
     */
    Exam createExamTemplate(Exam exam);

    /**
     * 从模板创建考试
     */
    Exam createExamFromTemplate(Long templateId, Exam examInfo);

    /**
     * 获取考试模板列表
     */
    List<Exam> getExamTemplates();

    // ========== 考试导入导出 ==========
    
    /**
     * 导出考试配置
     */
    String exportExamConfig(Long examId);

    /**
     * 导入考试配置
     */
    Exam importExamConfig(String config);

    /**
     * 批量导入考试
     */
    List<Exam> batchImportExams(String config);
}
