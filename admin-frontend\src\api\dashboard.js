import request from './request'

// 获取仪表盘统计数据
export function getDashboardStats() {
  return request({
    url: '/dashboard/stats',
    method: 'get'
  })
}

// 获取最近活动
export function getRecentActivities() {
  return request({
    url: '/dashboard/activities',
    method: 'get'
  })
}

// 获取用户注册趋势数据
export function getUserRegistrationTrend(days = 30) {
  return request({
    url: '/dashboard/user-trend',
    method: 'get',
    params: { days }
  })
}

// 获取学科分布数据
export function getSubjectDistribution() {
  return request({
    url: '/dashboard/subject-distribution',
    method: 'get'
  })
}

// 获取题目统计数据
export function getQuestionStats() {
  return request({
    url: '/dashboard/question-stats',
    method: 'get'
  })
}

// 获取笔记统计数据
export function getNoteStats() {
  return request({
    url: '/dashboard/note-stats',
    method: 'get'
  })
}
