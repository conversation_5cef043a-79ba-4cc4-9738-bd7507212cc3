# Coze工作流管理系统

## 功能概述

Coze工作流管理系统是一个完整的AI工作流管理平台，支持创建、编辑、执行和监控Coze AI工作流。系统提供了直观的Web界面，让用户可以轻松管理各种类型的AI工作流。

## 主要特性

### 1. 工作流管理
- **创建工作流**: 支持多种工作流类型（题目生成、内容分析、学习指导等）
- **编辑工作流**: 可视化编辑工作流配置、输入输出参数
- **状态管理**: 支持草稿、激活、停用、测试、归档等状态
- **版本控制**: 自动管理工作流版本，配置变更时自动递增版本号

### 2. 执行功能
- **同步执行**: 立即执行工作流并返回结果
- **异步执行**: 后台执行工作流，适合长时间运行的任务
- **测试模式**: 验证工作流配置而不保存执行记录
- **参数配置**: 支持表单模式和JSON模式输入参数

### 3. 监控统计
- **执行统计**: 总执行次数、成功率、平均执行时间等
- **实时监控**: 查看正在执行的工作流状态
- **历史记录**: 完整的执行历史和详细日志
- **性能分析**: 执行时间趋势和性能指标

### 4. 导入导出
- **配置导出**: 将工作流配置导出为JSON文件
- **批量导入**: 从JSON文件或模板快速创建工作流
- **模板库**: 预置常用工作流模板

## 系统架构

### 后端架构
```
Controller Layer (CozeWorkflowController)
    ↓
Service Layer (CozeWorkflowService)
    ↓
Repository Layer (CozeWorkflowRepository)
    ↓
Database (MySQL)
```

### 前端架构
```
Views (CozeWorkflow.vue)
    ↓
Components (WorkflowFormDialog, ExecuteWorkflowDialog, etc.)
    ↓
API Layer (cozeWorkflow.js)
    ↓
Backend APIs
```

## 数据模型

### 工作流实体 (CozeWorkflow)
- **基本信息**: 名称、描述、类型、状态
- **配置信息**: 工作流配置、输入输出参数配置
- **统计信息**: 执行次数、成功率、平均执行时间
- **元数据**: 创建者、分类、标签、版本

### 执行记录实体 (CozeWorkflowExecution)
- **执行信息**: 执行ID、状态、开始/结束时间
- **数据信息**: 输入参数、输出结果、错误信息
- **追踪信息**: 执行者、执行类型、触发方式

## API接口

### 工作流管理
- `GET /admin/coze-workflows` - 分页查询工作流
- `POST /admin/coze-workflows` - 创建工作流
- `PUT /admin/coze-workflows/{id}` - 更新工作流
- `DELETE /admin/coze-workflows/{id}` - 删除工作流
- `GET /admin/coze-workflows/{id}` - 获取工作流详情

### 工作流执行
- `POST /admin/coze-workflows/{id}/execute` - 同步执行工作流
- `POST /admin/coze-workflows/{id}/execute-async` - 异步执行工作流
- `POST /admin/coze-workflows/{id}/test` - 测试工作流

### 状态管理
- `POST /admin/coze-workflows/{id}/activate` - 激活工作流
- `POST /admin/coze-workflows/{id}/deactivate` - 停用工作流
- `POST /admin/coze-workflows/{id}/archive` - 归档工作流

### 导入导出
- `POST /admin/coze-workflows/import` - 导入工作流
- `GET /admin/coze-workflows/{id}/export` - 导出工作流
- `POST /admin/coze-workflows/{id}/duplicate` - 复制工作流

### 执行记录
- `GET /admin/coze-workflows/{id}/executions` - 获取执行记录
- `GET /admin/coze-workflows/executions/{executionId}` - 获取执行详情
- `POST /admin/coze-workflows/executions/{executionId}/cancel` - 取消执行
- `POST /admin/coze-workflows/executions/{executionId}/retry` - 重新执行

### 统计分析
- `GET /admin/coze-workflows/statistics` - 获取总体统计
- `GET /admin/coze-workflows/{id}/statistics` - 获取工作流统计
- `GET /admin/coze-workflows/popular` - 获取热门工作流
- `GET /admin/coze-workflows/recent` - 获取最近执行的工作流

## 使用指南

### 1. 创建工作流

1. 点击"新建工作流"按钮
2. 填写基本信息：名称、类型、描述等
3. 配置工作流参数：
   - **基础配置**: Coze工作流的核心配置
   - **输入配置**: 定义工作流的输入参数
   - **输出配置**: 定义工作流的输出格式
4. 选择关联的Token（可选）
5. 设置是否公开
6. 点击"创建"保存

### 2. 执行工作流

1. 在工作流列表中找到要执行的工作流
2. 点击"执行"按钮
3. 选择执行模式：
   - **同步执行**: 等待执行完成并显示结果
   - **异步执行**: 后台执行，返回执行ID
   - **测试模式**: 仅验证配置，不保存记录
4. 输入参数：
   - **表单模式**: 根据输入配置自动生成表单
   - **JSON模式**: 直接输入JSON格式参数
5. 点击"执行"开始运行

### 3. 监控执行

1. 在工作流详情页面查看执行统计
2. 查看最近执行记录列表
3. 点击执行记录查看详细信息：
   - 执行状态和时间
   - 输入输出数据
   - 错误信息（如果有）
4. 对失败的执行可以进行重试

### 4. 导入导出

#### 导出工作流
1. 在工作流列表中选择要导出的工作流
2. 点击"更多" → "导出"
3. 系统会下载JSON配置文件

#### 导入工作流
1. 点击"导入工作流"按钮
2. 选择导入方式：
   - **文件导入**: 上传JSON配置文件
   - **JSON导入**: 直接粘贴JSON内容
   - **模板导入**: 从预置模板创建
3. 预览导入内容
4. 点击"导入"完成

## 工作流类型

### 1. 题目生成 (QUESTION_GENERATION)
- **用途**: 基于学科和难度自动生成题目
- **输入**: 学科、难度级别、题目数量
- **输出**: 生成的题目列表

### 2. 内容分析 (CONTENT_ANALYSIS)
- **用途**: 分析学习内容和学生表现
- **输入**: 学习内容、学生数据
- **输出**: 分析结果和建议

### 3. 学习指导 (STUDY_GUIDANCE)
- **用途**: 为学生提供个性化学习指导
- **输入**: 学生档案、学习目标
- **输出**: 学习指导和计划

### 4. 答案解释 (ANSWER_EXPLANATION)
- **用途**: 为题目提供详细的答案解释
- **输入**: 题目内容、答案
- **输出**: 详细解释

### 5. 表现分析 (PERFORMANCE_ANALYSIS)
- **用途**: 分析学生的学习表现
- **输入**: 学习记录、成绩数据
- **输出**: 表现分析报告

### 6. 自定义 (CUSTOM)
- **用途**: 用户自定义的工作流类型
- **配置**: 完全由用户定义

## 最佳实践

### 1. 工作流设计
- 明确定义输入输出参数
- 使用描述性的名称和标签
- 合理设置工作流分类
- 定期更新和维护工作流

### 2. 执行管理
- 对于长时间运行的任务使用异步执行
- 在生产环境使用前先进行测试
- 监控执行性能和成功率
- 及时处理失败的执行

### 3. 配置管理
- 使用版本控制管理配置变更
- 定期备份重要的工作流配置
- 使用模板提高创建效率
- 保持配置文档的更新

### 4. 性能优化
- 定期清理过期的执行记录
- 监控系统资源使用情况
- 优化工作流配置以提高执行效率
- 合理设置并发执行限制

## 故障排除

### 常见问题

1. **工作流执行失败**
   - 检查输入参数格式是否正确
   - 验证关联的Token是否有效
   - 查看错误日志获取详细信息

2. **配置验证失败**
   - 确保JSON格式正确
   - 检查必填字段是否完整
   - 验证参数类型是否匹配

3. **导入失败**
   - 检查文件格式是否为有效JSON
   - 确认配置结构符合要求
   - 验证工作流名称是否重复

### 日志查看
- 后端日志: 查看应用程序日志文件
- 前端日志: 打开浏览器开发者工具查看控制台
- 执行日志: 在执行记录详情中查看

## 技术支持

如需技术支持，请联系系统管理员或查看相关文档：
- API文档: `/swagger-ui.html`
- 系统日志: 应用程序日志目录
- 数据库: 查看相关表结构和数据
