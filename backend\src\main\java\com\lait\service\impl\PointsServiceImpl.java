package com.lait.service.impl;

import com.lait.entity.PointsConfig;
import com.lait.entity.UserPoints;
import com.lait.repository.PointsConfigRepository;
import com.lait.repository.UserPointsRepository;
import com.lait.service.PointsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 积分服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PointsServiceImpl implements PointsService {

    private final PointsConfigRepository pointsConfigRepository;
    private final UserPointsRepository userPointsRepository;

    // ========== 积分配置管理 ==========

    @Override
    @Transactional
    public PointsConfig createPointsConfig(PointsConfig config) {
        // 检查配置键是否已存在
        if (pointsConfigRepository.findByConfigKey(config.getConfigKey()).isPresent()) {
            throw new RuntimeException("配置键已存在: " + config.getConfigKey());
        }

        return pointsConfigRepository.save(config);
    }

    @Override
    @Transactional
    public PointsConfig updatePointsConfig(Long id, PointsConfig config) {
        PointsConfig existingConfig = getPointsConfigById(id);

        config.setId(id);
        config.setCreatedTime(existingConfig.getCreatedTime());

        return pointsConfigRepository.save(config);
    }

    @Override
    @Transactional
    public void deletePointsConfig(Long id) {
        PointsConfig config = getPointsConfigById(id);
        pointsConfigRepository.delete(config);
    }

    @Override
    public PointsConfig getPointsConfigById(Long id) {
        return pointsConfigRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("积分配置不存在: " + id));
    }

    @Override
    public PointsConfig getPointsConfigByKey(String configKey) {
        return pointsConfigRepository.findByConfigKey(configKey)
                .orElseThrow(() -> new RuntimeException("积分配置不存在: " + configKey));
    }

    @Override
    public Page<PointsConfig> getPointsConfigs(String displayName, PointsConfig.ConfigCategory category,
                                              Boolean enabled, Pageable pageable) {
        // 简化实现，实际应该在Repository中添加相应的查询方法
        return pointsConfigRepository.findAll(pageable);
    }

    @Override
    public List<PointsConfig> getEnabledPointsConfigs() {
        return pointsConfigRepository.findByEnabledTrueOrderBySortOrder();
    }

    @Override
    public List<PointsConfig> getPointsConfigsByCategory(PointsConfig.ConfigCategory category) {
        return pointsConfigRepository.findByCategory(category);
    }

    @Override
    @Transactional
    public void batchUpdatePointsConfigStatus(List<Long> ids, Boolean enabled) {
        for (Long id : ids) {
            PointsConfig config = getPointsConfigById(id);
            config.setEnabled(enabled);
            pointsConfigRepository.save(config);
        }
    }

    // ========== 用户积分管理 ==========

    @Override
    @Transactional
    public UserPoints addPoints(Long userId, String configKey, Long relatedId, String relatedType, String description) {
        PointsConfig config = getPointsConfigByKey(configKey);

        if (!config.getEnabled()) {
            throw new RuntimeException("积分配置已禁用: " + configKey);
        }

        // 检查今日是否可以获得积分
        if (!canEarnPointsToday(userId, configKey)) {
            throw new RuntimeException("今日已达到积分获取上限");
        }

        UserPoints userPoints = new UserPoints();
        userPoints.setUserId(userId);
        userPoints.setPointsType(UserPoints.PointsType.EARN);
        userPoints.setPointsChange(config.getPoints());
        userPoints.setSource(config.getConfigKey());
        userPoints.setRelatedId(relatedId);
        userPoints.setRelatedType(relatedType);
        userPoints.setDescription(description != null ? description : config.getDisplayName());

        return userPointsRepository.save(userPoints);
    }

    @Override
    @Transactional
    public UserPoints deductPoints(Long userId, Integer points, String source, Long relatedId, String relatedType, String description) {
        // 检查用户当前积分是否足够
        Integer currentPoints = getUserCurrentPoints(userId);
        if (currentPoints < points) {
            throw new RuntimeException("积分不足，当前积分: " + currentPoints + "，需要扣除: " + points);
        }

        UserPoints userPoints = new UserPoints();
        userPoints.setUserId(userId);
        userPoints.setPointsType(UserPoints.PointsType.SPEND);
        userPoints.setPointsChange(-points); // 负数表示扣除
        userPoints.setSource(source);
        userPoints.setRelatedId(relatedId);
        userPoints.setRelatedType(relatedType);
        userPoints.setDescription(description);

        return userPointsRepository.save(userPoints);
    }

    @Override
    public Integer getUserCurrentPoints(Long userId) {
        // 使用Repository中的方法获取用户当前积分
        Pageable pageable = PageRequest.of(0, 1);
        List<Integer> currentPointsList = userPointsRepository.getCurrentPointsByUserId(userId, pageable);
        return currentPointsList.isEmpty() ? 0 : currentPointsList.get(0);
    }

    @Override
    public Page<UserPoints> getUserPointsHistory(Long userId, Pageable pageable) {
        return userPointsRepository.findByUserIdOrderByCreatedTimeDesc(userId, pageable);
    }

    @Override
    public Page<UserPoints> getPointsRecords(Long userId, UserPoints.PointsType pointsType, String source,
                                            LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        // 简化实现，实际应该在Repository中添加相应的查询方法
        if (userId != null) {
            return userPointsRepository.findByUserIdOrderByCreatedTimeDesc(userId, pageable);
        } else {
            return userPointsRepository.findAll(pageable);
        }
    }

    @Override
    public Integer getUserTodayPointsChange(Long userId) {
        // 使用Repository中的方法获取今日积分变化
        Integer todayChange = userPointsRepository.sumTodayPointsChangeByUserId(userId);
        return todayChange != null ? todayChange : 0;
    }

    @Override
    public List<Map<String, Object>> getUserPointsTrend(Long userId, int days) {
        LocalDateTime startTime = LocalDate.now().minusDays(days - 1).atStartOfDay();
        List<Object[]> trendData = userPointsRepository.getPointsTrendByUserId(userId, startTime);

        List<Map<String, Object>> trend = new ArrayList<>();
        for (Object[] data : trendData) {
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", data[0].toString());
            dayData.put("change", data[1]);
            trend.add(dayData);
        }

        return trend;
    }

    @Override
    public List<Map<String, Object>> getPointsRanking(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Object[]> rankings = userPointsRepository.getPointsRanking(pageable);

        List<Map<String, Object>> result = new ArrayList<>();
        for (Object[] ranking : rankings) {
            Map<String, Object> userRanking = new HashMap<>();
            userRanking.put("userId", ranking[0]);
            userRanking.put("totalPoints", ranking[1]);
            result.add(userRanking);
        }

        return result;
    }

    // ========== 积分统计 ==========

    @Override
    public Map<String, Object> getPointsConfigStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总配置数
        stats.put("totalConfigs", pointsConfigRepository.count());

        // 启用的配置数
        stats.put("enabledConfigs", pointsConfigRepository.findByEnabledTrueOrderBySortOrder().size());

        // 按分类统计 (简化实现)
        Map<String, Long> categoryStats = new HashMap<>();
        categoryStats.put("LEARNING", 0L);
        categoryStats.put("PRACTICE", 0L);
        categoryStats.put("SOCIAL", 0L);
        categoryStats.put("ACHIEVEMENT", 0L);
        categoryStats.put("PENALTY", 0L);
        stats.put("categoryStats", categoryStats);

        return stats;
    }

    @Override
    public Map<String, Object> getPointsRecordStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总记录数
        stats.put("totalRecords", userPointsRepository.count());

        // 按类型统计 (简化实现)
        stats.put("earnRecords", userPointsRepository.findByPointsType(UserPoints.PointsType.EARN).size());
        stats.put("consumeRecords", userPointsRepository.findByPointsType(UserPoints.PointsType.SPEND).size());

        // 今日积分变化 (简化实现)
        stats.put("todayEarned", 0);
        stats.put("todayConsumed", 0);

        return stats;
    }

    @Override
    public Map<String, Object> getUserPointsStatistics(Long userId) {
        Map<String, Object> stats = new HashMap<>();

        stats.put("userId", userId);
        stats.put("currentPoints", getUserCurrentPoints(userId));
        stats.put("todayChange", getUserTodayPointsChange(userId));

        // 总获得积分 (简化实现)
        List<UserPoints> userPointsList = userPointsRepository.findByUserIdOrderByCreatedTimeDesc(userId);
        int totalEarned = userPointsList.stream()
                .filter(p -> p.getPointsChange() > 0)
                .mapToInt(UserPoints::getPointsChange)
                .sum();
        stats.put("totalEarned", totalEarned);

        // 总消费积分 (简化实现)
        int totalConsumed = Math.abs(userPointsList.stream()
                .filter(p -> p.getPointsChange() < 0)
                .mapToInt(UserPoints::getPointsChange)
                .sum());
        stats.put("totalConsumed", totalConsumed);

        return stats;
    }

    // ========== 积分规则 ==========

    @Override
    public boolean canEarnPointsToday(Long userId, String configKey) {
        PointsConfig config = getPointsConfigByKey(configKey);

        if (config.getDailyLimit() == null || config.getDailyLimit() <= 0) {
            return true; // 无限制
        }

        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = LocalDate.now().atTime(LocalTime.MAX);

        // 简化实现：计算今日该配置的使用次数
        Integer todayChange = userPointsRepository.sumPointsChangeByUserIdAndTimeRange(
                userId, startOfDay, endOfDay);

        // 简化判断：如果今日有积分变化，则认为已使用
        return todayChange == null || todayChange == 0;
    }

    @Override
    @Transactional
    public void initializeDefaultPointsConfig() {
        // 检查是否已初始化
        if (pointsConfigRepository.count() > 0) {
            return;
        }

        // 创建默认积分配置
        createDefaultConfig("LOGIN", "每日登录", 10, PointsConfig.ConfigCategory.LEARNING, 1);
        createDefaultConfig("COMPLETE_QUESTION", "完成题目", 5, PointsConfig.ConfigCategory.LEARNING, null);
        createDefaultConfig("CREATE_NOTE", "创建笔记", 3, PointsConfig.ConfigCategory.LEARNING, null);
        createDefaultConfig("SHARE_NOTE", "分享笔记", 2, PointsConfig.ConfigCategory.SOCIAL, 5);
    }

    @Override
    @Transactional
    public void resetUserPoints(Long userId, String reason) {
        // 记录重置操作
        UserPoints resetRecord = new UserPoints();
        resetRecord.setUserId(userId);
        resetRecord.setPointsType(UserPoints.PointsType.PENALTY);
        resetRecord.setPointsChange(-getUserCurrentPoints(userId)); // 扣除当前所有积分
        resetRecord.setSource("SYSTEM_RESET");
        resetRecord.setDescription("积分重置: " + reason);

        userPointsRepository.save(resetRecord);
    }

    @Override
    @Transactional
    public void batchAdjustUserPoints(List<Long> userIds, Integer points, String reason) {
        for (Long userId : userIds) {
            UserPoints adjustRecord = new UserPoints();
            adjustRecord.setUserId(userId);
            adjustRecord.setPointsType(points > 0 ? UserPoints.PointsType.BONUS : UserPoints.PointsType.PENALTY);
            adjustRecord.setPointsChange(points); // 直接使用points值，正负号已包含
            adjustRecord.setSource("SYSTEM_ADJUST");
            adjustRecord.setDescription("批量调整: " + reason);

            userPointsRepository.save(adjustRecord);
        }
    }

    /**
     * 创建默认积分配置
     */
    private void createDefaultConfig(String configKey, String displayName, Integer points,
                                   PointsConfig.ConfigCategory category, Integer dailyLimit) {
        PointsConfig config = new PointsConfig();
        config.setConfigKey(configKey);
        config.setDisplayName(displayName);
        config.setPoints(points);
        config.setCategory(category);
        config.setDailyLimit(dailyLimit);
        config.setEnabled(true);
        config.setDescription("系统默认配置");

        pointsConfigRepository.save(config);
    }
}
