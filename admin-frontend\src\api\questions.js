import request from './request'

// 获取题目列表
export function getQuestions(params) {
  return request({
    url: '/questions',
    method: 'get',
    params
  })
}

// 获取题目详情
export function getQuestion(id) {
  return request({
    url: `/questions/${id}`,
    method: 'get'
  })
}

// 创建题目
export function createQuestion(data) {
  return request({
    url: '/questions',
    method: 'post',
    data
  })
}

// 更新题目
export function updateQuestion(id, data) {
  return request({
    url: `/questions/${id}`,
    method: 'put',
    data
  })
}

// 删除题目
export function deleteQuestion(id) {
  return request({
    url: `/questions/${id}`,
    method: 'delete'
  })
}

// 批量删除题目
export function batchDeleteQuestions(ids) {
  return request({
    url: '/questions/batch',
    method: 'delete',
    data: { ids }
  })
}

// 根据学科获取题目
export function getQuestionsBySubject(subjectId, params) {
  return request({
    url: `/questions/subject/${subjectId}`,
    method: 'get',
    params
  })
}

// 获取题目统计信息
export function getQuestionStats() {
  return request({
    url: '/questions/stats',
    method: 'get'
  })
}
