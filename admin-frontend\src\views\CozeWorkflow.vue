<template>
  <div class="coze-workflow">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>Coze工作流管理</h1>
        <p>管理和执行AI工作流</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreateClick">
          新建工作流
        </el-button>
        <el-button @click="handleImportClick">
          导入工作流
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">总工作流</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.activeCount || 0 }}</div>
              <div class="stat-label">激活状态</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalExecutions || 0 }}</div>
              <div class="stat-label">总执行次数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.draftCount || 0 }}</div>
              <div class="stat-label">草稿状态</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 工作流列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="workflows"
        style="width: 100%"
      >
        <el-table-column prop="name" label="工作流名称" min-width="200" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag>{{ getTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag>{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executionCount" label="执行次数" width="100" align="center" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewClick(row)">查看</el-button>
            <el-button size="small" @click="handleEditClick(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 模拟API导入（暂时不使用真实API）
const workflowTypes = [
  { value: 'QUESTION_GENERATION', label: '题目生成' },
  { value: 'CONTENT_ANALYSIS', label: '内容分析' },
  { value: 'STUDY_GUIDANCE', label: '学习指导' },
  { value: 'ANSWER_EXPLANATION', label: '答案解释' },
  { value: 'PERFORMANCE_ANALYSIS', label: '表现分析' },
  { value: 'CUSTOM', label: '自定义' }
]

const workflowStatuses = [
  { value: 'DRAFT', label: '草稿' },
  { value: 'ACTIVE', label: '激活' },
  { value: 'INACTIVE', label: '停用' },
  { value: 'TESTING', label: '测试中' },
  { value: 'ARCHIVED', label: '已归档' }
]

// 响应式数据
const loading = ref(false)
const workflows = ref([])
const statistics = ref({
  totalCount: 3,
  activeCount: 2,
  totalExecutions: 15,
  draftCount: 1
})

// 方法
const getTypeLabel = (type) => {
  const typeObj = workflowTypes.find(t => t.value === type)
  return typeObj ? typeObj.label : type
}

const getStatusLabel = (status) => {
  const statusObj = workflowStatuses.find(s => s.value === status)
  return statusObj ? statusObj.label : status
}

const handleCreateClick = () => {
  ElMessage.info('创建工作流功能开发中...')
}

const handleImportClick = () => {
  ElMessage.info('导入工作流功能开发中...')
}

const handleViewClick = (workflow) => {
  ElMessage.info(`查看工作流: ${workflow.name}`)
}

const handleEditClick = (workflow) => {
  ElMessage.info(`编辑工作流: ${workflow.name}`)
}

const loadMockData = () => {
  // 模拟数据
  workflows.value = [
    {
      id: 1,
      name: '题目生成工作流',
      type: 'QUESTION_GENERATION',
      status: 'ACTIVE',
      executionCount: 10
    },
    {
      id: 2,
      name: '内容分析工作流',
      type: 'CONTENT_ANALYSIS',
      status: 'ACTIVE',
      executionCount: 5
    },
    {
      id: 3,
      name: '学习指导工作流',
      type: 'STUDY_GUIDANCE',
      status: 'DRAFT',
      executionCount: 0
    }
  ]
}

// 生命周期
onMounted(() => {
  loadMockData()
})
</script>

<style scoped>
.coze-workflow {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-top: 20px;
}
</style>
