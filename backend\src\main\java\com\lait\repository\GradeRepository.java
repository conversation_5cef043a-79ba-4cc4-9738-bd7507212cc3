package com.lait.repository;

import com.lait.entity.Grade;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 成绩数据访问层
 */
@Repository
public interface GradeRepository extends JpaRepository<Grade, Long> {

    /**
     * 根据学生ID查找成绩
     */
    List<Grade> findByStudentId(Long studentId);

    /**
     * 根据学科ID查找成绩
     */
    List<Grade> findBySubjectId(Long subjectId);

    /**
     * 根据学生和学科查找成绩
     */
    List<Grade> findByStudentIdAndSubjectId(Long studentId, Long subjectId);

    /**
     * 根据状态查找成绩
     */
    List<Grade> findByStatus(Grade.GradeStatus status);

    /**
     * 分页查询成绩（排除已删除）
     */
    @Query("SELECT g FROM Grade g WHERE g.isDeleted = false")
    Page<Grade> findAllActive(Pageable pageable);

    /**
     * 根据学生分页查询成绩
     */
    @Query("SELECT g FROM Grade g WHERE g.isDeleted = false AND g.studentId = :studentId")
    Page<Grade> findByStudentIdAndNotDeleted(@Param("studentId") Long studentId, Pageable pageable);

    /**
     * 根据学科分页查询成绩
     */
    @Query("SELECT g FROM Grade g WHERE g.isDeleted = false AND g.subjectId = :subjectId")
    Page<Grade> findBySubjectIdAndNotDeleted(@Param("subjectId") Long subjectId, Pageable pageable);

    /**
     * 统计学生平均分
     */
    @Query("SELECT AVG(g.score) FROM Grade g WHERE g.isDeleted = false AND g.studentId = :studentId AND g.status = 'PUBLISHED'")
    Double getAverageScoreByStudent(@Param("studentId") Long studentId);

    /**
     * 统计学科平均分
     */
    @Query("SELECT AVG(g.score) FROM Grade g WHERE g.isDeleted = false AND g.subjectId = :subjectId AND g.status = 'PUBLISHED'")
    Double getAverageScoreBySubject(@Param("subjectId") Long subjectId);
}
