import request from './request'

// 获取我的笔记列表
export function getMyNotes(params) {
  return request({
    url: '/student/notes/my',
    method: 'get',
    params
  })
}

// 获取笔记详情
export function getNote(id) {
  return request({
    url: `/notes/${id}`,
    method: 'get'
  })
}

// 创建笔记
export function createNote(data) {
  return request({
    url: '/notes',
    method: 'post',
    data
  })
}

// 更新笔记
export function updateNote(id, data) {
  return request({
    url: `/notes/${id}`,
    method: 'put',
    data
  })
}

// 删除笔记
export function deleteNote(id) {
  return request({
    url: `/notes/${id}`,
    method: 'delete'
  })
}

// 根据学科获取笔记
export function getNotesBySubject(subjectId, params) {
  return request({
    url: `/student/notes/my/subject/${subjectId}`,
    method: 'get',
    params
  })
}

// 分享笔记
export function shareNote(id) {
  return request({
    url: `/notes/${id}/share`,
    method: 'put'
  })
}

// 取消分享笔记
export function unshareNote(id) {
  return request({
    url: `/notes/${id}/unshare`,
    method: 'put'
  })
}

// 获取我的笔记统计信息
export function getMyNoteStats() {
  return request({
    url: '/notes/my/stats',
    method: 'get'
  })
}

// 获取共享笔记
export function getSharedNotes(params) {
  return request({
    url: '/notes/shared',
    method: 'get',
    params
  })
}
