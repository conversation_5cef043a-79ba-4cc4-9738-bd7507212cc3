<template>
  <el-dialog
    v-model="dialogVisible"
    title="工作流详情"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="workflow" class="workflow-detail">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusTagType(workflow.status)">
              {{ getStatusLabel(workflow.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作流名称">{{ workflow.name }}</el-descriptions-item>
          <el-descriptions-item label="工作流ID">{{ workflow.workflowId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="getTypeTagType(workflow.type)">
              {{ getTypeLabel(workflow.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分类">{{ workflow.category || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建者">{{ workflow.creatorName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="版本">v{{ workflow.version || 1 }}</el-descriptions-item>
          <el-descriptions-item label="是否公开">
            <el-tag :type="workflow.isPublic ? 'success' : 'info'">
              {{ workflow.isPublic ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标签">
            <div v-if="workflow.tags" class="tags">
              <el-tag
                v-for="tag in workflow.tags.split(',')"
                :key="tag"
                size="small"
                style="margin-right: 5px"
              >
                {{ tag.trim() }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(workflow.createdTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="2">
            {{ formatDateTime(workflow.updatedTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ workflow.description || '无描述' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 执行统计 -->
      <el-card class="stats-card">
        <template #header>
          <span>执行统计</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ workflow.executionCount || 0 }}</div>
              <div class="stat-label">总执行次数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ workflow.successCount || 0 }}</div>
              <div class="stat-label">成功次数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ workflow.failureCount || 0 }}</div>
              <div class="stat-label">失败次数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getSuccessRate() }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-value">{{ workflow.avgExecutionTime || 0 }}ms</div>
              <div class="stat-label">平均执行时间</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-value">{{ formatDateTime(workflow.lastExecutedAt) }}</div>
              <div class="stat-label">最后执行时间</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 配置信息 -->
      <el-card class="config-card">
        <template #header>
          <span>配置信息</span>
        </template>
        
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="基础配置" name="config">
            <el-input
              :model-value="formatJson(workflow.config)"
              type="textarea"
              :rows="10"
              readonly
            />
          </el-tab-pane>
          
          <el-tab-pane label="输入配置" name="inputConfig">
            <el-input
              :model-value="formatJson(workflow.inputConfig)"
              type="textarea"
              :rows="10"
              readonly
            />
          </el-tab-pane>
          
          <el-tab-pane label="输出配置" name="outputConfig">
            <el-input
              :model-value="formatJson(workflow.outputConfig)"
              type="textarea"
              :rows="10"
              readonly
            />
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 最近执行记录 -->
      <el-card class="executions-card">
        <template #header>
          <div class="card-header">
            <span>最近执行记录</span>
            <el-button size="small" @click="loadExecutions">刷新</el-button>
          </div>
        </template>
        
        <el-table
          v-loading="executionsLoading"
          :data="executions"
          style="width: 100%"
          max-height="300"
        >
          <el-table-column prop="id" label="执行ID" width="80" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getExecutionStatusType(row.status)">
                {{ getExecutionStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="执行时长" width="100">
            <template #default="{ row }">
              {{ row.duration ? row.duration + 'ms' : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="executorName" label="执行者" width="120" />
          <el-table-column prop="startTime" label="开始时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="endTime" label="结束时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.endTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button size="small" @click="viewExecution(row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="editWorkflow">编辑工作流</el-button>
      </div>
    </template>

    <!-- 执行记录详情对话框 -->
    <el-dialog
      v-model="showExecutionDialog"
      title="执行记录详情"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="currentExecution" class="execution-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="执行ID">{{ currentExecution.id }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getExecutionStatusType(currentExecution.status)">
              {{ getExecutionStatusLabel(currentExecution.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行时长">
            {{ currentExecution.duration ? currentExecution.duration + 'ms' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="执行者">{{ currentExecution.executorName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="开始时间" :span="2">
            {{ formatDateTime(currentExecution.startTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间" :span="2">
            {{ formatDateTime(currentExecution.endTime) }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider>输入数据</el-divider>
        <el-input
          :model-value="formatJson(currentExecution.inputData)"
          type="textarea"
          :rows="6"
          readonly
        />

        <el-divider>输出数据</el-divider>
        <el-input
          :model-value="formatJson(currentExecution.outputData)"
          type="textarea"
          :rows="6"
          readonly
        />

        <div v-if="currentExecution.errorMessage" class="error-section">
          <el-divider>错误信息</el-divider>
          <el-alert
            :title="currentExecution.errorMessage"
            type="error"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExecutionDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getWorkflowExecutions,
  getExecution,
  workflowTypes,
  workflowStatuses,
  executionStatuses
} from '@/api/cozeWorkflow'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  workflow: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'edit'])

// 响应式数据
const activeTab = ref('config')
const executions = ref([])
const executionsLoading = ref(false)
const showExecutionDialog = ref(false)
const currentExecution = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const getTypeLabel = (type) => {
  const typeObj = workflowTypes.find(t => t.value === type)
  return typeObj ? typeObj.label : type
}

const getStatusLabel = (status) => {
  const statusObj = workflowStatuses.find(s => s.value === status)
  return statusObj ? statusObj.label : status
}

const getTypeTagType = (type) => {
  const typeMap = {
    'QUESTION_GENERATION': 'primary',
    'CONTENT_ANALYSIS': 'success',
    'STUDY_GUIDANCE': 'warning',
    'ANSWER_EXPLANATION': 'info',
    'PERFORMANCE_ANALYSIS': 'danger',
    'CUSTOM': ''
  }
  return typeMap[type] || ''
}

const getStatusTagType = (status) => {
  const statusMap = {
    'DRAFT': 'info',
    'ACTIVE': 'success',
    'INACTIVE': 'warning',
    'TESTING': 'primary',
    'ARCHIVED': 'danger'
  }
  return statusMap[status] || ''
}

const getExecutionStatusType = (status) => {
  const statusMap = {
    'PENDING': 'info',
    'RUNNING': 'primary',
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'CANCELLED': 'warning',
    'TIMEOUT': 'danger'
  }
  return statusMap[status] || ''
}

const getExecutionStatusLabel = (status) => {
  const statusObj = executionStatuses.find(s => s.value === status)
  return statusObj ? statusObj.label : status
}

const getSuccessRate = () => {
  if (!props.workflow || !props.workflow.executionCount) return 0
  const rate = (props.workflow.successCount / props.workflow.executionCount) * 100
  return Math.round(rate * 100) / 100
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatJson = (jsonStr) => {
  if (!jsonStr) return '{}'
  try {
    const obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return jsonStr
  }
}

const loadExecutions = async () => {
  if (!props.workflow) return
  
  executionsLoading.value = true
  try {
    const response = await getWorkflowExecutions(props.workflow.id, {
      page: 0,
      size: 10
    })
    if (response.data && response.data.content) {
      executions.value = response.data.content
    }
  } catch (error) {
    ElMessage.error('加载执行记录失败')
  } finally {
    executionsLoading.value = false
  }
}

const viewExecution = async (execution) => {
  try {
    const response = await getExecution(execution.id)
    if (response.data) {
      currentExecution.value = response.data
      showExecutionDialog.value = true
    }
  } catch (error) {
    ElMessage.error('加载执行记录详情失败')
  }
}

const editWorkflow = () => {
  emit('edit', props.workflow)
  handleClose()
}

const handleClose = () => {
  dialogVisible.value = false
  activeTab.value = 'config'
  executions.value = []
  showExecutionDialog.value = false
  currentExecution.value = null
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal && props.workflow) {
    loadExecutions()
  }
})
</script>

<style scoped>
.workflow-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.stats-card .stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.execution-detail {
  margin-bottom: 20px;
}

.error-section {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}
</style>
