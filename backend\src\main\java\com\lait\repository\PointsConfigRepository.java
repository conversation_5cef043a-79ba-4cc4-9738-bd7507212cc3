package com.lait.repository;

import com.lait.entity.PointsConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 积分配置数据访问层
 */
@Repository
public interface PointsConfigRepository extends JpaRepository<PointsConfig, Long> {

    /**
     * 根据配置键查找
     */
    Optional<PointsConfig> findByConfigKey(String configKey);

    /**
     * 根据分类查找配置列表
     */
    List<PointsConfig> findByCategory(PointsConfig.ConfigCategory category);

    /**
     * 查找启用的配置
     */
    List<PointsConfig> findByEnabledTrueOrderBySortOrder();

    /**
     * 根据分类查找启用的配置
     */
    List<PointsConfig> findByCategoryAndEnabledTrueOrderBySortOrder(PointsConfig.ConfigCategory category);

    /**
     * 分页查询配置
     */
    @Query("SELECT p FROM PointsConfig p WHERE " +
           "(:displayName IS NULL OR p.displayName LIKE %:displayName%) AND " +
           "(:category IS NULL OR p.category = :category) AND " +
           "(:enabled IS NULL OR p.enabled = :enabled) " +
           "ORDER BY p.category, p.sortOrder")
    Page<PointsConfig> findConfigsWithFilters(@Param("displayName") String displayName,
                                             @Param("category") PointsConfig.ConfigCategory category,
                                             @Param("enabled") Boolean enabled,
                                             Pageable pageable);

    /**
     * 统计各分类配置数量
     */
    @Query("SELECT p.category, COUNT(p) FROM PointsConfig p GROUP BY p.category")
    List<Object[]> countByCategory();

    /**
     * 检查配置键是否存在
     */
    boolean existsByConfigKey(String configKey);

    /**
     * 获取分类下的最大排序值
     */
    @Query("SELECT COALESCE(MAX(p.sortOrder), 0) FROM PointsConfig p WHERE p.category = :category")
    Integer getMaxSortOrderByCategory(@Param("category") PointsConfig.ConfigCategory category);
}
