package com.lait.controller;

import com.lait.dto.FeishuUserTokenDto;
import com.lait.dto.FeishuUserInfoDto;
import com.lait.service.FeishuApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 飞书OAuth授权控制器
 * 处理用户授权登录流程
 */
@RestController
@RequestMapping("/feishu-oauth")
@RequiredArgsConstructor
@Slf4j
public class FeishuOAuthController {

    private final FeishuApiService feishuApiService;

    /**
     * 生成授权URL
     */
    @GetMapping("/auth-url")
    public ResponseEntity<Map<String, Object>> generateAuthUrl() {
        try {
            log.info("生成飞书OAuth授权URL");
            
            // 生成随机state参数防止CSRF攻击
            String state = UUID.randomUUID().toString();
            
            String authUrl = feishuApiService.generateAuthUrl(state);
            
            Map<String, Object> result = new HashMap<>();
            result.put("authUrl", authUrl);
            result.put("state", state);
            result.put("success", true);
            
            log.info("成功生成授权URL");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("生成授权URL失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 处理授权回调
     */
    @PostMapping("/callback")
    public ResponseEntity<Map<String, Object>> handleCallback(
            @RequestBody Map<String, String> request) {
        try {
            String code = request.get("code");
            String state = request.get("state");
            
            log.info("处理飞书OAuth回调 - code: {}, state: {}", code, state);
            
            if (code == null || code.isEmpty()) {
                throw new RuntimeException("授权码不能为空");
            }
            
            // 获取用户访问令牌
            FeishuUserTokenDto userToken = feishuApiService.getUserAccessToken(code);
            
            // 获取用户信息
            FeishuUserInfoDto userInfo = feishuApiService.getUserInfo(userToken.getAccessToken());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("userToken", userToken);
            result.put("userInfo", userInfo);
            
            log.info("成功处理OAuth回调 - 用户: {}", userInfo.getName());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("处理OAuth回调失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 刷新用户访问令牌
     */
    @PostMapping("/refresh-token")
    public ResponseEntity<Map<String, Object>> refreshToken(
            @RequestBody Map<String, String> request) {
        try {
            String refreshToken = request.get("refreshToken");
            
            log.info("刷新用户访问令牌");
            
            if (refreshToken == null || refreshToken.isEmpty()) {
                throw new RuntimeException("刷新令牌不能为空");
            }
            
            FeishuUserTokenDto newToken = feishuApiService.refreshUserAccessToken(refreshToken);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("userToken", newToken);
            
            log.info("成功刷新用户访问令牌");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("刷新用户访问令牌失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 获取用户信息
     */
    @PostMapping("/user-info")
    public ResponseEntity<Map<String, Object>> getUserInfo(
            @RequestBody Map<String, String> request) {
        try {
            String userAccessToken = request.get("userAccessToken");
            
            log.info("获取用户信息");
            
            if (userAccessToken == null || userAccessToken.isEmpty()) {
                throw new RuntimeException("用户访问令牌不能为空");
            }
            
            FeishuUserInfoDto userInfo = feishuApiService.getUserInfo(userAccessToken);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("userInfo", userInfo);
            
            log.info("成功获取用户信息 - 用户: {}", userInfo.getName());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 验证用户访问令牌
     */
    @PostMapping("/validate-token")
    public ResponseEntity<Map<String, Object>> validateToken(
            @RequestBody Map<String, String> request) {
        try {
            String userAccessToken = request.get("userAccessToken");
            
            log.info("验证用户访问令牌");
            
            if (userAccessToken == null || userAccessToken.isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("valid", false);
                result.put("error", "令牌不能为空");
                return ResponseEntity.ok(result);
            }
            
            // 尝试获取用户信息来验证令牌
            FeishuUserInfoDto userInfo = feishuApiService.getUserInfo(userAccessToken);
            
            Map<String, Object> result = new HashMap<>();
            result.put("valid", true);
            result.put("userInfo", userInfo);
            
            log.info("用户访问令牌验证成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.warn("用户访问令牌验证失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("valid", false);
            result.put("error", e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取OAuth配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getOAuthConfig() {
        try {
            log.info("获取OAuth配置信息");
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "OAuth配置获取成功");
            
            // 不暴露敏感信息，只返回基本状态
            Map<String, Object> config = new HashMap<>();
            config.put("enabled", true);
            config.put("provider", "feishu");
            config.put("scopes", new String[]{
                "drive:drive:readonly",
                "contact:user.id:readonly", 
                "contact:user.base:readonly"
            });
            
            result.put("config", config);
            
            log.info("成功获取OAuth配置信息");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取OAuth配置信息失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
}
