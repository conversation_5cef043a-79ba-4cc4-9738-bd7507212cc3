package com.lait.controller;

import com.lait.entity.FeishuDocument;
import com.lait.repository.FeishuDocumentRepository;
import com.lait.service.FeishuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 飞书文档管理控制器
 */
@RestController
@RequestMapping("/admin/feishu")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
@Slf4j
public class FeishuController {

    private final FeishuService feishuService;
    private final FeishuDocumentRepository feishuDocumentRepository;

    // ========== 文档管理 ==========

    /**
     * 创建飞书文档记录
     */
    @PostMapping("/documents")
    public ResponseEntity<FeishuDocument> createDocument(@Valid @RequestBody FeishuDocument document) {
        FeishuDocument createdDocument = feishuService.createDocument(document);
        return ResponseEntity.ok(createdDocument);
    }

    /**
     * 更新飞书文档记录
     */
    @PutMapping("/documents/{id}")
    public ResponseEntity<FeishuDocument> updateDocument(@PathVariable Long id,
                                                        @Valid @RequestBody FeishuDocument document) {
        FeishuDocument updatedDocument = feishuService.updateDocument(id, document);
        return ResponseEntity.ok(updatedDocument);
    }

    /**
     * 删除飞书文档记录
     */
    @DeleteMapping("/documents/{id}")
    public ResponseEntity<Void> deleteDocument(@PathVariable Long id) {
        feishuService.deleteDocument(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取文档详情
     */
    @GetMapping("/documents/{id}")
    public ResponseEntity<FeishuDocument> getDocument(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        return ResponseEntity.ok(document);
    }

    /**
     * 根据文档ID获取文档
     */
    @GetMapping("/documents/doc/{docId}")
    public ResponseEntity<FeishuDocument> getDocumentByDocId(@PathVariable String docId) {
        FeishuDocument document = feishuService.getDocumentByDocId(docId);
        return ResponseEntity.ok(document);
    }

    /**
     * 分页查询文档
     */
    @GetMapping("/documents")
    public ResponseEntity<Page<FeishuDocument>> getDocuments(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) FeishuDocument.DocumentType docType,
            @RequestParam(required = false) FeishuDocument.DocumentStatus status,
            @RequestParam(required = false) Long creatorId,
            @RequestParam(required = false) Long subjectId,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        try {
            log.info("查询飞书文档列表 - 参数: title={}, docType={}, status={}, creatorId={}, subjectId={}, category={}, page={}, size={}",
                    title, docType, status, creatorId, subjectId, category, page, size);

            Pageable pageable = PageRequest.of(page, size);
            Page<FeishuDocument> documents = feishuService.getDocuments(title, docType, status,
                                                                       creatorId, subjectId, category, pageable);

            log.info("查询飞书文档列表成功 - 总数: {}, 当前页: {}, 页大小: {}",
                    documents.getTotalElements(), documents.getNumber(), documents.getSize());

            return ResponseEntity.ok(documents);
        } catch (Exception e) {
            log.error("查询飞书文档列表失败", e);
            throw new RuntimeException("查询文档列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 搜索文档
     */
    @GetMapping("/documents/search")
    public ResponseEntity<Page<FeishuDocument>> searchDocuments(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<FeishuDocument> documents = feishuService.searchDocuments(keyword, pageable);
        return ResponseEntity.ok(documents);
    }

    /**
     * 获取热门文档
     */
    @GetMapping("/documents/popular")
    public ResponseEntity<List<FeishuDocument>> getPopularDocuments(
            @RequestParam(defaultValue = "10") int limit) {
        List<FeishuDocument> documents = feishuService.getPopularDocuments(limit);
        return ResponseEntity.ok(documents);
    }

    /**
     * 获取最近更新的文档
     */
    @GetMapping("/documents/recent")
    public ResponseEntity<List<FeishuDocument>> getRecentlyUpdatedDocuments(
            @RequestParam(defaultValue = "10") int limit) {
        List<FeishuDocument> documents = feishuService.getRecentlyUpdatedDocuments(limit);
        return ResponseEntity.ok(documents);
    }

    // ========== 飞书API集成 ==========

    /**
     * 从飞书同步文档内容
     */
    @PostMapping("/documents/{id}/sync")
    public ResponseEntity<FeishuDocument> syncDocumentFromFeishu(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        FeishuDocument syncedDocument = feishuService.syncDocumentFromFeishu(document.getDocId());
        return ResponseEntity.ok(syncedDocument);
    }

    /**
     * 批量同步文档
     */
    @PostMapping("/documents/batch-sync")
    public ResponseEntity<Void> batchSyncDocuments(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> documentIds = (List<Long>) request.get("documentIds");
        feishuService.batchSyncDocuments(documentIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取飞书文档内容
     */
    @GetMapping("/documents/{id}/content")
    public ResponseEntity<FeishuDocument> getFeishuDocumentContent(@PathVariable Long id) {
        log.info("获取飞书文档内容 - 文档ID: {}", id);

        FeishuDocument document = feishuService.getDocumentById(id);

        return ResponseEntity.ok(document);

    }

    /**
     * 更新飞书文档内容
     */
    @PutMapping("/documents/{id}/content")
    public ResponseEntity<Map<String, Object>> updateFeishuDocumentContent(@PathVariable Long id,
                                                                          @RequestBody Map<String, String> request) {
        try {
            FeishuDocument document = feishuService.getDocumentById(id);
            String content = request.get("content");

            log.info("更新文档内容 - 文档ID: {}, 内容长度: {}", id, content != null ? content.length() : 0);

            boolean success = feishuService.updateFeishuDocumentContent(document.getDocId(), content);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "内容更新成功" : "内容更新失败");
            result.put("timestamp", LocalDateTime.now());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新文档内容失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
            result.put("timestamp", LocalDateTime.now());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 获取飞书文档信息
     */
    @GetMapping("/documents/{id}/info")
    public ResponseEntity<Map<String, Object>> getFeishuDocumentInfo(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        Map<String, Object> info = feishuService.getFeishuDocumentInfo(document.getDocId());
        return ResponseEntity.ok(info);
    }

    /**
     * 创建飞书文档
     */
    @PostMapping("/documents/create-feishu")
    public ResponseEntity<Map<String, Object>> createFeishuDocument(@RequestBody Map<String, Object> request) {
        String title = (String) request.get("title");
        String content = (String) request.get("content");
        FeishuDocument.DocumentType docType = FeishuDocument.DocumentType.valueOf((String) request.get("docType"));

        Map<String, Object> result = feishuService.createFeishuDocument(title, content, docType);
        return ResponseEntity.ok(result);
    }

    /**
     * 导入飞书文档
     */
    @PostMapping("/documents/import")
    public ResponseEntity<FeishuDocument> importFeishuDocument(@RequestBody Map<String, String> request) {
        String docUrl = request.get("docUrl");
        FeishuDocument document = feishuService.importFeishuDocument(docUrl);
        return ResponseEntity.ok(document);
    }

    /**
     * 批量导入飞书文档
     */
    @PostMapping("/documents/batch-import")
    public ResponseEntity<List<FeishuDocument>> batchImportFeishuDocuments(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<String> docUrls = (List<String>) request.get("docUrls");
        List<FeishuDocument> documents = feishuService.batchImportFeishuDocuments(docUrls);
        return ResponseEntity.ok(documents);
    }

    // ========== 文档操作 ==========

    /**
     * 增加文档查看次数
     */
    @PostMapping("/documents/{id}/view")
    public ResponseEntity<Map<String, Object>> incrementDocumentViewCount(@PathVariable Long id) {
        try {
            log.info("增加文档查看次数: {}", id);
            FeishuDocument document = feishuService.incrementViewCount(id);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("viewCount", document.getViewCount());

            log.info("文档查看次数增加成功: {} -> {}", id, document.getViewCount());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("增加文档查看次数失败: {}", id, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 自动同步文档
     */
    @PostMapping("/documents/auto-sync")
    public ResponseEntity<Void> autoSyncDocuments() {
        feishuService.autoSyncDocuments();
        return ResponseEntity.ok().build();
    }

    /**
     * 获取需要同步的文档
     */
    @GetMapping("/documents/need-sync")
    public ResponseEntity<List<FeishuDocument>> getDocumentsNeedingSync() {
        List<FeishuDocument> documents = feishuService.getDocumentsNeedingSync();
        return ResponseEntity.ok(documents);
    }

    // ========== 权限管理 ==========

    /**
     * 设置文档访问权限
     */
    @PutMapping("/documents/{id}/access-level")
    public ResponseEntity<Void> setDocumentAccess(@PathVariable Long id,
                                                 @RequestBody Map<String, String> request) {
        FeishuDocument.AccessLevel accessLevel = FeishuDocument.AccessLevel.valueOf(request.get("accessLevel"));
        feishuService.setDocumentAccess(id, accessLevel);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取文档权限
     */
    @GetMapping("/documents/{id}/permissions")
    public ResponseEntity<Map<String, Object>> getFeishuDocumentPermissions(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        Map<String, Object> permissions = feishuService.getFeishuDocumentPermissions(document.getDocId());
        return ResponseEntity.ok(permissions);
    }

    /**
     * 设置文档权限
     */
    @PutMapping("/documents/{id}/permissions")
    public ResponseEntity<Map<String, Object>> setFeishuDocumentPermissions(@PathVariable Long id,
                                                                           @RequestBody Map<String, Object> permissions) {
        FeishuDocument document = feishuService.getDocumentById(id);
        boolean success = feishuService.setFeishuDocumentPermissions(document.getDocId(), permissions);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        return ResponseEntity.ok(result);
    }

    // ========== 统计分析 ==========

    /**
     * 获取文档统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getDocumentStatistics() {
        try {
            log.info("获取飞书文档统计信息");
            Map<String, Object> stats = feishuService.getDocumentStatistics();
            log.info("获取飞书文档统计信息成功: {}", stats);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取飞书文档统计信息失败", e);
            throw new RuntimeException("获取统计信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取热门飞书文档
     */
    @GetMapping("/popular")
    public ResponseEntity<List<FeishuDocument>> getPopularDocuments(
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            log.info("获取热门飞书文档 - limit: {}", limit);

            List<FeishuDocument> popularDocs = feishuService.getPopularDocuments(limit);

            log.info("成功获取热门飞书文档，数量: {}", popularDocs.size());
            return ResponseEntity.ok(popularDocs);
        } catch (Exception e) {
            log.error("获取热门飞书文档失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 测试数据库连接和基本查询
     */
    @GetMapping("/test/connection")
    public ResponseEntity<Map<String, Object>> testConnection() {
        try {
            log.info("测试飞书文档数据库连接");

            Map<String, Object> result = new HashMap<>();

            // 测试基本查询
            long totalCount = feishuDocumentRepository.count();
            result.put("totalDocuments", totalCount);

            // 测试分页查询
            Pageable pageable = PageRequest.of(0, 5);
            Page<FeishuDocument> documents = feishuDocumentRepository.findAll(pageable);
            result.put("sampleDocuments", documents.getContent());

            // 创建分页信息Map (Java 8兼容方式)
            Map<String, Object> pageInfo = new HashMap<>();
            pageInfo.put("totalElements", documents.getTotalElements());
            pageInfo.put("totalPages", documents.getTotalPages());
            pageInfo.put("currentPage", documents.getNumber());
            pageInfo.put("size", documents.getSize());
            result.put("pageInfo", pageInfo);

            log.info("数据库连接测试成功 - 总文档数: {}", totalCount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 获取用户文档统计
     */
    @GetMapping("/statistics/users/{userId}")
    public ResponseEntity<Map<String, Object>> getUserDocumentStatistics(@PathVariable Long userId) {
        Map<String, Object> stats = feishuService.getUserDocumentStatistics(userId);
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取学科文档统计
     */
    @GetMapping("/statistics/subjects")
    public ResponseEntity<Map<String, Object>> getSubjectDocumentStatistics() {
        Map<String, Object> stats = feishuService.getSubjectDocumentStatistics();
        return ResponseEntity.ok(stats);
    }

    // ========== 飞书API集成 ==========

    /**
     * 测试飞书API连接
     */
    @GetMapping("/api/test-connection")
    public ResponseEntity<Map<String, Object>> testFeishuApiConnection() {
        try {
            log.info("测试飞书API连接");
            Map<String, Object> result = feishuService.testFeishuApiConnection();
            log.info("飞书API连接测试完成");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("飞书API连接测试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("connected", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 获取飞书个人空间文档列表
     */
    @GetMapping("/api/personal-documents")
    public ResponseEntity<Map<String, Object>> getFeishuPersonalDocuments(
            @RequestParam(required = false) String folderId,
            @RequestParam(required = false) String pageToken,
            @RequestParam(defaultValue = "50") Integer pageSize) {
        try {
            log.info("获取飞书个人空间文档列表 - folderId: {}, pageToken: {}, pageSize: {}",
                    folderId, pageToken, pageSize);

            Map<String, Object> result = feishuService.getFeishuPersonalDocuments(folderId, pageToken, pageSize);

            log.info("成功获取飞书个人空间文档列表");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取飞书个人空间文档列表失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 从飞书API同步文档到本地数据库
     */
    @PostMapping("/api/sync-documents")
    public ResponseEntity<Map<String, Object>> syncDocumentsFromFeishuApi(
            @RequestBody Map<String, Object> request) {
        try {
            String folderId = (String) request.get("folderId");
            Integer pageSize = request.get("pageSize") != null ?
                    Integer.valueOf(request.get("pageSize").toString()) : 50;

            log.info("从飞书API同步文档 - folderId: {}, pageSize: {}", folderId, pageSize);

            List<FeishuDocument> syncedDocuments = feishuService.syncDocumentsFromFeishuApi(folderId, pageSize);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("syncedCount", syncedDocuments.size());
            result.put("documents", syncedDocuments);
            result.put("timestamp", LocalDateTime.now());

            log.info("成功从飞书API同步 {} 个文档", syncedDocuments.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("从飞书API同步文档失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 从飞书API获取文档内容
     */
    @GetMapping("/api/content/{fileToken}")
    public ResponseEntity<Map<String, Object>> getContentFromFeishuApi(
            @PathVariable String fileToken,
            @RequestParam(defaultValue = "doc") String fileType) {
        try {
            log.info("从飞书API获取文档内容 - fileToken: {}, fileType: {}", fileToken, fileType);

            String content = feishuService.getContentFromFeishuApi(fileToken, fileType);

            Map<String, Object> result = new HashMap<>();
            result.put("content", content);
            result.put("fileToken", fileToken);
            result.put("fileType", fileType);
            result.put("timestamp", LocalDateTime.now());

            log.info("成功从飞书API获取文档内容，长度: {}", content.length());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("从飞书API获取文档内容失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);
            return ResponseEntity.status(500).body(errorResult);
        }
    }
}
