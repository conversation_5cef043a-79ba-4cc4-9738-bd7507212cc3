package com.lait.service;

import java.util.Map;

/**
 * 仪表盘服务接口
 */
public interface DashboardService {

    /**
     * 获取仪表盘统计数据
     *
     * @return 统计数据
     */
    Map<String, Object> getDashboardStats();

    /**
     * 获取最近活动
     *
     * @return 最近活动数据
     */
    Map<String, Object> getRecentActivities();

    /**
     * 获取用户注册趋势
     *
     * @param days 天数
     * @return 趋势数据
     */
    Map<String, Object> getUserRegistrationTrend(Integer days);

    /**
     * 获取学科分布
     *
     * @return 学科分布数据
     */
    Map<String, Object> getSubjectDistribution();

    /**
     * 获取题目统计
     *
     * @return 题目统计数据
     */
    Map<String, Object> getQuestionStats();

    /**
     * 获取笔记统计
     *
     * @return 笔记统计数据
     */
    Map<String, Object> getNoteStats();
}
