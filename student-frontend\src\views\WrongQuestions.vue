<template>
  <div class="wrong-questions-page">
    <!-- 头部导航 -->
    <van-nav-bar title="错题本" left-arrow @click-left="$router.go(-1)">
      <template #right>
        <van-icon name="filter-o" @click="showFilterPopup = true" />
      </template>
    </van-nav-bar>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <van-row gutter="12">
        <van-col span="8">
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalCount }}</div>
            <div class="stat-label">总错题</div>
          </div>
        </van-col>
        <van-col span="8">
          <div class="stat-card">
            <div class="stat-number">{{ stats.unmasteredCount }}</div>
            <div class="stat-label">待复习</div>
          </div>
        </van-col>
        <van-col span="8">
          <div class="stat-card">
            <div class="stat-number">{{ stats.masteredCount }}</div>
            <div class="stat-label">已掌握</div>
          </div>
        </van-col>
      </van-row>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <van-button
        type="primary"
        size="large"
        block
        @click="startReview"
        :disabled="stats.unmasteredCount === 0"
      >
        开始复习 ({{ stats.unmasteredCount }}题)
      </van-button>
    </div>

    <!-- 学科筛选 -->
    <van-tabs v-model:active="activeSubject" @change="onSubjectChange" sticky>
      <van-tab title="全部" name="all"></van-tab>
      <van-tab
        v-for="subject in subjects"
        :key="subject.id"
        :title="subject.name"
        :name="subject.id"
      ></van-tab>
    </van-tabs>

    <!-- 错题列表 -->
    <div class="wrong-questions-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="item in wrongQuestions"
            :key="item.id"
            class="wrong-question-item"
            @click="viewQuestion(item)"
          >
            <div class="question-header">
              <van-tag :type="getSubjectColor(item.subjectName)" size="small">
                {{ item.subjectName }}
              </van-tag>
              <van-tag
                :type="item.isMastered ? 'success' : 'danger'"
                size="small"
              >
                {{ item.isMastered ? '已掌握' : '未掌握' }}
              </van-tag>
            </div>

            <div class="question-content">
              <p class="question-text">{{ item.questionContent }}</p>
            </div>

            <div class="question-answers">
              <div class="answer-row">
                <span class="answer-label">我的答案：</span>
                <span class="my-answer">{{ item.studentAnswer }}</span>
              </div>
              <div class="answer-row">
                <span class="answer-label">正确答案：</span>
                <span class="correct-answer">{{ item.correctAnswer }}</span>
              </div>
            </div>

            <div class="question-footer">
              <div class="question-meta">
                <span class="wrong-count">错误{{ item.wrongCount }}次</span>
                <span class="wrong-time">{{ formatTime(item.createdTime) }}</span>
              </div>
              <div class="question-actions">
                <van-button
                  size="mini"
                  type="success"
                  @click.stop="markAsMastered(item)"
                  v-if="!item.isMastered"
                >
                  标记掌握
                </van-button>
                <van-button
                  size="mini"
                  type="danger"
                  @click.stop="deleteWrongQuestion(item)"
                >
                  删除
                </van-button>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && wrongQuestions.length === 0"
      description="暂无错题记录"
      image="search"
    >
      <van-button type="primary" @click="$router.push('/practice')">
        去练习
      </van-button>
    </van-empty>

    <!-- 筛选弹窗 -->
    <van-popup v-model:show="showFilterPopup" position="bottom">
      <div class="filter-popup">
        <div class="filter-header">
          <span>筛选条件</span>
          <van-button type="primary" size="small" @click="applyFilter">
            确定
          </van-button>
        </div>

        <van-cell-group>
          <van-field label="掌握状态">
            <template #input>
              <van-radio-group v-model="filterForm.isMastered" direction="horizontal">
                <van-radio name="">全部</van-radio>
                <van-radio :name="false">未掌握</van-radio>
                <van-radio :name="true">已掌握</van-radio>
              </van-radio-group>
            </template>
          </van-field>

          <van-field label="复习状态">
            <template #input>
              <van-radio-group v-model="filterForm.reviewStatus" direction="horizontal">
                <van-radio name="">全部</van-radio>
                <van-radio name="PENDING">待复习</van-radio>
                <van-radio name="REVIEWING">复习中</van-radio>
                <van-radio name="MASTERED">已掌握</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
      </div>
    </van-popup>

    <!-- 错题详情弹窗 -->
    <van-popup v-model:show="showDetailPopup" position="bottom" style="height: 80%">
      <div class="detail-popup" v-if="currentQuestion">
        <div class="detail-header">
          <van-nav-bar title="错题详情" @click-left="showDetailPopup = false">
            <template #left>
              <van-icon name="cross" />
            </template>
          </van-nav-bar>
        </div>

        <div class="detail-content">
          <div class="detail-section">
            <h4>题目内容</h4>
            <p class="question-detail-text">{{ currentQuestion.questionContent }}</p>
          </div>

          <div class="detail-section">
            <h4>答案对比</h4>
            <div class="answer-comparison">
              <div class="answer-item wrong">
                <span class="answer-type">我的答案</span>
                <span class="answer-value">{{ currentQuestion.studentAnswer }}</span>
              </div>
              <div class="answer-item correct">
                <span class="answer-type">正确答案</span>
                <span class="answer-value">{{ currentQuestion.correctAnswer }}</span>
              </div>
            </div>
          </div>

          <div class="detail-section" v-if="currentQuestion.explanation">
            <h4>题目解析</h4>
            <p class="explanation-text">{{ currentQuestion.explanation }}</p>
          </div>

          <div class="detail-section" v-if="currentQuestion.notes">
            <h4>我的笔记</h4>
            <p class="notes-text">{{ currentQuestion.notes }}</p>
          </div>

          <div class="detail-actions">
            <van-button
              type="success"
              size="large"
              block
              @click="markAsMastered(currentQuestion)"
              v-if="!currentQuestion.isMastered"
            >
              标记为已掌握
            </van-button>
            <van-button
              type="primary"
              size="large"
              block
              @click="addNote"
            >
              添加笔记
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 复习模式弹窗 -->
    <van-popup v-model:show="showReviewPopup" position="bottom">
      <div class="review-popup">
        <div class="review-header">
          <h3>选择复习模式</h3>
        </div>

        <van-cell-group>
          <van-cell
            title="顺序复习"
            label="按时间顺序复习错题"
            is-link
            @click="startReviewMode('order')"
          >
            <template #icon>
              <van-icon name="orders-o" />
            </template>
          </van-cell>

          <van-cell
            title="随机复习"
            label="随机顺序复习错题"
            is-link
            @click="startReviewMode('random')"
          >
            <template #icon>
              <van-icon name="shuffle" />
            </template>
          </van-cell>

          <van-cell
            title="重点复习"
            label="优先复习错误次数多的题目"
            is-link
            @click="startReviewMode('priority')"
          >
            <template #icon>
              <van-icon name="fire-o" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { getMyWrongQuestions, markAsMastered as markAsMasteredApi, deleteWrongQuestion as deleteWrongQuestionApi, getMyWrongQuestionStats } from '@/api/wrongQuestions'
import { getSubjects } from '@/api/subjects'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const showFilterPopup = ref(false)
const showDetailPopup = ref(false)
const showReviewPopup = ref(false)

const wrongQuestions = ref([])
const subjects = ref([])
const currentQuestion = ref(null)
const activeSubject = ref('all')

// 统计数据
const stats = reactive({
  totalCount: 0,
  masteredCount: 0,
  unmasteredCount: 0
})

// 分页参数
const pagination = reactive({
  page: 0,
  size: 20,
  hasMore: true
})

// 筛选表单
const filterForm = reactive({
  isMastered: '',
  reviewStatus: '',
  subjectId: null
})

// 方法
const loadWrongQuestions = async (isRefresh = false) => {
  if (loading.value) return

  loading.value = true

  try {
    if (isRefresh) {
      pagination.page = 0
      pagination.hasMore = true
      finished.value = false
    }

    const params = {
      page: pagination.page,
      size: pagination.size,
      subjectId: activeSubject.value === 'all' ? null : activeSubject.value,
      ...filterForm
    }

    const response = await getMyWrongQuestions(params)
    const newData = response.data.content || response.data

    if (isRefresh) {
      wrongQuestions.value = newData
    } else {
      wrongQuestions.value.push(...newData)
    }

    pagination.page++

    if (newData.length < pagination.size) {
      finished.value = true
      pagination.hasMore = false
    }

  } catch (error) {
    showToast('加载错题失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await getMyWrongQuestionStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadSubjects = async () => {
  try {
    const response = await getSubjects()
    subjects.value = response.data
  } catch (error) {
    console.error('加载学科列表失败:', error)
  }
}

const onLoad = () => {
  if (!pagination.hasMore) {
    finished.value = true
    return
  }
  loadWrongQuestions()
}

const onRefresh = () => {
  loadWrongQuestions(true)
}

const onSubjectChange = () => {
  wrongQuestions.value = []
  pagination.page = 0
  pagination.hasMore = true
  finished.value = false
  loadWrongQuestions(true)
}

const viewQuestion = (question) => {
  currentQuestion.value = question
  showDetailPopup.value = true
}

const markAsMastered = async (question) => {
  try {
    await showConfirmDialog({
      title: '确认操作',
      message: '确定要标记这道题为已掌握吗？'
    })

    await markAsMasteredApi(question.id)
    showToast('标记成功')

    // 更新本地数据
    question.isMastered = true
    stats.masteredCount++
    stats.unmasteredCount--

    showDetailPopup.value = false
  } catch (error) {
    if (error !== 'cancel') {
      showToast('标记失败')
    }
  }
}

const deleteWrongQuestion = async (question) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这道错题吗？'
    })

    await deleteWrongQuestionApi(question.id)
    showToast('删除成功')

    // 从列表中移除
    const index = wrongQuestions.value.findIndex(item => item.id === question.id)
    if (index > -1) {
      wrongQuestions.value.splice(index, 1)
    }

    // 更新统计
    if (question.isMastered) {
      stats.masteredCount--
    } else {
      stats.unmasteredCount--
    }
    stats.totalCount--

    showDetailPopup.value = false
  } catch (error) {
    if (error !== 'cancel') {
      showToast('删除失败')
    }
  }
}

const startReview = () => {
  if (stats.unmasteredCount === 0) {
    showToast('暂无需要复习的错题')
    return
  }
  showReviewPopup.value = true
}

const startReviewMode = (mode) => {
  showReviewPopup.value = false
  router.push({
    path: '/practice',
    query: { mode: 'review', reviewMode: mode }
  })
}

const applyFilter = () => {
  showFilterPopup.value = false
  wrongQuestions.value = []
  pagination.page = 0
  pagination.hasMore = true
  finished.value = false
  loadWrongQuestions(true)
}

const addNote = () => {
  // 跳转到笔记创建页面
  router.push({
    path: '/notes/create',
    query: { questionId: currentQuestion.value.questionId }
  })
}

// 辅助方法
const getSubjectColor = (subjectName) => {
  const colors = {
    '语文': 'primary',
    '数学': 'success',
    '英语': 'warning',
    '物理': 'danger',
    '化学': 'primary',
    '生物': 'success'
  }
  return colors[subjectName] || 'default'
}

const formatTime = (timeString) => {
  const date = new Date(timeString)
  const now = new Date()
  const diff = now - date

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

// 生命周期
onMounted(() => {
  loadStats()
  loadSubjects()
  loadWrongQuestions(true)
})
</script>

<style scoped>
.wrong-questions-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

/* 统计卡片 */
.stats-section {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.stat-card {
  text-align: center;
  padding: 16px 8px;
  background: #f7f8fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

/* 快捷操作 */
.quick-actions {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

/* 错题列表 */
.wrong-questions-list {
  padding: 0 16px;
}

.wrong-question-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.wrong-question-item:active {
  transform: scale(0.98);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.question-content {
  margin-bottom: 12px;
}

.question-text {
  font-size: 14px;
  line-height: 1.6;
  color: #323233;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.question-answers {
  margin-bottom: 12px;
}

.answer-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 13px;
}

.answer-label {
  color: #646566;
  margin-right: 8px;
  flex-shrink: 0;
}

.my-answer {
  color: #ee0a24;
  flex: 1;
}

.correct-answer {
  color: #07c160;
  flex: 1;
}

.question-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #969799;
}

.question-actions {
  display: flex;
  gap: 8px;
}

/* 筛选弹窗 */
.filter-popup {
  padding: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

/* 详情弹窗 */
.detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #323233;
}

.question-detail-text {
  font-size: 14px;
  line-height: 1.6;
  color: #323233;
  margin: 0;
}

.answer-comparison {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.answer-item {
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid;
}

.answer-item.wrong {
  background: #fef0f0;
  border-left-color: #ee0a24;
}

.answer-item.correct {
  background: #f0f9ff;
  border-left-color: #07c160;
}

.answer-type {
  display: block;
  font-size: 12px;
  color: #646566;
  margin-bottom: 4px;
}

.answer-value {
  font-size: 14px;
  color: #323233;
}

.explanation-text,
.notes-text {
  font-size: 14px;
  line-height: 1.6;
  color: #323233;
  margin: 0;
}

.detail-actions {
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
}

.detail-actions .van-button {
  margin-bottom: 12px;
}

.detail-actions .van-button:last-child {
  margin-bottom: 0;
}

/* 复习弹窗 */
.review-popup {
  padding: 20px;
}

.review-header {
  text-align: center;
  margin-bottom: 20px;
}

.review-header h3 {
  margin: 0;
  font-size: 18px;
  color: #323233;
}

/* 平板适配 */
@media (min-width: 768px) {
  .wrong-questions-page {
    max-width: 768px;
    margin: 0 auto;
  }

  .stats-section {
    padding: 24px;
  }

  .stat-card {
    padding: 24px 16px;
  }

  .stat-number {
    font-size: 28px;
  }

  .stat-label {
    font-size: 14px;
  }

  .quick-actions {
    padding: 24px;
  }

  .wrong-questions-list {
    padding: 0 24px;
  }

  .wrong-question-item {
    padding: 20px;
    margin-bottom: 16px;
  }

  .question-text {
    font-size: 16px;
    -webkit-line-clamp: 4;
  }

  .answer-row {
    font-size: 14px;
  }

  .question-meta {
    font-size: 13px;
  }

  .detail-content {
    padding: 24px;
  }

  .detail-section h4 {
    font-size: 18px;
  }

  .question-detail-text {
    font-size: 16px;
  }

  .answer-item {
    padding: 16px;
  }

  .answer-value {
    font-size: 16px;
  }

  .explanation-text,
  .notes-text {
    font-size: 16px;
  }
}

/* 大屏平板适配 */
@media (min-width: 1024px) {
  .wrong-questions-page {
    max-width: 1024px;
  }

  .stats-section .van-row {
    max-width: 600px;
    margin: 0 auto;
  }

  .wrong-questions-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 16px;
    padding: 0 24px;
  }

  .wrong-question-item {
    margin-bottom: 0;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 600px) {
  .detail-popup {
    height: 90vh;
  }

  .stats-section {
    padding: 12px 16px;
  }

  .stat-card {
    padding: 12px 8px;
  }

  .stat-number {
    font-size: 20px;
  }

  .quick-actions {
    padding: 12px 16px;
  }
}

/* 触摸优化 */
.van-button {
  min-height: 44px;
}

.wrong-question-item {
  min-height: 44px;
}

.van-cell {
  min-height: 54px;
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .wrong-question-item {
    transition: none;
  }

  .wrong-question-item:active {
    transform: none;
  }
}
</style>
