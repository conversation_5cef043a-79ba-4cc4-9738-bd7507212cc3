package com.lait.dto;

import lombok.Data;

/**
 * 飞书用户访问令牌DTO
 */
@Data
public class FeishuUserTokenDto {
    
    /**
     * 用户访问令牌
     */
    private String accessToken;
    
    /**
     * 令牌类型
     */
    private String tokenType;
    
    /**
     * 过期时间（秒）
     */
    private Integer expiresIn;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 授权范围
     */
    private String scope;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户OpenID
     */
    private String openId;
    
    /**
     * 令牌获取时间戳
     */
    private Long timestamp;
    
    /**
     * 检查令牌是否过期
     */
    public boolean isExpired() {
        if (timestamp == null || expiresIn == null) {
            return true;
        }
        long currentTime = System.currentTimeMillis() / 1000;
        return currentTime >= (timestamp + expiresIn - 300); // 提前5分钟过期
    }
    
    /**
     * 检查刷新令牌是否可用
     */
    public boolean hasValidRefreshToken() {
        return refreshToken != null && !refreshToken.isEmpty();
    }
}
