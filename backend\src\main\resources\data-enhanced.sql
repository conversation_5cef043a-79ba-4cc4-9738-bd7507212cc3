-- 增强版数据库初始化脚本
-- 包含所有新增功能的表结构和初始数据

-- ========== 新增表结构 ==========

-- Coze Token管理表
CREATE TABLE IF NOT EXISTS coze_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT 'Token名称',
    token VARCHAR(500) NOT NULL COMMENT 'Token值',
    token_type VARCHAR(50) NOT NULL COMMENT 'Token类型',
    app_id VARCHAR(100) COMMENT '应用ID',
    app_name VARCHAR(100) COMMENT '应用名称',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    expires_at DATETIME COMMENT '过期时间',
    last_used_at DATETIME COMMENT '最后使用时间',
    usage_count BIGINT DEFAULT 0 COMMENT '使用次数',
    daily_limit INT COMMENT '每日限制',
    monthly_limit INT COMMENT '每月限制',
    description VARCHAR(500) COMMENT '描述',
    config TEXT COMMENT '配置信息',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_token_name (name),
    UNIQUE KEY uk_token_value (token),
    INDEX idx_token_type (token_type),
    INDEX idx_token_status (status),
    INDEX idx_expires_at (expires_at)
) COMMENT='Coze Token管理表';

-- 积分配置表
CREATE TABLE IF NOT EXISTS points_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    points INT NOT NULL COMMENT '积分值',
    category VARCHAR(50) NOT NULL COMMENT '分类',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    daily_limit INT DEFAULT 0 COMMENT '每日限制',
    description VARCHAR(500) COMMENT '描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_enabled (enabled),
    INDEX idx_sort_order (sort_order)
) COMMENT='积分配置表';

-- 用户积分记录表
CREATE TABLE IF NOT EXISTS user_points (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    points_change INT NOT NULL COMMENT '积分变化',
    total_points INT NOT NULL COMMENT '总积分',
    source VARCHAR(100) NOT NULL COMMENT '来源',
    points_type VARCHAR(50) NOT NULL COMMENT '积分类型',
    related_id BIGINT COMMENT '关联对象ID',
    related_type VARCHAR(50) COMMENT '关联对象类型',
    description VARCHAR(500) COMMENT '描述',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_points_type (points_type),
    INDEX idx_created_time (created_time),
    INDEX idx_source (source),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='用户积分记录表';

-- 飞书文档表
CREATE TABLE IF NOT EXISTS feishu_documents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '文档标题',
    doc_id VARCHAR(100) UNIQUE NOT NULL COMMENT '飞书文档ID',
    doc_token VARCHAR(200) COMMENT '文档Token',
    doc_type VARCHAR(50) NOT NULL COMMENT '文档类型',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    doc_url VARCHAR(500) COMMENT '文档URL',
    content LONGTEXT COMMENT '文档内容',
    summary VARCHAR(1000) COMMENT '文档摘要',
    creator_id BIGINT COMMENT '创建者ID',
    subject_id BIGINT COMMENT '关联学科ID',
    category VARCHAR(100) COMMENT '文档分类',
    tags VARCHAR(500) COMMENT '标签',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    access_level VARCHAR(20) NOT NULL DEFAULT 'INTERNAL' COMMENT '访问权限',
    last_sync_at DATETIME COMMENT '最后同步时间',
    sync_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '同步状态',
    version INT DEFAULT 1 COMMENT '版本号',
    view_count BIGINT DEFAULT 0 COMMENT '查看次数',
    config TEXT COMMENT '配置信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_doc_type (doc_type),
    INDEX idx_status (status),
    INDEX idx_creator_id (creator_id),
    INDEX idx_subject_id (subject_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_view_count (view_count),
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL
) COMMENT='飞书文档表';

-- 考试表
CREATE TABLE IF NOT EXISTS exams (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '考试标题',
    description TEXT COMMENT '考试描述',
    subject_id BIGINT NOT NULL COMMENT '学科ID',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    exam_type VARCHAR(50) NOT NULL COMMENT '考试类型',
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '考试状态',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration_minutes INT COMMENT '考试时长(分钟)',
    total_points INT DEFAULT 0 COMMENT '总分',
    pass_score INT COMMENT '及格分数',
    question_count INT DEFAULT 0 COMMENT '题目数量',
    participant_count INT DEFAULT 0 COMMENT '参与人数',
    max_attempts INT DEFAULT 1 COMMENT '最大尝试次数',
    shuffle_questions BOOLEAN DEFAULT FALSE COMMENT '是否打乱题目',
    shuffle_options BOOLEAN DEFAULT FALSE COMMENT '是否打乱选项',
    show_result_immediately BOOLEAN DEFAULT TRUE COMMENT '是否立即显示结果',
    allow_review BOOLEAN DEFAULT TRUE COMMENT '是否允许查看',
    instructions TEXT COMMENT '考试说明',
    grade_level INT COMMENT '年级',
    class_name VARCHAR(50) COMMENT '班级',
    settings JSON COMMENT '考试设置',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_subject_id (subject_id),
    INDEX idx_creator_id (creator_id),
    INDEX idx_exam_type (exam_type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT='考试表';

-- 考试题目关联表
CREATE TABLE IF NOT EXISTS exam_questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    exam_id BIGINT NOT NULL COMMENT '考试ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    question_order INT COMMENT '题目顺序',
    points INT COMMENT '题目分值',
    time_limit INT COMMENT '单题时间限制(秒)',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必答',
    custom_options TEXT COMMENT '自定义选项',
    custom_answer TEXT COMMENT '自定义答案',
    custom_explanation TEXT COMMENT '自定义解析',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_exam_question (exam_id, question_id),
    INDEX idx_exam_id (exam_id),
    INDEX idx_question_id (question_id),
    INDEX idx_question_order (question_order),
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
) COMMENT='考试题目关联表';

-- 学习记录表
CREATE TABLE IF NOT EXISTS study_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    subject_id BIGINT NOT NULL COMMENT '学科ID',
    question_id BIGINT COMMENT '题目ID',
    exam_id BIGINT COMMENT '考试ID',
    record_type VARCHAR(50) NOT NULL COMMENT '记录类型',
    user_answer TEXT COMMENT '用户答案',
    correct_answer TEXT COMMENT '正确答案',
    is_correct BOOLEAN COMMENT '是否正确',
    score INT COMMENT '得分',
    time_spent INT COMMENT '花费时间(秒)',
    study_date DATETIME COMMENT '学习时间',
    difficulty_level VARCHAR(20) COMMENT '难度级别',
    notes TEXT COMMENT '学习笔记',
    mistake_type VARCHAR(100) COMMENT '错误类型',
    metadata JSON COMMENT '额外元数据',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_subject_id (subject_id),
    INDEX idx_question_id (question_id),
    INDEX idx_exam_id (exam_id),
    INDEX idx_record_type (record_type),
    INDEX idx_is_correct (is_correct),
    INDEX idx_study_date (study_date),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE SET NULL,
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE SET NULL
) COMMENT='学习记录表';

-- 学习进度表
CREATE TABLE IF NOT EXISTS study_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    subject_id BIGINT NOT NULL COMMENT '学科ID',
    chapter VARCHAR(100) COMMENT '章节',
    total_questions INT DEFAULT 0 COMMENT '总题目数',
    completed_questions INT DEFAULT 0 COMMENT '已完成题目数',
    correct_questions INT DEFAULT 0 COMMENT '正确题目数',
    total_time_spent INT DEFAULT 0 COMMENT '总学习时间(分钟)',
    last_study_time DATETIME COMMENT '最后学习时间',
    study_streak INT DEFAULT 0 COMMENT '连续学习天数',
    mastery_level VARCHAR(20) DEFAULT 'BEGINNER' COMMENT '掌握程度',
    completion_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成百分比',
    accuracy_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '正确率',
    average_time_per_question DECIMAL(8,2) DEFAULT 0.00 COMMENT '平均每题时间',
    statistics JSON COMMENT '详细统计数据',
    weak_points JSON COMMENT '薄弱知识点',
    strong_points JSON COMMENT '强项知识点',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_subject_chapter (user_id, subject_id, chapter),
    INDEX idx_user_id (user_id),
    INDEX idx_subject_id (subject_id),
    INDEX idx_mastery_level (mastery_level),
    INDEX idx_completion_percentage (completion_percentage),
    INDEX idx_last_study_time (last_study_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
) COMMENT='学习进度表';

-- ========== 初始化数据 ==========

-- 初始化默认积分配置
INSERT IGNORE INTO points_config (config_key, display_name, points, category, description, sort_order) VALUES
('daily_login', '每日登录', 5, 'LEARNING', '每日首次登录获得积分', 1),
('complete_question', '完成题目', 2, 'PRACTICE', '完成一道题目获得积分', 2),
('correct_answer', '答对题目', 3, 'PRACTICE', '答对题目额外获得积分', 3),
('continuous_correct', '连续答对', 5, 'ACHIEVEMENT', '连续答对5题获得奖励积分', 4),
('daily_practice', '每日练习', 10, 'LEARNING', '每日完成练习任务获得积分', 5),
('share_note', '分享笔记', 8, 'SOCIAL', '分享学习笔记获得积分', 6),
('help_others', '帮助他人', 15, 'SOCIAL', '帮助其他同学获得积分', 7),
('exam_excellent', '考试优秀', 50, 'ACHIEVEMENT', '考试成绩优秀获得大量积分', 8),
('study_streak_7', '连续学习7天', 30, 'ACHIEVEMENT', '连续学习7天获得成就积分', 9),
('wrong_answer', '答错扣分', -1, 'PENALTY', '答错题目扣除积分', 10);

-- 初始化示例Coze Token
INSERT IGNORE INTO coze_tokens (name, token, token_type, app_name, status, description) VALUES
('默认API密钥', 'demo_api_key_12345', 'API_KEY', '学习助手', 'ACTIVE', '用于AI学习助手的默认API密钥'),
('Webhook令牌', 'demo_webhook_token_67890', 'WEBHOOK_TOKEN', '消息推送', 'ACTIVE', '用于消息推送的Webhook令牌');

-- 更新现有用户表，添加新字段的默认值
UPDATE users SET login_count = 0 WHERE login_count IS NULL;
UPDATE users SET gender = 'OTHER' WHERE gender IS NULL;

-- 为现有题目添加默认状态
UPDATE questions SET status = 'ACTIVE' WHERE status IS NULL;
UPDATE questions SET points = 1 WHERE points IS NULL;
UPDATE questions SET error_count = 0 WHERE error_count IS NULL;

COMMIT;
