<template>
  <div class="home">
    <!-- 顶部用户信息 -->
    <div class="header">
      <div class="user-info">
        <van-image
          round
          width="50"
          height="50"
          :src="authStore.user?.avatarUrl"
          fit="cover"
          class="avatar"
        >
          <template #error>
            <div class="avatar-placeholder">
              {{ authStore.user?.realName?.charAt(0) || 'U' }}
            </div>
          </template>
        </van-image>
        <div class="user-details">
          <div class="name">{{ authStore.user?.realName || authStore.user?.username }}</div>
          <div class="grade">{{ gradeText }}</div>
        </div>
      </div>
      <van-icon name="bell-o" size="24" class="notification-icon" />
    </div>

    <!-- 学习统计 -->
    <van-cell-group title="学习统计" class="stats-group">
      <van-grid :column-num="4" :border="false">
        <van-grid-item
          v-for="stat in stats"
          :key="stat.key"
          :text="stat.label"
          class="stat-item"
        >
          <template #icon>
            <div class="stat-number">{{ stat.value }}</div>
          </template>
        </van-grid-item>
      </van-grid>
    </van-cell-group>

    <!-- 快捷功能 -->
    <van-cell-group title="快捷功能" class="quick-actions">
      <van-grid :column-num="3" :border="false">
        <van-grid-item
          v-for="action in quickActions"
          :key="action.key"
          :icon="action.icon"
          :text="action.label"
          @click="handleQuickAction(action.key)"
          class="action-item"
        />
      </van-grid>
    </van-cell-group>

    <!-- 最近学习 -->
    <van-cell-group title="最近学习" class="recent-study">
      <van-cell
        v-for="item in recentStudy"
        :key="item.id"
        :title="item.title"
        :label="item.description"
        :value="item.time"
        is-link
        @click="handleStudyItem(item)"
      >
        <template #icon>
          <van-tag :type="getSubjectTagType(item.subject)">
            {{ item.subject }}
          </van-tag>
        </template>
      </van-cell>
    </van-cell-group>

    <!-- 今日推荐 -->
    <van-cell-group title="今日推荐" class="recommendations">
      <van-swipe :autoplay="3000" indicator-color="white">
        <van-swipe-item
          v-for="item in recommendations"
          :key="item.id"
          class="recommendation-item"
          @click="handleRecommendation(item)"
        >
          <div class="recommendation-content">
            <div class="recommendation-title">{{ item.title }}</div>
            <div class="recommendation-desc">{{ item.description }}</div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </van-cell-group>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useAuthStore } from '@/stores/auth'
import { getStudyStats } from '@/api/study'
import { getMyGradeStats } from '@/api/grades'

const router = useRouter()
const authStore = useAuthStore()

// 计算年级文本
const gradeText = computed(() => {
  const grade = authStore.user?.gradeLevel
  const className = authStore.user?.className
  if (grade && className) {
    return `${grade}年级 ${className}`
  } else if (grade) {
    return `${grade}年级`
  }
  return '学生'
})

// 学习统计
const stats = ref([
  { key: 'study_days', label: '学习天数', value: 0 },
  { key: 'completed_exercises', label: '完成练习', value: 0 },
  { key: 'correct_rate', label: '正确率', value: '0%' },
  { key: 'notes_count', label: '笔记数量', value: 0 }
])

// 快捷功能
const quickActions = ref([
  { key: 'practice', icon: 'edit', label: '开始练习' },
  { key: 'wrong_questions', icon: 'warning-o', label: '错题本' },
  { key: 'notes', icon: 'notes-o', label: '我的笔记' },
  { key: 'grades', icon: 'chart-trending-o', label: '成绩查询' },
  { key: 'ai_tutor', icon: 'chat-o', label: 'AI助教' },
  { key: 'study_plan', icon: 'calendar-o', label: '学习计划' }
])

// 最近学习
const recentStudy = ref([
  {
    id: 1,
    title: '小数的加减法',
    description: '完成了10道练习题',
    subject: '数学',
    time: '2小时前'
  },
  {
    id: 2,
    title: '古诗词背诵',
    description: '背诵了《静夜思》',
    subject: '语文',
    time: '昨天'
  },
  {
    id: 3,
    title: '英语单词',
    description: '学习了20个新单词',
    subject: '英语',
    time: '2天前'
  }
])

// 今日推荐
const recommendations = ref([
  {
    id: 1,
    title: '数学思维训练',
    description: '提升逻辑思维能力，轻松解决应用题',
    type: 'course'
  },
  {
    id: 2,
    title: '语文阅读理解',
    description: '掌握阅读技巧，提高理解能力',
    type: 'practice'
  },
  {
    id: 3,
    title: 'AI智能辅导',
    description: '个性化学习方案，专属你的学习助手',
    type: 'ai'
  }
])

// 方法
const loadStats = async () => {
  try {
    // 获取学习统计数据
    const studyResponse = await getStudyStats()
    const studyData = studyResponse.data || {}

    // 获取成绩统计数据
    const gradeResponse = await getMyGradeStats()
    const gradeData = gradeResponse.data || {}

    // 更新统计数据
    stats.value = [
      {
        key: 'study_days',
        label: '学习天数',
        value: studyData.studyDays || 0
      },
      {
        key: 'completed_exercises',
        label: '完成练习',
        value: studyData.questionsCompleted || 0
      },
      {
        key: 'correct_rate',
        label: '正确率',
        value: `${studyData.correctRate || 0}%`
      },
      {
        key: 'notes_count',
        label: '笔记数量',
        value: studyData.notesCount || 0
      }
    ]
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 保持默认值
    stats.value = [
      { key: 'study_days', label: '学习天数', value: 0 },
      { key: 'completed_exercises', label: '完成练习', value: 0 },
      { key: 'correct_rate', label: '正确率', value: '0%' },
      { key: 'notes_count', label: '笔记数量', value: 0 }
    ]
  }
}

const handleQuickAction = (key) => {
  const routeMap = {
    practice: '/practice',
    wrong_questions: '/wrong-questions',
    notes: '/notes',
    grades: '/grades'
  }

  if (routeMap[key]) {
    router.push(routeMap[key])
  } else {
    showToast(`${key} 功能开发中`)
  }
}

const handleStudyItem = (item) => {
  showToast(`查看 ${item.title}`)
}

const handleRecommendation = (item) => {
  showToast(`推荐: ${item.title}`)
}

const getSubjectTagType = (subject) => {
  const typeMap = {
    '数学': 'primary',
    '语文': 'success',
    '英语': 'warning',
    '科学': 'danger'
  }
  return typeMap[subject] || 'default'
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.home {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  margin-right: 12px;
}

.avatar-placeholder {
  width: 50px;
  height: 50px;
  background-color: #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: #666;
}

.user-details .name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.user-details .grade {
  font-size: 14px;
  opacity: 0.8;
}

.notification-icon {
  color: white;
}

.stats-group {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 4px;
}

.quick-actions {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.action-item {
  text-align: center;
  padding: 16px 8px;
}

.recent-study {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.recommendations {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.recommendation-item {
  height: 120px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
}

.recommendation-content {
  text-align: center;
  padding: 20px;
}

.recommendation-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.recommendation-desc {
  font-size: 14px;
  opacity: 0.9;
}
</style>
