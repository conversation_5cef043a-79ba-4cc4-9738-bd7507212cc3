package com.lait.controller;

import com.lait.entity.FeishuDocument;
import com.lait.service.FeishuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 飞书API管理控制器
 * 不需要管理员权限，所有登录用户都可以访问
 */
@RestController
@RequestMapping("/feishu-api")
@RequiredArgsConstructor
@Slf4j
public class FeishuApiController {

    private final FeishuService feishuService;

    /**
     * 测试飞书API连接
     */
    @GetMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testFeishuApiConnection() {
        try {
            log.info("测试飞书API连接");
            Map<String, Object> result = feishuService.testFeishuApiConnection();
            log.info("飞书API连接测试完成");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("飞书API连接测试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("connected", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 获取飞书个人空间文档列表
     */
    @GetMapping("/personal-documents")
    public ResponseEntity<Map<String, Object>> getFeishuPersonalDocuments(
            @RequestParam(required = false) String folderId,
            @RequestParam(required = false) String pageToken,
            @RequestParam(defaultValue = "50") Integer pageSize) {
        try {
            log.info("获取飞书个人空间文档列表 - folderId: {}, pageToken: {}, pageSize: {}",
                    folderId, pageToken, pageSize);

            Map<String, Object> result = feishuService.getFeishuPersonalDocuments(folderId, pageToken, pageSize);

            log.info("成功获取飞书个人空间文档列表");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取飞书个人空间文档列表失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 从飞书API同步文档到本地数据库
     */
    @PostMapping("/sync-documents")
    public ResponseEntity<Map<String, Object>> syncDocumentsFromFeishuApi(
            @RequestBody Map<String, Object> request) {
        try {
            String folderId = (String) request.get("folderId");
            Integer pageSize = request.get("pageSize") != null ?
                    Integer.valueOf(request.get("pageSize").toString()) : 50;

            log.info("从飞书API同步文档 - folderId: {}, pageSize: {}", folderId, pageSize);

            List<FeishuDocument> syncedDocuments = feishuService.syncDocumentsFromFeishuApi(folderId, pageSize);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("syncedCount", syncedDocuments.size());
            result.put("documents", syncedDocuments);
            result.put("timestamp", LocalDateTime.now());

            log.info("成功从飞书API同步 {} 个文档", syncedDocuments.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("从飞书API同步文档失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 从飞书API获取文档内容
     */
    @GetMapping("/content/{fileToken}")
    public ResponseEntity<Map<String, Object>> getContentFromFeishuApi(
            @PathVariable String fileToken,
            @RequestParam(defaultValue = "doc") String fileType) {
        try {
            log.info("从飞书API获取文档内容 - fileToken: {}, fileType: {}", fileToken, fileType);

            String content = feishuService.getContentFromFeishuApi(fileToken, fileType);

            Map<String, Object> result = new HashMap<>();
            result.put("content", content);
            result.put("fileToken", fileToken);
            result.put("fileType", fileType);
            result.put("timestamp", LocalDateTime.now());

            log.info("成功从飞书API获取文档内容，长度: {}", content.length());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("从飞书API获取文档内容失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 根据docToken获取文档内容
     */
    @GetMapping("/content-by-token/{docToken}")
    public ResponseEntity<Map<String, Object>> getContentByDocToken(
            @PathVariable String docToken) {
        try {
            log.info("根据docToken获取文档内容 - docToken: {}", docToken);

            String content = feishuService.getDocumentContentByToken(docToken);

            Map<String, Object> result = new HashMap<>();
            result.put("content", content);
            result.put("docToken", docToken);
            result.put("timestamp", LocalDateTime.now());
            result.put("success", true);

            log.info("成功根据docToken获取文档内容，长度: {}", content.length());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("根据docToken获取文档内容失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("success", false);
            errorResult.put("docToken", docToken);
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 获取飞书API配置状态
     */
    @GetMapping("/config-status")
    public ResponseEntity<Map<String, Object>> getConfigStatus() {
        try {
            log.info("获取飞书API配置状态");

            Map<String, Object> result = new HashMap<>();

            // 检查基本配置状态（不暴露敏感信息）
            boolean hasAppId = System.getenv("FEISHU_APP_ID") != null && !System.getenv("FEISHU_APP_ID").isEmpty();
            boolean hasAppSecret = System.getenv("FEISHU_APP_SECRET") != null && !System.getenv("FEISHU_APP_SECRET").isEmpty();

            result.put("hasAppId", hasAppId);
            result.put("hasAppSecret", hasAppSecret);
            result.put("configured", hasAppId && hasAppSecret);
            result.put("timestamp", LocalDateTime.now());

            if (!hasAppId || !hasAppSecret) {
                result.put("message", "请配置FEISHU_APP_ID和FEISHU_APP_SECRET环境变量");
            }

            log.info("飞书API配置状态检查完成 - 已配置: {}", hasAppId && hasAppSecret);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取飞书API配置状态失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("configured", false);
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 获取支持的文档类型
     */
    @GetMapping("/supported-types")
    public ResponseEntity<Map<String, Object>> getSupportedTypes() {
        try {
            log.info("获取支持的文档类型");

            Map<String, Object> result = new HashMap<>();

            // 支持的文档类型
            Map<String, String> types = new HashMap<>();
            types.put("doc", "飞书文档");
            types.put("docx", "飞书文档");
            types.put("sheet", "飞书表格");
            types.put("slides", "飞书演示文稿");
            types.put("bitable", "多维表格");
            types.put("mindnote", "思维笔记");

            result.put("types", types);
            result.put("count", types.size());
            result.put("timestamp", LocalDateTime.now());

            log.info("成功获取支持的文档类型，共 {} 种", types.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取支持的文档类型失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 获取API使用统计
     */
    @GetMapping("/usage-stats")
    public ResponseEntity<Map<String, Object>> getUsageStats() {
        try {
            log.info("获取飞书API使用统计");

            Map<String, Object> result = new HashMap<>();

            // 这里可以添加实际的统计逻辑
            result.put("totalCalls", 0);
            result.put("successCalls", 0);
            result.put("failedCalls", 0);
            result.put("lastCallTime", null);
            result.put("timestamp", LocalDateTime.now());

            log.info("成功获取飞书API使用统计");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取飞书API使用统计失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
}
