package com.lait.service.impl;

import com.lait.entity.CozeToken;
import com.lait.repository.CozeTokenRepository;
import com.lait.service.CozeTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Coze Token服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CozeTokenServiceImpl implements CozeTokenService {

    private final CozeTokenRepository cozeTokenRepository;

    @Override
    @Transactional
    public CozeToken createToken(CozeToken token) {
        // 检查名称是否已存在
        if (cozeTokenRepository.existsByName(token.getName())) {
            throw new RuntimeException("Token名称已存在");
        }

        // 检查Token值是否已存在
        if (cozeTokenRepository.existsByToken(token.getToken())) {
            throw new RuntimeException("Token值已存在");
        }

        // 如果设置为默认Token，先取消其他默认Token
        if (Boolean.TRUE.equals(token.getIsDefault())) {
            cozeTokenRepository.findByIsDefaultTrue().ifPresent(existingDefault -> {
                existingDefault.setIsDefault(false);
                cozeTokenRepository.save(existingDefault);
            });
        }

        token.setUsageCount(0L);
        token.setCreatedTime(LocalDateTime.now());
        token.setUpdatedTime(LocalDateTime.now());

        return cozeTokenRepository.save(token);
    }

    @Override
    @Transactional
    public CozeToken updateToken(Long id, CozeToken token) {
        CozeToken existingToken = getTokenById(id);

        // 检查名称是否与其他Token冲突
        if (!existingToken.getName().equals(token.getName()) &&
            cozeTokenRepository.existsByName(token.getName())) {
            throw new RuntimeException("Token名称已存在");
        }

        // 检查Token值是否与其他Token冲突
        if (!existingToken.getToken().equals(token.getToken()) &&
            cozeTokenRepository.existsByToken(token.getToken())) {
            throw new RuntimeException("Token值已存在");
        }

        // 如果设置为默认Token，先取消其他默认Token
        if (Boolean.TRUE.equals(token.getIsDefault()) && !Boolean.TRUE.equals(existingToken.getIsDefault())) {
            cozeTokenRepository.findByIsDefaultTrue().ifPresent(defaultToken -> {
                defaultToken.setIsDefault(false);
                cozeTokenRepository.save(defaultToken);
            });
        }

        // 更新字段
        existingToken.setName(token.getName());
        existingToken.setToken(token.getToken());
        existingToken.setTokenType(token.getTokenType());
        existingToken.setAppId(token.getAppId());
        existingToken.setAppName(token.getAppName());
        existingToken.setStatus(token.getStatus());
        existingToken.setExpiresAt(token.getExpiresAt());
        existingToken.setDailyLimit(token.getDailyLimit());
        existingToken.setMonthlyLimit(token.getMonthlyLimit());
        existingToken.setDescription(token.getDescription());
        existingToken.setConfig(token.getConfig());
        existingToken.setIsDefault(token.getIsDefault());
        existingToken.setUpdatedTime(LocalDateTime.now());

        return cozeTokenRepository.save(existingToken);
    }

    @Override
    @Transactional
    public void deleteToken(Long id) {
        CozeToken token = getTokenById(id);
        cozeTokenRepository.delete(token);
    }

    @Override
    public CozeToken getTokenById(Long id) {
        return cozeTokenRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Token不存在"));
    }

    @Override
    public CozeToken getTokenByName(String name) {
        return cozeTokenRepository.findByName(name)
                .orElseThrow(() -> new RuntimeException("Token不存在"));
    }

    @Override
    public CozeToken getDefaultToken() {
        return cozeTokenRepository.findByIsDefaultTrue()
                .orElseThrow(() -> new RuntimeException("未设置默认Token"));
    }

    @Override
    @Transactional
    public void setDefaultToken(Long id) {
        CozeToken token = getTokenById(id);

        // 取消其他默认Token
        cozeTokenRepository.findByIsDefaultTrue().ifPresent(existingDefault -> {
            existingDefault.setIsDefault(false);
            cozeTokenRepository.save(existingDefault);
        });

        // 设置新的默认Token
        token.setIsDefault(true);
        token.setUpdatedTime(LocalDateTime.now());
        cozeTokenRepository.save(token);
    }

    @Override
    public Page<CozeToken> getTokens(String name, CozeToken.TokenType tokenType,
                                    CozeToken.TokenStatus status, Pageable pageable) {
        return cozeTokenRepository.findTokensWithFilters(name, tokenType, status, pageable);
    }

    @Override
    public List<CozeToken> getActiveTokens() {
        return cozeTokenRepository.findActiveTokens(LocalDateTime.now());
    }

    @Override
    public boolean validateToken(String token) {
        Optional<CozeToken> cozeToken = cozeTokenRepository.findByToken(token);
        if (!cozeToken.isPresent()) {
            return false;
        }

        CozeToken tokenEntity = cozeToken.get();

        // 检查状态
        if (tokenEntity.getStatus() != CozeToken.TokenStatus.ACTIVE) {
            return false;
        }

        // 检查过期时间
        if (tokenEntity.getExpiresAt() != null && tokenEntity.getExpiresAt().isBefore(LocalDateTime.now())) {
            return false;
        }

        return true;
    }

    @Override
    @Transactional
    public void refreshTokenStatus(Long id) {
        CozeToken token = getTokenById(id);

        // 检查是否过期
        if (token.getExpiresAt() != null && token.getExpiresAt().isBefore(LocalDateTime.now())) {
            token.setStatus(CozeToken.TokenStatus.EXPIRED);
            token.setUpdatedTime(LocalDateTime.now());
            cozeTokenRepository.save(token);
        }
    }

    @Override
    @Transactional
    public void updateTokenUsage(Long id) {
        cozeTokenRepository.updateUsageInfo(id, LocalDateTime.now());
    }

    @Override
    public Map<String, Object> getTokenStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总数统计
        stats.put("totalCount", cozeTokenRepository.count());

        // 状态统计
        List<Object[]> statusStats = cozeTokenRepository.countByStatus();
        Map<String, Long> statusMap = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("statusStats", statusMap);

        // 类型统计
        List<Object[]> typeStats = cozeTokenRepository.countByTokenType();
        Map<String, Long> typeMap = new HashMap<>();
        for (Object[] stat : typeStats) {
            typeMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("typeStats", typeMap);

        // 即将过期的Token数量
        LocalDateTime threshold = LocalDateTime.now().plusDays(7);
        List<CozeToken> expiringTokens = cozeTokenRepository.findExpiringTokens(LocalDateTime.now(), threshold);
        stats.put("expiringCount", expiringTokens.size());

        return stats;
    }

    @Override
    public List<CozeToken> getExpiringTokens(int days) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.plusDays(days);
        return cozeTokenRepository.findExpiringTokens(now, threshold);
    }

    @Override
    @Transactional
    public void batchUpdateTokenStatus(List<Long> ids, CozeToken.TokenStatus status) {
        for (Long id : ids) {
            CozeToken token = getTokenById(id);
            token.setStatus(status);
            token.setUpdatedTime(LocalDateTime.now());
            cozeTokenRepository.save(token);
        }
    }

    @Override
    public Map<String, Object> testTokenConnection(Long id) {
        CozeToken token = getTokenById(id);
        Map<String, Object> result = new HashMap<>();

        try {
            // 这里可以实现实际的Token连接测试逻辑
            // 例如调用Coze API进行验证

            boolean isValid = validateToken(token.getToken());
            result.put("success", isValid);
            result.put("message", isValid ? "连接成功" : "连接失败");
            result.put("timestamp", LocalDateTime.now());

        } catch (Exception e) {
            log.error("测试Token连接失败", e);
            result.put("success", false);
            result.put("message", "连接测试异常: " + e.getMessage());
            result.put("timestamp", LocalDateTime.now());
        }

        return result;
    }

    @Override
    public String exportTokenConfig(List<Long> ids) {
        // 实现Token配置导出逻辑
        // 返回JSON格式的配置字符串
        return "{}"; // 简化实现
    }

    @Override
    @Transactional
    public List<CozeToken> importTokenConfig(String config) {
        // 实现Token配置导入逻辑
        // 简化实现，返回空列表
        return new ArrayList<>();
    }
}
