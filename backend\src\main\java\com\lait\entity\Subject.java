package com.lait.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 学科实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "subjects")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Subject extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "学科名称不能为空")
    @Size(max = 100, message = "学科名称长度不能超过100个字符")
    @Column(nullable = false)
    private String name;

    @Size(max = 500, message = "学科描述长度不能超过500个字符")
    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(name = "grade_level")
    private Integer gradeLevel; // 适用年级

    @Column(name = "subject_code", unique = true)
    private String subjectCode; // 学科代码

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private SubjectStatus status;

    @Column(name = "sort_order")
    private Integer sortOrder; // 排序

    /**
     * 学科状态枚举
     */
    public enum SubjectStatus {
        ACTIVE("启用"),
        INACTIVE("禁用");

        private final String description;

        SubjectStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
