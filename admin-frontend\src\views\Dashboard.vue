<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>仪表盘</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading" :icon="Refresh">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.userCount || 0 }}</div>
              <div class="stats-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon subject-icon">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.subjectCount || 0 }}</div>
              <div class="stats-label">学科数量</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon document-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.documentCount || 0 }}</div>
              <div class="stats-label">飞书文档</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon question-icon">
              <el-icon><EditPen /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.questionCount || 0 }}</div>
              <div class="stats-label">题目数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 文档类型分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>文档类型分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div v-if="docTypeData.length === 0" class="no-data">
              <el-empty description="暂无数据" />
            </div>
            <div v-else class="type-stats">
              <div v-for="item in docTypeData" :key="item.type" class="type-item">
                <div class="type-info">
                  <el-tag :type="getDocTypeColor(item.type)" size="small">
                    {{ getDocTypeText(item.type) }}
                  </el-tag>
                  <span class="type-count">{{ item.count }}</span>
                </div>
                <div class="type-progress">
                  <el-progress
                    :percentage="getPercentage(item.count, stats.documentCount)"
                    :show-text="false"
                    :stroke-width="6"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 同步状态分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>同步状态分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div v-if="syncStatusData.length === 0" class="no-data">
              <el-empty description="暂无数据" />
            </div>
            <div v-else class="status-stats">
              <div v-for="item in syncStatusData" :key="item.status" class="status-item">
                <div class="status-info">
                  <el-tag :type="getSyncStatusColor(item.status)" size="small">
                    {{ getSyncStatusText(item.status) }}
                  </el-tag>
                  <span class="status-count">{{ item.count }}</span>
                </div>
                <div class="status-progress">
                  <el-progress
                    :percentage="getPercentage(item.count, stats.documentCount)"
                    :show-text="false"
                    :stroke-width="6"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 学科分布 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>学科分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div v-if="subjectData.length === 0" class="no-data">
              <el-empty description="暂无数据" />
            </div>
            <div v-else class="subject-stats">
              <div v-for="item in subjectData" :key="item.subjectId" class="subject-item">
                <div class="subject-info">
                  <span class="subject-name">{{ item.subjectName }}</span>
                  <span class="subject-count">{{ item.count }}</span>
                </div>
                <div class="subject-progress">
                  <el-progress
                    :percentage="getPercentage(item.count, stats.documentCount)"
                    :show-text="false"
                    :stroke-width="6"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动和热门文档 -->
    <el-row :gutter="20" class="activity-row">
      <!-- 最近文档 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最近文档</span>
              <el-button text type="primary" @click="goToFeishuDocs">
                查看全部
              </el-button>
            </div>
          </template>
          <div class="activity-list">
            <div v-if="recentDocuments.length === 0" class="no-data">
              <el-empty description="暂无最近文档" />
            </div>
            <div v-else>
              <div v-for="doc in recentDocuments" :key="doc.id" class="activity-item">
                <div class="activity-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ doc.title }}</div>
                  <div class="activity-meta">
                    <el-tag :type="getDocTypeColor(doc.docType)" size="small">
                      {{ getDocTypeText(doc.docType) }}
                    </el-tag>
                    <span class="activity-time">{{ formatDateTime(doc.createdTime) }}</span>
                  </div>
                </div>
                <div class="activity-actions">
                  <el-button text type="primary" @click="viewDocument(doc)">
                    查看
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 热门文档 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>热门文档</span>
              <el-button text type="primary" @click="goToFeishuDocs">
                查看全部
              </el-button>
            </div>
          </template>
          <div class="activity-list">
            <div v-if="popularDocuments.length === 0" class="no-data">
              <el-empty description="暂无热门文档" />
            </div>
            <div v-else>
              <div v-for="doc in popularDocuments" :key="doc.id" class="activity-item">
                <div class="activity-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ doc.title }}</div>
                  <div class="activity-meta">
                    <el-tag :type="getDocTypeColor(doc.docType)" size="small">
                      {{ getDocTypeText(doc.docType) }}
                    </el-tag>
                    <span class="activity-views">{{ doc.viewCount }} 次查看</span>
                  </div>
                </div>
                <div class="activity-actions">
                  <el-button text type="primary" @click="viewDocument(doc)">
                    查看
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions-row">
      <el-col :span="24">
        <el-card class="quick-actions-card">
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" :icon="Plus" @click="goToCreateDocument">
              新建文档
            </el-button>
            <el-button type="success" :icon="Upload" @click="goToImportDocument">
              导入文档
            </el-button>
            <el-button type="warning" :icon="Refresh" @click="goToSyncDocuments">
              同步文档
            </el-button>
            <el-button type="info" :icon="Connection" @click="goToFeishuApi">
              飞书API管理
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh, Document, CircleCheck, View, Collection, Plus, Upload,
  Connection, TrendCharts, User, Reading, EditPen
} from '@element-plus/icons-vue'

// API导入
import { getDashboardStats, getRecentActivities } from '@/api/dashboard'
import {
  getFeishuDocumentStatisticsForDashboard,
  getFeishuDocuments,
  getPopularFeishuDocumentsForDashboard
} from '@/api/feishu'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const stats = reactive({
  userCount: 0,
  subjectCount: 0,
  questionCount: 0,
  documentCount: 0
})

const docTypeData = ref([])
const syncStatusData = ref([])
const subjectData = ref([])
const recentDocuments = ref([])
const popularDocuments = ref([])

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadFeishuStatistics(),
      loadRecentDocuments(),
      loadPopularDocuments()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await getDashboardStats()
    Object.assign(stats, response)
  } catch (error) {
    console.error('加载基础统计数据失败:', error)
  }
}

const loadFeishuStatistics = async () => {
  try {
    const response = await getFeishuDocumentStatisticsForDashboard()
    if (response && response.data) {
      stats.documentCount = response.data.totalDocuments || 0

      // 处理分布数据
      docTypeData.value = response.data.docTypeDistribution || []
      syncStatusData.value = response.data.syncStatusDistribution || []
      subjectData.value = response.data.subjectDistribution || []
    }
  } catch (error) {
    console.error('加载飞书文档统计数据失败:', error)
  }
}

const loadRecentDocuments = async () => {
  try {
    const response = await getFeishuDocuments({
      page: 0,
      size: 5
    })
    if (response && response.data && response.data.content) {
      recentDocuments.value = response.data.content
    }
  } catch (error) {
    console.error('加载最近文档失败:', error)
  }
}

const loadPopularDocuments = async () => {
  try {
    const response = await getPopularFeishuDocumentsForDashboard(5)
    if (response && response.data) {
      popularDocuments.value = response.data
    }
  } catch (error) {
    console.error('加载热门文档失败:', error)
  }
}

// 工具方法
const getPercentage = (value, total) => {
  if (!total || total === 0) return 0
  return Math.round((value / total) * 100)
}

const getDocTypeColor = (type) => {
  const colors = {
    DOC: 'primary',
    SHEET: 'success',
    SLIDE: 'warning',
    MINDMAP: 'info',
    BITABLE: 'danger'
  }
  return colors[type] || 'default'
}

const getDocTypeText = (type) => {
  const texts = {
    DOC: '文档',
    SHEET: '表格',
    SLIDE: '演示文稿',
    MINDMAP: '思维导图',
    BITABLE: '多维表格'
  }
  return texts[type] || type
}

const getSyncStatusColor = (status) => {
  const colors = {
    SYNCED: 'success',
    PENDING: 'warning',
    SYNCING: 'primary',
    FAILED: 'danger'
  }
  return colors[status] || 'default'
}

const getSyncStatusText = (status) => {
  const texts = {
    SYNCED: '已同步',
    PENDING: '待同步',
    SYNCING: '同步中',
    FAILED: '同步失败'
  }
  return texts[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 导航方法
const viewDocument = (document) => {
  if (document.id) {
    router.push(`/feishu/view/${document.id}`)
  } else {
    ElMessage.warning('文档ID不存在，无法查看')
  }
}

const goToFeishuDocs = () => {
  router.push('/feishu')
}

const goToCreateDocument = () => {
  router.push('/feishu?action=create')
}

const goToImportDocument = () => {
  router.push('/feishu?action=import')
}

const goToSyncDocuments = () => {
  router.push('/feishu?action=sync')
}

const goToFeishuApi = () => {
  router.push('/feishu-api')
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 20px;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.subject-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.document-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.question-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
  padding: 10px;
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 类型统计样式 */
.type-stats, .status-stats, .subject-stats {
  height: 100%;
  overflow-y: auto;
}

.type-item, .status-item, .subject-item {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 6px;
  background: #f8f9fa;
}

.type-info, .status-info, .subject-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.type-count, .status-count, .subject-count {
  font-weight: bold;
  color: #303133;
}

.subject-name {
  font-weight: 500;
  color: #303133;
}

.type-progress, .status-progress, .subject-progress {
  margin-top: 5px;
}

.activity-row {
  margin-bottom: 20px;
}

.activity-card {
  height: 500px;
}

.activity-list {
  height: 420px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.activity-item:hover {
  background-color: #f8f9fa;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.activity-time, .activity-views {
  font-size: 12px;
  color: #909399;
}

.activity-actions {
  flex-shrink: 0;
}

.quick-actions-row {
  margin-bottom: 20px;
}

.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-card {
    margin-bottom: 10px;
  }

  .chart-card {
    height: 350px;
    margin-bottom: 15px;
  }

  .activity-card {
    height: 400px;
    margin-bottom: 15px;
  }

  .quick-actions {
    justify-content: center;
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .activity-content {
    width: 100%;
  }

  .activity-actions {
    width: 100%;
    text-align: right;
  }
}
</style>
