<template>
  <div class="feishu-page">
    <div class="page-header">
      <h1>飞书文档管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          添加文档
        </el-button>
        <el-button type="success" @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          导入文档
        </el-button>
        <el-button @click="autoSyncDocuments">
          <el-icon><Refresh /></el-icon>
          自动同步
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalDocuments || 0 }}</div>
              <div class="stat-label">总文档数</div>
            </div>
            <el-icon class="stat-icon"><Document /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card active">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.activeDocuments || 0 }}</div>
              <div class="stat-label">活跃文档</div>
            </div>
            <el-icon class="stat-icon"><CircleCheck /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.needSyncDocuments || 0 }}</div>
              <div class="stat-label">待同步</div>
            </div>
            <el-icon class="stat-icon"><Warning /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalViews || 0 }}</div>
              <div class="stat-label">总查看数</div>
            </div>
            <el-icon class="stat-icon"><View /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="文档标题">
          <el-input v-model="searchForm.title" placeholder="请输入文档标题" clearable />
        </el-form-item>
        <el-form-item label="文档类型">
          <el-select v-model="searchForm.docType" placeholder="选择类型" clearable>
            <el-option label="文档" value="DOC" />
            <el-option label="表格" value="SHEET" />
            <el-option label="演示文稿" value="SLIDE" />
            <el-option label="思维导图" value="MINDMAP" />
            <el-option label="多维表格" value="BITABLE" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="正常" value="ACTIVE" />
            <el-option label="已归档" value="ARCHIVED" />
            <el-option label="已删除" value="DELETED" />
            <el-option label="草稿" value="DRAFT" />
          </el-select>
        </el-form-item>
        <el-form-item label="学科">
          <el-select v-model="searchForm.subjectId" placeholder="选择学科" clearable>
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 文档列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">文档列表</div>
        <div class="table-actions">
          <el-button
            type="success"
            :disabled="selectedDocuments.length === 0"
            @click="batchSyncDocuments"
          >
            批量同步
          </el-button>
          <el-button
            type="info"
            @click="showPopularDocuments"
          >
            热门文档
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="documents"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="文档标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="docType" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getDocTypeColor(row.docType)">
              {{ getDocTypeText(row.docType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="docToken" label="文档Token" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.docToken" class="doc-token-cell">
              <el-tag size="small" type="info" class="doc-token-tag">
                {{ row.docToken }}
              </el-tag>
              <el-button
                size="small"
                text
                type="primary"
                @click="copyToClipboard(row.docToken)"
                class="copy-btn"
              >
                <el-icon><CopyDocument /></el-icon>
              </el-button>
            </div>
            <span v-else class="text-muted">未设置</span>
          </template>
        </el-table-column>
        <el-table-column prop="docUrl" label="文档URL" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.docUrl" class="doc-url-cell">
              <el-link :href="row.docUrl" target="_blank" type="primary" class="doc-url-link">
                {{ truncateUrl(row.docUrl) }}
              </el-link>
              <el-button
                size="small"
                text
                type="primary"
                @click="copyToClipboard(row.docUrl)"
                class="copy-btn"
              >
                <el-icon><CopyDocument /></el-icon>
              </el-button>
            </div>
            <span v-else class="text-muted">未设置</span>
          </template>
        </el-table-column>
        <el-table-column prop="subjectName" label="学科" width="120" show-overflow-tooltip />
        <el-table-column prop="creatorName" label="创建者" width="120" show-overflow-tooltip />
        <el-table-column prop="viewCount" label="查看次数" width="100" />
        <el-table-column prop="syncStatus" label="同步状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getSyncStatusColor(row.syncStatus)">
              {{ getSyncStatusText(row.syncStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastSyncAt" label="最后同步" width="180">
          <template #default="{ row }">
            <span v-if="row.lastSyncAt">{{ formatDateTime(row.lastSyncAt) }}</span>
            <span v-else class="text-muted">从未同步</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="350" fixed="right">
          <template #default="{ row }">
<!--            <el-button size="small" @click="viewDocument(row)">查看</el-button>-->
            <el-button size="small" type="primary" @click="editDocument(row)">编辑</el-button>
            <el-button size="small" type="warning" @click="editDocumentContent(row)">编辑内容</el-button>
            <el-button
              size="small"
              type="success"
              @click="syncDocument(row)"
              :loading="row.syncing"
            >
              同步
            </el-button>
            <el-button size="small" type="info" @click="openFeishuDoc(row)">打开</el-button>
            <el-button size="small" type="danger" @click="deleteDocument(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑文档' : '添加文档'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="文档标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入文档标题" />
        </el-form-item>
        <el-form-item label="飞书文档ID" prop="docId">
          <el-input v-model="form.docId" placeholder="请输入飞书文档ID" />
        </el-form-item>
        <el-form-item label="文档URL">
          <el-input
            v-model="form.docUrl"
            placeholder="请输入飞书文档URL"
            type="textarea"
            :rows="2"
            @input="handleDocUrlChange"
          />
          <div class="form-tip">
            <el-text size="small" type="info">
              例如: https://bytedance.feishu.cn/docs/doccnxxxxxx
            </el-text>
          </div>
        </el-form-item>
        <el-form-item label="文档Token">
          <el-input
            v-model="form.docToken"
            placeholder="从URL自动提取"
            readonly
            class="readonly-input"
          >
            <template #append>
              <el-button
                v-if="form.docToken"
                @click="copyToClipboard(form.docToken)"
                :icon="CopyDocument"
                title="复制Token"
              />
            </template>
          </el-input>
          <div class="form-tip">
            <el-text size="small" type="info">
              Token会从URL自动提取，用于API调用
            </el-text>
          </div>
        </el-form-item>
        <el-form-item label="文档类型" prop="docType">
          <el-select v-model="form.docType" placeholder="选择类型">
            <el-option label="文档" value="DOC" />
            <el-option label="表格" value="SHEET" />
            <el-option label="演示文稿" value="SLIDE" />
            <el-option label="思维导图" value="MINDMAP" />
            <el-option label="多维表格" value="BITABLE" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联学科">
          <el-select v-model="form.subjectId" placeholder="选择学科" clearable>
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文档分类">
          <el-input v-model="form.category" placeholder="请输入文档分类" />
        </el-form-item>
        <el-form-item label="文档状态" prop="status">
          <el-select v-model="form.status" placeholder="选择状态">
            <el-option label="正常" value="ACTIVE" />
            <el-option label="已归档" value="ARCHIVED" />
            <el-option label="草稿" value="DRAFT" />
          </el-select>
        </el-form-item>
        <el-form-item label="访问权限" prop="accessLevel">
          <el-select v-model="form.accessLevel" placeholder="选择权限">
            <el-option label="公开" value="PUBLIC" />
            <el-option label="内部" value="INTERNAL" />
            <el-option label="私有" value="PRIVATE" />
            <el-option label="受限" value="RESTRICTED" />
          </el-select>
        </el-form-item>
        <el-form-item label="文档摘要">
          <el-input
            v-model="form.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入文档摘要"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="form.isPublic">公开文档</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入飞书文档"
      width="600px"
    >
      <el-form label-width="120px">
        <el-form-item label="文档URL">
          <el-input
            v-model="importUrl"
            placeholder="请输入飞书文档URL"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="批量导入">
          <el-input
            v-model="batchImportUrls"
            placeholder="请输入多个URL，每行一个"
            type="textarea"
            :rows="5"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="importDocument" :loading="importing">
          导入
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Refresh, Document, CircleCheck, Warning, View, CopyDocument } from '@element-plus/icons-vue'
import {
  getFeishuDocuments,
  createFeishuDocument,
  updateFeishuDocument,
  deleteFeishuDocument,
  syncFeishuDocument,
  batchSyncFeishuDocuments,
  importFeishuDocument,
  batchImportFeishuDocuments,
  autoSyncFeishuDocuments,
  getFeishuDocumentStatistics,
  getPopularFeishuDocuments
} from '@/api/feishu'
import { getActiveSubjects } from '@/api/subjects'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const importing = ref(false)
const dialogVisible = ref(false)
const importDialogVisible = ref(false)
const isEdit = ref(false)

const documents = ref([])
const selectedDocuments = ref([])
const currentDocument = ref(null)
const statistics = ref({
  totalDocuments: 0,
  activeDocuments: 0,
  needSyncDocuments: 0,
  totalViews: 0
})
const subjects = ref([])

const importUrl = ref('')
const batchImportUrls = ref('')

// 搜索表单
const searchForm = reactive({
  title: '',
  docType: '',
  status: '',
  subjectId: null
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({
  title: '',
  docId: '',
  docUrl: '',
  docToken: '',
  docType: '',
  status: 'ACTIVE',
  subjectId: null,
  category: '',
  accessLevel: 'INTERNAL',
  summary: '',
  isPublic: false
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入文档标题', trigger: 'blur' }
  ],
  docId: [
    { required: true, message: '请输入飞书文档ID', trigger: 'blur' }
  ],
  docType: [
    { required: true, message: '请选择文档类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择文档状态', trigger: 'change' }
  ],
  accessLevel: [
    { required: true, message: '请选择访问权限', trigger: 'change' }
  ]
}

// 方法
const loadDocuments = async () => {
  debugger;
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      ...searchForm
    }
    console.log('加载文档列表，参数:', params)
    const response = await getFeishuDocuments(params)
    console.log('文档列表响应:', response)

    if (response && response.content) {
      debugger;
      documents.value = response.content || []
      pagination.total = response.totalElements || 0
      console.log('文档列表加载成功，共', pagination.total, '条记录')
    } else {
      console.error('响应数据格式异常:', response)
      documents.value = []
      pagination.total = 0
      ElMessage.warning('文档列表数据格式异常')
    }
  } catch (error) {
    console.error('加载文档列表失败:', error)
    documents.value = []
    pagination.total = 0

    // 更详细的错误信息
    let errorMessage = '加载文档列表失败'
    if (error.response) {
      errorMessage += `: ${error.response.status} ${error.response.statusText}`
      if (error.response.data && error.response.data.message) {
        errorMessage += ` - ${error.response.data.message}`
      }
    } else if (error.message) {
      errorMessage += `: ${error.message}`
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await getFeishuDocumentStatistics()
    // 确保返回的数据包含所有必需的字段，如果没有则设置默认值
    statistics.value = {
      totalDocuments: 0,
      activeDocuments: 0,
      needSyncDocuments: 0,
      totalViews: 0,
      ...response
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
    // 设置默认统计数据以防止模板错误
    statistics.value = {
      totalDocuments: 0,
      activeDocuments: 0,
      needSyncDocuments: 0,
      totalViews: 0
    }
    ElMessage.error('加载统计信息失败')
  }
}

const loadSubjects = async () => {
  try {
    const response = await getActiveSubjects()
    subjects.value = response.data
  } catch (error) {
    console.error('加载学科列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadDocuments()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    docType: '',
    status: '',
    subjectId: null
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadDocuments()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadDocuments()
}

const handleSelectionChange = (selection) => {
  selectedDocuments.value = selection
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

const editDocument = (document) => {
  isEdit.value = true
  dialogVisible.value = true

  // 手动赋值确保所有字段都正确映射
  Object.assign(form, {
    title: document.title || '',
    docId: document.docId || '',
    docUrl: document.docUrl || '',
    docToken: document.docToken || extractDocTokenFromUrl(document.docUrl || ''),
    docType: document.docType || '',
    status: document.status || 'ACTIVE',
    subjectId: document.subjectId || null,
    category: document.category || '',
    accessLevel: document.accessLevel || 'INTERNAL',
    summary: document.summary || '',
    isPublic: document.isPublic || false
  })

  currentDocument.value = document
}

const viewDocument = (document) => {
  // 跳转到文档查看页面，使用文档ID
  if (document.id) {
    router.push(`/feishu/view/${document.id}`)
  } else {
    ElMessage.warning('文档ID不存在，无法查看')
  }
}

const editDocumentContent = (document) => {
  // 跳转到文档内容编辑页面，使用文档ID
  if (document.id) {
    router.push(`/feishu/edit/${document.id}`)
  } else {
    ElMessage.warning('文档ID不存在，无法编辑')
  }
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEdit.value) {
      await updateFeishuDocument(currentDocument.value.id, form)
      ElMessage.success('更新成功')
    } else {
      await createFeishuDocument(form)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadDocuments()
    loadStatistics()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

const deleteDocument = async (document) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文档 "${document.title}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteFeishuDocument(document.id)
    ElMessage.success('删除成功')
    loadDocuments()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const syncDocument = async (document) => {
  document.syncing = true
  try {
    await syncFeishuDocument(document.id)
    ElMessage.success('同步成功')
    loadDocuments()
  } catch (error) {
    ElMessage.error('同步失败')
  } finally {
    document.syncing = false
  }
}

const batchSyncDocuments = async () => {
  try {
    const documentIds = selectedDocuments.value.map(doc => doc.id)
    await batchSyncFeishuDocuments(documentIds)
    ElMessage.success('批量同步成功')
    loadDocuments()
    loadStatistics()
  } catch (error) {
    ElMessage.error('批量同步失败')
  }
}

const autoSyncDocuments = async () => {
  try {
    await autoSyncFeishuDocuments()
    ElMessage.success('自动同步完成')
    loadDocuments()
    loadStatistics()
  } catch (error) {
    ElMessage.error('自动同步失败')
  }
}

const showImportDialog = () => {
  importDialogVisible.value = true
  importUrl.value = ''
  batchImportUrls.value = ''
}

const importDocument = async () => {
  if (!importUrl.value && !batchImportUrls.value) {
    ElMessage.warning('请输入要导入的文档URL')
    return
  }

  importing.value = true
  try {
    if (batchImportUrls.value) {
      const urls = batchImportUrls.value.split('\n').filter(url => url.trim())
      await batchImportFeishuDocuments(urls)
      ElMessage.success(`成功导入 ${urls.length} 个文档`)
    } else {
      await importFeishuDocument(importUrl.value)
      ElMessage.success('导入成功')
    }

    importDialogVisible.value = false
    loadDocuments()
    loadStatistics()
  } catch (error) {
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

const showPopularDocuments = async () => {
  try {
    const response = await getPopularFeishuDocuments(10)
    const popularDocs = response.data

    let message = '热门文档：\n'
    popularDocs.forEach((doc, index) => {
      message += `${index + 1}. ${doc.title} (${doc.viewCount} 次查看)\n`
    })

    ElMessageBox.alert(message, '热门文档', {
      confirmButtonText: '确定'
    })
  } catch (error) {
    ElMessage.error('获取热门文档失败')
  }
}

const openFeishuDoc = (document) => {
  if (document.docUrl) {
    window.open(document.docUrl, '_blank')
  } else {
    ElMessage.warning('文档URL不存在')
  }
}

const resetForm = () => {
  Object.assign(form, {
    title: '',
    docId: '',
    docUrl: '',
    docToken: '',
    docType: '',
    status: 'ACTIVE',
    subjectId: null,
    category: '',
    accessLevel: 'INTERNAL',
    summary: '',
    isPublic: false
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 处理URL变化，自动提取docToken
const handleDocUrlChange = (url) => {
  form.docToken = extractDocTokenFromUrl(url)
}

// 从URL中提取docToken的前端实现
const extractDocTokenFromUrl = (url) => {
  if (!url || url.trim() === '') {
    return ''
  }

  try {
    // 移除查询参数和锚点
    const cleanUrl = url.split('?')[0].split('#')[0]

    // 提取最后一段作为token
    const parts = cleanUrl.split('/')
    if (parts.length > 0) {
      const lastPart = parts[parts.length - 1]
      // 验证token格式
      if (isValidDocToken(lastPart)) {
        return lastPart
      }
    }
  } catch (error) {
    console.warn('提取docToken失败:', error)
  }

  return ''
}

// 验证docToken格式
const isValidDocToken = (token) => {
  if (!token || token.length < 10) {
    return false
  }

  // 飞书文档token通常以特定前缀开头
  return token.startsWith('doccn') ||  // 文档
         token.startsWith('shtcn') ||  // 表格
         token.startsWith('sldcn') ||  // 演示文稿
         token.startsWith('bmncn') ||  // 思维笔记
         token.startsWith('bascn') ||  // 多维表格
         token.startsWith('wikicn') || // 知识库
         token.startsWith('fldcn')     // 文件夹
}

// URL处理方法
const truncateUrl = (url) => {
  if (!url) return ''
  if (url.length <= 50) return url
  return url.substring(0, 47) + '...'
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('URL已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级处理：创建临时文本域
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('URL已复制到剪贴板')
    } catch (fallbackError) {
      ElMessage.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

// 辅助方法
const getDocTypeColor = (type) => {
  const colors = {
    DOC: 'primary',
    SHEET: 'success',
    SLIDE: 'warning',
    MINDMAP: 'info',
    BITABLE: 'danger'
  }
  return colors[type] || 'default'
}

const getDocTypeText = (type) => {
  const texts = {
    DOC: '文档',
    SHEET: '表格',
    SLIDE: '演示文稿',
    MINDMAP: '思维导图',
    BITABLE: '多维表格'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    ACTIVE: 'success',
    ARCHIVED: 'warning',
    DELETED: 'danger',
    DRAFT: 'info'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    ACTIVE: '正常',
    ARCHIVED: '已归档',
    DELETED: '已删除',
    DRAFT: '草稿'
  }
  return texts[status] || status
}

const getSyncStatusColor = (status) => {
  const colors = {
    SYNCED: 'success',
    PENDING: 'warning',
    SYNCING: 'primary',
    FAILED: 'danger'
  }
  return colors[status] || 'default'
}

const getSyncStatusText = (status) => {
  const texts = {
    SYNCED: '已同步',
    PENDING: '待同步',
    SYNCING: '同步中',
    FAILED: '同步失败'
  }
  return texts[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadDocuments()
  loadStatistics()
  loadSubjects()
})
</script>

<style scoped>
.feishu-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card .el-card__body {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  font-size: 40px;
  opacity: 0.3;
}

.stat-card.active .stat-number,
.stat-card.active .stat-icon {
  color: #409eff;
}

.stat-card.warning .stat-number,
.stat-card.warning .stat-icon {
  color: #e6a23c;
}

.stat-card.success .stat-number,
.stat-card.success .stat-icon {
  color: #67c23a;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.text-muted {
  color: #909399;
}

/* 文档URL相关样式 */
.doc-url-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.doc-url-link {
  flex: 1;
  min-width: 0;
  text-decoration: none;
}

.copy-btn {
  flex-shrink: 0;
  padding: 4px;
  min-height: auto;
}

.copy-btn:hover {
  background-color: var(--el-color-primary-light-9);
}

.form-tip {
  margin-top: 4px;
}

/* 文档Token相关样式 */
.doc-token-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.doc-token-tag {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.readonly-input {
  background-color: #f5f7fa;
}

.readonly-input .el-input__inner {
  background-color: #f5f7fa;
  color: #606266;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-dialog .el-form {
  padding: 0 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feishu-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
    flex-wrap: wrap;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .el-table {
    font-size: 12px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .page-header h1 {
    color: #e5eaf3;
  }

  .stat-number {
    color: #e5eaf3;
  }

  .stat-label {
    color: #a3a6ad;
  }

  .table-title {
    color: #e5eaf3;
  }
}
</style>
