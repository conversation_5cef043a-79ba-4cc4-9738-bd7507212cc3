package com.lait.service;

import com.lait.entity.PointsConfig;
import com.lait.entity.UserPoints;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 积分服务接口
 */
public interface PointsService {

    // ========== 积分配置管理 ==========
    
    /**
     * 创建积分配置
     */
    PointsConfig createPointsConfig(PointsConfig config);

    /**
     * 更新积分配置
     */
    PointsConfig updatePointsConfig(Long id, PointsConfig config);

    /**
     * 删除积分配置
     */
    void deletePointsConfig(Long id);

    /**
     * 根据ID获取积分配置
     */
    PointsConfig getPointsConfigById(Long id);

    /**
     * 根据配置键获取积分配置
     */
    PointsConfig getPointsConfigByKey(String configKey);

    /**
     * 分页查询积分配置
     */
    Page<PointsConfig> getPointsConfigs(String displayName, PointsConfig.ConfigCategory category, 
                                       Boolean enabled, Pageable pageable);

    /**
     * 获取启用的积分配置
     */
    List<PointsConfig> getEnabledPointsConfigs();

    /**
     * 根据分类获取积分配置
     */
    List<PointsConfig> getPointsConfigsByCategory(PointsConfig.ConfigCategory category);

    /**
     * 批量更新积分配置状态
     */
    void batchUpdatePointsConfigStatus(List<Long> ids, Boolean enabled);

    // ========== 用户积分管理 ==========
    
    /**
     * 给用户添加积分
     */
    UserPoints addPoints(Long userId, String configKey, Long relatedId, String relatedType, String description);

    /**
     * 给用户扣除积分
     */
    UserPoints deductPoints(Long userId, Integer points, String source, Long relatedId, String relatedType, String description);

    /**
     * 获取用户当前积分
     */
    Integer getUserCurrentPoints(Long userId);

    /**
     * 获取用户积分记录
     */
    Page<UserPoints> getUserPointsHistory(Long userId, Pageable pageable);

    /**
     * 分页查询积分记录
     */
    Page<UserPoints> getPointsRecords(Long userId, UserPoints.PointsType pointsType, String source,
                                     LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 获取用户今日积分变化
     */
    Integer getUserTodayPointsChange(Long userId);

    /**
     * 获取用户积分趋势
     */
    List<Map<String, Object>> getUserPointsTrend(Long userId, int days);

    /**
     * 获取积分排行榜
     */
    List<Map<String, Object>> getPointsRanking(int limit);

    // ========== 积分统计 ==========
    
    /**
     * 获取积分配置统计
     */
    Map<String, Object> getPointsConfigStatistics();

    /**
     * 获取积分记录统计
     */
    Map<String, Object> getPointsRecordStatistics();

    /**
     * 获取用户积分统计
     */
    Map<String, Object> getUserPointsStatistics(Long userId);

    // ========== 积分规则 ==========
    
    /**
     * 检查用户今日是否可以获得指定积分
     */
    boolean canEarnPointsToday(Long userId, String configKey);

    /**
     * 初始化默认积分配置
     */
    void initializeDefaultPointsConfig();

    /**
     * 重置用户积分
     */
    void resetUserPoints(Long userId, String reason);

    /**
     * 批量调整用户积分
     */
    void batchAdjustUserPoints(List<Long> userIds, Integer points, String reason);
}
