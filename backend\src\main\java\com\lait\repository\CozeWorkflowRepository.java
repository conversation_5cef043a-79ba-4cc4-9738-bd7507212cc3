package com.lait.repository;

import com.lait.entity.CozeWorkflow;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Coze工作流Repository
 */
@Repository
public interface CozeWorkflowRepository extends JpaRepository<CozeWorkflow, Long> {

    /**
     * 根据名称查找工作流
     */
    Optional<CozeWorkflow> findByName(String name);

    /**
     * 检查名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 根据工作流ID查找
     */
    Optional<CozeWorkflow> findByWorkflowId(String workflowId);

    /**
     * 根据状态查找工作流
     */
    List<CozeWorkflow> findByStatus(CozeWorkflow.WorkflowStatus status);

    /**
     * 根据类型查找工作流
     */
    List<CozeWorkflow> findByType(CozeWorkflow.WorkflowType type);

    /**
     * 根据创建者查找工作流
     */
    List<CozeWorkflow> findByCreatorId(Long creatorId);

    /**
     * 根据分类查找工作流
     */
    List<CozeWorkflow> findByCategory(String category);

    /**
     * 查找公开的工作流
     */
    List<CozeWorkflow> findByIsPublicTrue();

    /**
     * 分页查询工作流
     */
    @Query("SELECT w FROM CozeWorkflow w WHERE " +
           "(:name IS NULL OR w.name LIKE %:name%) AND " +
           "(:type IS NULL OR w.type = :type) AND " +
           "(:status IS NULL OR w.status = :status) AND " +
           "(:category IS NULL OR w.category = :category) AND " +
           "(:creatorId IS NULL OR w.creatorId = :creatorId)")
    Page<CozeWorkflow> findWorkflows(@Param("name") String name,
                                   @Param("type") CozeWorkflow.WorkflowType type,
                                   @Param("status") CozeWorkflow.WorkflowStatus status,
                                   @Param("category") String category,
                                   @Param("creatorId") Long creatorId,
                                   Pageable pageable);

    /**
     * 查找最近执行的工作流
     */
    @Query("SELECT w FROM CozeWorkflow w WHERE w.lastExecutedAt IS NOT NULL ORDER BY w.lastExecutedAt DESC")
    List<CozeWorkflow> findRecentlyExecuted(Pageable pageable);

    /**
     * 查找热门工作流（按执行次数排序）
     */
    @Query("SELECT w FROM CozeWorkflow w WHERE w.executionCount > 0 ORDER BY w.executionCount DESC")
    List<CozeWorkflow> findPopularWorkflows(Pageable pageable);

    /**
     * 统计工作流数量
     */
    @Query("SELECT COUNT(w) FROM CozeWorkflow w WHERE w.status = :status")
    long countByStatus(@Param("status") CozeWorkflow.WorkflowStatus status);

    /**
     * 统计各类型工作流数量
     */
    @Query("SELECT w.type, COUNT(w) FROM CozeWorkflow w GROUP BY w.type")
    List<Object[]> countByType();

    /**
     * 查找需要更新的工作流（长时间未执行）
     */
    @Query("SELECT w FROM CozeWorkflow w WHERE w.lastExecutedAt < :threshold OR w.lastExecutedAt IS NULL")
    List<CozeWorkflow> findStaleWorkflows(@Param("threshold") LocalDateTime threshold);
}
