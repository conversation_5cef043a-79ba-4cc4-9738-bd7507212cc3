package com.lait.service.impl;

import com.lait.dto.UserDTO;
import com.lait.entity.User;
import com.lait.repository.UserRepository;
import com.lait.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public UserDTO createUser(UserDTO.CreateUserRequest request) {
        log.info("创建用户: {}", request.getUsername());

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (request.getEmail() != null && userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }

        User user = new User();
        BeanUtils.copyProperties(request, user);
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setStatus(User.UserStatus.ACTIVE);

        User savedUser = userRepository.save(user);
        return convertToDTO(savedUser);
    }

    @Override
    public UserDTO getUserById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        return convertToDTO(user);
    }

    @Override
    public UserDTO getUserByUsername(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        return convertToDTO(user);
    }

    @Override
    @Transactional
    public UserDTO updateUser(Long id, UserDTO.UpdateUserRequest request) {
        log.info("更新用户信息: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 检查邮箱是否已被其他用户使用
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new RuntimeException("邮箱已被其他用户使用");
            }
        }

        BeanUtils.copyProperties(request, user, "id", "username", "password", "role");
        User savedUser = userRepository.save(user);
        return convertToDTO(savedUser);
    }

    @Override
    @Transactional
    public void deleteUser(Long id) {
        log.info("删除用户: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setIsDeleted(true);
        userRepository.save(user);
    }

    @Override
    @Transactional
    public void changePassword(Long id, UserDTO.ChangePasswordRequest request) {
        log.info("修改用户密码: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new RuntimeException("原密码不正确");
        }

        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);
    }

    @Override
    public Page<UserDTO> getUsers(Pageable pageable) {
        Page<User> users = userRepository.findAllActive(pageable);
        return users.map(this::convertToDTO);
    }

    @Override
    public Page<UserDTO> getUsersByRole(User.UserRole role, Pageable pageable) {
        Page<User> users = userRepository.findByRoleAndNotDeleted(role, pageable);
        return users.map(this::convertToDTO);
    }

    @Override
    public Page<UserDTO> searchUsers(String keyword, Pageable pageable) {
        Page<User> users = userRepository.searchUsers(keyword, pageable);
        return users.map(this::convertToDTO);
    }

    @Override
    public List<UserDTO> getStudentsByGrade(Integer gradeLevel) {
        List<User> students = userRepository.findByRoleAndGradeLevel(User.UserRole.STUDENT, gradeLevel);
        return students.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<UserDTO> getStudentsByClass(String className) {
        List<User> students = userRepository.findByRoleAndClassName(User.UserRole.STUDENT, className);
        return students.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    @Transactional
    public void activateUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        user.setStatus(User.UserStatus.ACTIVE);
        userRepository.save(user);
    }

    @Override
    @Transactional
    public void deactivateUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        user.setStatus(User.UserStatus.INACTIVE);
        userRepository.save(user);
    }

    @Override
    @Transactional
    public void batchDeleteUsers(List<Long> userIds) {
        for (Long userId : userIds) {
            deleteUser(userId);
        }
    }

    @Override
    @Transactional
    public void batchUpdateUserStatus(List<Long> userIds, User.UserStatus status) {
        for (Long userId : userIds) {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在: " + userId));
            user.setStatus(status);
            userRepository.save(user);
        }
    }

    @Override
    @Transactional
    public String resetUserPassword(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 生成随机密码
        String newPassword = generateRandomPassword();
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        return newPassword;
    }

    @Override
    public Map<String, Object> getUserStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总用户数
        stats.put("totalUsers", userRepository.count());

        // 按角色统计 (简化实现)
        stats.put("adminCount", userRepository.findByRole(User.UserRole.ADMIN).size());
        stats.put("teacherCount", userRepository.findByRole(User.UserRole.TEACHER).size());
        stats.put("studentCount", userRepository.findByRole(User.UserRole.STUDENT).size());

        // 按状态统计 (简化实现)
        stats.put("activeCount", userRepository.findByStatus(User.UserStatus.ACTIVE).size());
        stats.put("inactiveCount", userRepository.findByStatus(User.UserStatus.INACTIVE).size());
        stats.put("suspendedCount", userRepository.findByStatus(User.UserStatus.SUSPENDED).size());

        return stats;
    }

    @Override
    public Map<String, Object> getUserStudyStatistics(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        Map<String, Object> stats = new HashMap<>();
        stats.put("userId", id);
        stats.put("username", user.getUsername());
        stats.put("realName", user.getRealName());

        // 这里可以添加更多学习统计信息
        // 例如：完成的题目数、笔记数、成绩等
        stats.put("totalQuestions", 0);
        stats.put("totalNotes", 0);
        stats.put("averageScore", 0.0);

        return stats;
    }

    @Override
    public String exportUsers(List<Long> userIds, String format) {
        List<User> users;
        if (userIds != null && !userIds.isEmpty()) {
            users = userRepository.findAllById(userIds);
        } else {
            users = userRepository.findAll();
        }

        // 简化实现，实际应该根据format生成不同格式的数据
        StringBuilder sb = new StringBuilder();
        if ("CSV".equalsIgnoreCase(format)) {
            sb.append("ID,用户名,真实姓名,邮箱,角色,状态\n");
            for (User user : users) {
                sb.append(user.getId()).append(",")
                  .append(user.getUsername()).append(",")
                  .append(user.getRealName()).append(",")
                  .append(user.getEmail()).append(",")
                  .append(user.getRole()).append(",")
                  .append(user.getStatus()).append("\n");
            }
        }

        return sb.toString();
    }

    @Override
    @Transactional
    public Map<String, Object> importUsers(String data, String format) {
        Map<String, Object> result = new HashMap<>();

        // 简化实现，实际应该解析不同格式的数据
        result.put("success", true);
        result.put("importedCount", 0);
        result.put("failedCount", 0);
        result.put("message", "导入功能待实现");

        return result;
    }

    /**
     * 生成随机密码
     */
    private String generateRandomPassword() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder password = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }
        return password.toString();
    }

    /**
     * 实体转DTO
     */
    private UserDTO convertToDTO(User user) {
        UserDTO dto = new UserDTO();
        BeanUtils.copyProperties(user, dto);
        return dto;
    }
}
