@echo off
echo 启动智能学习系统...

echo.
echo 1. 启动后端服务...
cd backend
start "后端服务" cmd /k "mvn spring-boot:run"

echo.
echo 2. 启动管理界面...
cd ..\admin-frontend
start "管理界面" cmd /k "npm run dev"

echo.
echo 3. 启动学生界面...
cd ..\student-frontend
start "学生界面" cmd /k "npm run dev"

echo.
echo 系统启动完成！
echo 后端服务: http://localhost:8080
echo 管理界面: http://localhost:3000
echo 学生界面: http://localhost:3001
echo.
echo 默认管理员账号: admin / 123456
pause
