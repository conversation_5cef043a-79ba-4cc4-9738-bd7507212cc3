# 飞书文档编辑器增强指南

## 功能概述

为飞书文档编辑页面添加了专业的Markdown和HTML编辑器组件，提供更好的编辑体验和功能。

## 新增编辑器功能

### 1. Markdown编辑器 (ByteMD)

**功能特性：**
- 🎯 **专业Markdown编辑器**：基于ByteMD，支持完整的Markdown语法
- 📝 **实时预览**：左侧编辑，右侧实时预览
- 🔧 **丰富工具栏**：粗体、斜体、标题、链接、图片、表格等
- 🎨 **语法高亮**：代码块语法高亮显示
- 📋 **GFM支持**：支持GitHub Flavored Markdown扩展语法
- 🌐 **中文界面**：完整的中文本地化

**支持的Markdown语法：**
- 标题 (H1-H6)
- 粗体、斜体、删除线
- 有序列表、无序列表、任务列表
- 链接和图片
- 代码块和行内代码
- 表格
- 引用块
- 分割线

### 2. HTML编辑器 (Monaco Editor)

**功能特性：**
- 💻 **专业代码编辑器**：基于VS Code的Monaco Editor
- 🎨 **语法高亮**：HTML语法高亮和智能提示
- 🔍 **代码折叠**：支持代码块折叠和展开
- 📏 **行号显示**：清晰的行号和代码结构
- 🌙 **暗色主题**：护眼的暗色编辑主题
- 🔧 **智能缩进**：自动缩进和格式化
- 🗺️ **代码地图**：右侧小地图导航

**编辑器特性：**
- 自动补全
- 括号匹配
- 多光标编辑
- 查找替换
- 代码格式化

### 3. 分屏编辑模式

**功能特性：**
- 📱 **左右分屏**：左侧编辑，右侧实时预览
- 🔄 **实时同步**：编辑内容实时渲染到预览区
- 📏 **可调节布局**：左右面板等宽显示
- 🎯 **专注编辑**：简洁的分屏界面设计

### 4. 纯预览模式

**功能特性：**
- 👁️ **只读预览**：查看渲染后的最终效果
- 📖 **完整样式**：保持与查看页面一致的样式
- 🖨️ **打印友好**：适合打印和分享的格式

## 编辑模式说明

### 模式切换
在编辑器顶部工具栏可以切换四种编辑模式：

1. **Markdown编辑器** - 专业的Markdown编辑环境
2. **HTML编辑器** - 专业的HTML代码编辑环境  
3. **分屏编辑** - 左侧编辑，右侧预览的分屏模式
4. **预览** - 纯预览模式，查看最终渲染效果

### 使用建议

**Markdown编辑器适用于：**
- 快速编写结构化文档
- 需要丰富格式的文档
- 协作编辑和版本控制
- 技术文档和说明

**HTML编辑器适用于：**
- 需要精确控制样式
- 复杂的页面布局
- 嵌入特殊元素
- 高级格式定制

**分屏编辑适用于：**
- 需要实时查看效果
- 学习Markdown语法
- 调试格式问题
- 长文档编辑

## 技术实现

### 依赖包
```json
{
  "@bytemd/vue-next": "^1.21.0",
  "@bytemd/plugin-gfm": "^1.21.0", 
  "@bytemd/plugin-highlight": "^1.21.0",
  "monaco-editor": "^0.44.0"
}
```

### 核心组件
- **ByteMD**: Vue 3兼容的Markdown编辑器
- **Monaco Editor**: VS Code核心编辑器
- **highlight.js**: 代码语法高亮
- **GitHub Flavored Markdown**: 扩展Markdown语法支持

### 样式集成
- 完整的主题定制
- 与Element Plus组件库样式统一
- 响应式设计支持
- 暗色主题支持

## 使用方法

### 基本操作
1. 进入飞书文档管理页面
2. 点击任意文档的"编辑内容"按钮
3. 在顶部工具栏选择编辑模式
4. 开始编辑文档内容
5. 点击"保存文档"保存更改

### Markdown编辑器操作
1. 选择"Markdown编辑器"模式
2. 使用工具栏快速插入格式
3. 在左侧编辑区输入Markdown语法
4. 右侧实时预览渲染效果
5. 支持拖拽调整预览区大小

### HTML编辑器操作
1. 选择"HTML编辑器"模式
2. 在暗色主题编辑器中编写HTML
3. 享受语法高亮和智能提示
4. 使用Ctrl+S快速保存
5. 支持代码折叠和小地图导航

### 分屏编辑操作
1. 选择"分屏编辑"模式
2. 左侧输入Markdown内容
3. 右侧实时查看预览效果
4. 适合学习和调试使用

## 快捷键支持

### 通用快捷键
- `Ctrl + S`: 保存文档
- `Ctrl + Z`: 撤销操作
- `Ctrl + Y`: 重做操作
- `Ctrl + A`: 全选内容
- `Ctrl + C/V`: 复制粘贴

### Markdown编辑器快捷键
- `Ctrl + B`: 粗体
- `Ctrl + I`: 斜体
- `Ctrl + K`: 插入链接
- `Ctrl + Shift + C`: 插入代码块
- `Ctrl + Shift + T`: 插入表格

### HTML编辑器快捷键
- `Ctrl + /`: 注释/取消注释
- `Ctrl + D`: 选择下一个相同内容
- `Ctrl + F`: 查找
- `Ctrl + H`: 替换
- `Alt + Shift + F`: 格式化代码

## 性能优化

### 编辑器优化
- 懒加载编辑器组件
- 按需初始化Monaco编辑器
- 自动清理编辑器实例
- 优化大文档渲染性能

### 内存管理
- 组件卸载时自动清理
- 避免内存泄漏
- 优化事件监听器

## 故障排除

### 常见问题

**编辑器无法加载**
- 检查网络连接
- 清除浏览器缓存
- 刷新页面重试

**Monaco编辑器显示异常**
- 确保容器元素存在
- 检查控制台错误信息
- 尝试切换到其他编辑模式

**保存失败**
- 检查内容格式
- 确认网络连接正常
- 查看后端服务状态

### 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 后续计划

### 功能增强
- [ ] 添加更多Markdown插件
- [ ] 支持图片上传和管理
- [ ] 添加协同编辑功能
- [ ] 支持文档模板
- [ ] 添加版本历史

### 性能优化
- [ ] 虚拟滚动支持
- [ ] 增量渲染优化
- [ ] 离线编辑支持
- [ ] 自动保存功能

## 总结

新的编辑器功能显著提升了飞书文档的编辑体验：

✅ **专业编辑器**: 提供VS Code级别的编辑体验
✅ **多模式支持**: 满足不同用户的编辑需求  
✅ **实时预览**: 所见即所得的编辑体验
✅ **丰富功能**: 完整的Markdown和HTML编辑支持
✅ **良好性能**: 优化的加载和渲染性能
✅ **用户友好**: 直观的界面和操作方式

这些增强功能让飞书文档编辑更加专业、高效和用户友好！
