<template>
  <div class="profile">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <van-image
        round
        width="80"
        height="80"
        :src="authStore.user?.avatarUrl"
        fit="cover"
        class="avatar"
      >
        <template #error>
          <div class="avatar-placeholder">
            {{ authStore.user?.realName?.charAt(0) || 'U' }}
          </div>
        </template>
      </van-image>
      
      <div class="user-info">
        <div class="name">{{ authStore.user?.realName || authStore.user?.username }}</div>
        <div class="details">
          <span>{{ gradeText }}</span>
          <span v-if="authStore.user?.email">{{ authStore.user.email }}</span>
        </div>
      </div>
      
      <van-button type="primary" size="small" @click="showEditProfile">
        编辑资料
      </van-button>
    </div>

    <!-- 学习数据 -->
    <van-cell-group title="学习数据" class="data-group">
      <van-cell
        v-for="item in studyData"
        :key="item.key"
        :title="item.label"
        :value="item.value"
        :label="item.description"
      >
        <template #icon>
          <van-icon :name="item.icon" class="data-icon" />
        </template>
      </van-cell>
    </van-cell-group>

    <!-- 功能菜单 -->
    <van-cell-group title="功能菜单" class="menu-group">
      <van-cell
        v-for="menu in menuItems"
        :key="menu.key"
        :title="menu.title"
        :label="menu.description"
        is-link
        @click="handleMenuClick(menu.key)"
      >
        <template #icon>
          <van-icon :name="menu.icon" class="menu-icon" />
        </template>
      </van-cell>
    </van-cell-group>

    <!-- 设置 -->
    <van-cell-group title="设置" class="settings-group">
      <van-cell
        v-for="setting in settings"
        :key="setting.key"
        :title="setting.title"
        is-link
        @click="handleSettingClick(setting.key)"
      >
        <template #icon>
          <van-icon :name="setting.icon" class="setting-icon" />
        </template>
      </van-cell>
    </van-cell-group>

    <!-- 退出登录 -->
    <div class="logout-section">
      <van-button type="danger" block @click="handleLogout">
        退出登录
      </van-button>
    </div>

    <!-- 编辑资料弹窗 -->
    <van-popup v-model:show="showEditDialog" position="bottom" :style="{ height: '60%' }">
      <div class="edit-profile">
        <van-nav-bar
          title="编辑资料"
          left-text="取消"
          right-text="保存"
          @click-left="showEditDialog = false"
          @click-right="saveProfile"
        />
        
        <van-form @submit="saveProfile" class="profile-form">
          <van-cell-group>
            <van-field
              v-model="editForm.realName"
              name="realName"
              label="真实姓名"
              placeholder="请输入真实姓名"
              :rules="[{ required: true, message: '请输入真实姓名' }]"
            />
            <van-field
              v-model="editForm.email"
              name="email"
              label="邮箱"
              placeholder="请输入邮箱"
              :rules="[{ pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱格式' }]"
            />
            <van-field
              v-model="editForm.phoneNumber"
              name="phoneNumber"
              label="手机号"
              placeholder="请输入手机号"
            />
          </van-cell-group>
        </van-form>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const showEditDialog = ref(false)

// 计算年级文本
const gradeText = computed(() => {
  const grade = authStore.user?.gradeLevel
  const className = authStore.user?.className
  if (grade && className) {
    return `${grade}年级 ${className}`
  } else if (grade) {
    return `${grade}年级`
  }
  return '学生'
})

// 编辑表单
const editForm = reactive({
  realName: '',
  email: '',
  phoneNumber: ''
})

// 学习数据
const studyData = ref([
  {
    key: 'total_study_time',
    label: '总学习时长',
    value: '0小时',
    description: '本月已学习',
    icon: 'clock-o'
  },
  {
    key: 'completed_courses',
    label: '完成课程',
    value: '0门',
    description: '累计完成',
    icon: 'completed'
  },
  {
    key: 'practice_count',
    label: '练习次数',
    value: '0次',
    description: '本周练习',
    icon: 'edit'
  },
  {
    key: 'achievement_count',
    label: '获得成就',
    value: '0个',
    description: '累计获得',
    icon: 'medal-o'
  }
])

// 功能菜单
const menuItems = ref([
  {
    key: 'wrong_questions',
    title: '错题本',
    description: '查看和复习错题',
    icon: 'warning-o'
  },
  {
    key: 'notes',
    title: '我的笔记',
    description: '管理学习笔记',
    icon: 'notes-o'
  },
  {
    key: 'grades',
    title: '成绩查询',
    description: '查看考试成绩',
    icon: 'chart-trending-o'
  },
  {
    key: 'study_report',
    title: '学习报告',
    description: '查看学习分析报告',
    icon: 'bar-chart-o'
  },
  {
    key: 'achievements',
    title: '我的成就',
    description: '查看获得的成就',
    icon: 'medal'
  }
])

// 设置选项
const settings = ref([
  {
    key: 'notifications',
    title: '消息通知',
    icon: 'bell-o'
  },
  {
    key: 'privacy',
    title: '隐私设置',
    icon: 'shield-o'
  },
  {
    key: 'help',
    title: '帮助与反馈',
    icon: 'question-o'
  },
  {
    key: 'about',
    title: '关于我们',
    icon: 'info-o'
  }
])

// 方法
const loadStudyData = async () => {
  try {
    // 这里应该调用API获取真实学习数据
    studyData.value = [
      {
        key: 'total_study_time',
        label: '总学习时长',
        value: '25小时',
        description: '本月已学习',
        icon: 'clock-o'
      },
      {
        key: 'completed_courses',
        label: '完成课程',
        value: '3门',
        description: '累计完成',
        icon: 'completed'
      },
      {
        key: 'practice_count',
        label: '练习次数',
        value: '18次',
        description: '本周练习',
        icon: 'edit'
      },
      {
        key: 'achievement_count',
        label: '获得成就',
        value: '5个',
        description: '累计获得',
        icon: 'medal-o'
      }
    ]
  } catch (error) {
    console.error('加载学习数据失败:', error)
  }
}

const showEditProfile = () => {
  // 初始化编辑表单
  editForm.realName = authStore.user?.realName || ''
  editForm.email = authStore.user?.email || ''
  editForm.phoneNumber = authStore.user?.phoneNumber || ''
  showEditDialog.value = true
}

const saveProfile = async () => {
  try {
    // 这里应该调用API保存用户信息
    // await updateUserProfileAPI(editForm)
    
    showToast('资料更新成功')
    showEditDialog.value = false
    
    // 重新获取用户信息
    await authStore.fetchUser()
  } catch (error) {
    showToast('资料更新失败')
  }
}

const handleMenuClick = (key) => {
  const routeMap = {
    wrong_questions: '/wrong-questions',
    notes: '/notes',
    grades: '/grades'
  }

  if (routeMap[key]) {
    router.push(routeMap[key])
  } else {
    showToast(`${key} 功能开发中`)
  }
}

const handleSettingClick = (key) => {
  showToast(`${key} 设置功能开发中`)
}

const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '提示',
      message: '确定要退出登录吗？'
    })
    
    authStore.logout()
    showToast('退出登录成功')
    router.push('/login')
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  loadStudyData()
})
</script>

<style scoped>
.profile {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px 20px;
  display: flex;
  align-items: center;
  color: white;
}

.avatar {
  margin-right: 16px;
}

.avatar-placeholder {
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.user-info {
  flex: 1;
}

.user-info .name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}

.user-info .details {
  font-size: 14px;
  opacity: 0.8;
}

.user-info .details span {
  display: block;
  margin-bottom: 4px;
}

.data-group,
.menu-group,
.settings-group {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.data-icon,
.menu-icon,
.setting-icon {
  margin-right: 12px;
  color: #1989fa;
}

.logout-section {
  padding: 20px;
}

.edit-profile {
  height: 100%;
  background-color: white;
}

.profile-form {
  padding: 16px;
}
</style>
