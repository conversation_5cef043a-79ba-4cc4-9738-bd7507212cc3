package com.lait.dto;

import lombok.Data;

/**
 * 飞书访问令牌DTO
 */
@Data
public class FeishuTokenDto {
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 令牌类型
     */
    private String tokenType;
    
    /**
     * 过期时间（秒）
     */
    private Integer expiresIn;
    
    /**
     * 令牌获取时间戳
     */
    private Long timestamp;
    
    /**
     * 检查令牌是否过期
     */
    public boolean isExpired() {
        if (timestamp == null || expiresIn == null) {
            return true;
        }
        long currentTime = System.currentTimeMillis() / 1000;
        return currentTime >= (timestamp + expiresIn - 300); // 提前5分钟过期
    }
}
