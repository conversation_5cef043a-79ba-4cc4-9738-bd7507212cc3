import request from '@/utils/request'

// 获取工作流列表
export function getWorkflows(params) {
  return request({
    url: '/admin/coze-workflows',
    method: 'get',
    params
  })
}

// 获取工作流详情
export function getWorkflow(id) {
  return request({
    url: `/admin/coze-workflows/${id}`,
    method: 'get'
  })
}

// 创建工作流
export function createWorkflow(data) {
  return request({
    url: '/admin/coze-workflows',
    method: 'post',
    data
  })
}

// 更新工作流
export function updateWorkflow(id, data) {
  return request({
    url: `/admin/coze-workflows/${id}`,
    method: 'put',
    data
  })
}

// 删除工作流
export function deleteWorkflow(id) {
  return request({
    url: `/admin/coze-workflows/${id}`,
    method: 'delete'
  })
}

// 执行工作流
export function executeWorkflow(id, inputData) {
  return request({
    url: `/admin/coze-workflows/${id}/execute`,
    method: 'post',
    data: inputData
  })
}

// 异步执行工作流
export function executeWorkflowAsync(id, inputData) {
  return request({
    url: `/admin/coze-workflows/${id}/execute-async`,
    method: 'post',
    data: inputData
  })
}

// 测试工作流
export function testWorkflow(id, inputData) {
  return request({
    url: `/admin/coze-workflows/${id}/test`,
    method: 'post',
    data: inputData
  })
}

// 复制工作流
export function duplicateWorkflow(id, newName) {
  return request({
    url: `/admin/coze-workflows/${id}/duplicate`,
    method: 'post',
    params: { newName }
  })
}

// 导入工作流
export function importWorkflow(config) {
  return request({
    url: '/admin/coze-workflows/import',
    method: 'post',
    data: config,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 导出工作流
export function exportWorkflow(id) {
  return request({
    url: `/admin/coze-workflows/${id}/export`,
    method: 'get'
  })
}

// 激活工作流
export function activateWorkflow(id) {
  return request({
    url: `/admin/coze-workflows/${id}/activate`,
    method: 'post'
  })
}

// 停用工作流
export function deactivateWorkflow(id) {
  return request({
    url: `/admin/coze-workflows/${id}/deactivate`,
    method: 'post'
  })
}

// 归档工作流
export function archiveWorkflow(id) {
  return request({
    url: `/admin/coze-workflows/${id}/archive`,
    method: 'post'
  })
}

// 获取工作流执行记录
export function getWorkflowExecutions(id, params) {
  return request({
    url: `/admin/coze-workflows/${id}/executions`,
    method: 'get',
    params
  })
}

// 获取执行记录详情
export function getExecution(executionId) {
  return request({
    url: `/admin/coze-workflows/executions/${executionId}`,
    method: 'get'
  })
}

// 取消执行
export function cancelExecution(executionId) {
  return request({
    url: `/admin/coze-workflows/executions/${executionId}/cancel`,
    method: 'post'
  })
}

// 重新执行
export function retryExecution(executionId) {
  return request({
    url: `/admin/coze-workflows/executions/${executionId}/retry`,
    method: 'post'
  })
}

// 获取工作流统计信息
export function getWorkflowStatistics() {
  return request({
    url: '/admin/coze-workflows/statistics',
    method: 'get'
  })
}

// 获取工作流执行统计
export function getExecutionStatistics(id) {
  return request({
    url: `/admin/coze-workflows/${id}/statistics`,
    method: 'get'
  })
}

// 获取热门工作流
export function getPopularWorkflows(limit = 10) {
  return request({
    url: '/admin/coze-workflows/popular',
    method: 'get',
    params: { limit }
  })
}

// 获取最近执行的工作流
export function getRecentlyExecutedWorkflows(limit = 10) {
  return request({
    url: '/admin/coze-workflows/recent',
    method: 'get',
    params: { limit }
  })
}

// 验证工作流配置
export function validateWorkflowConfig(config) {
  return request({
    url: '/admin/coze-workflows/validate-config',
    method: 'post',
    data: config,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取工作流模板
export function getWorkflowTemplates() {
  return request({
    url: '/admin/coze-workflows/templates',
    method: 'get'
  })
}

// 清理过期执行记录
export function cleanupOldExecutions(daysToKeep = 30) {
  return request({
    url: '/admin/coze-workflows/cleanup',
    method: 'post',
    params: { daysToKeep }
  })
}

// 工作流类型选项
export const workflowTypes = [
  { value: 'QUESTION_GENERATION', label: '题目生成' },
  { value: 'CONTENT_ANALYSIS', label: '内容分析' },
  { value: 'STUDY_GUIDANCE', label: '学习指导' },
  { value: 'ANSWER_EXPLANATION', label: '答案解释' },
  { value: 'PERFORMANCE_ANALYSIS', label: '表现分析' },
  { value: 'CUSTOM', label: '自定义' }
]

// 工作流状态选项
export const workflowStatuses = [
  { value: 'DRAFT', label: '草稿' },
  { value: 'ACTIVE', label: '激活' },
  { value: 'INACTIVE', label: '停用' },
  { value: 'TESTING', label: '测试中' },
  { value: 'ARCHIVED', label: '已归档' }
]

// 执行状态选项
export const executionStatuses = [
  { value: 'PENDING', label: '等待中' },
  { value: 'RUNNING', label: '执行中' },
  { value: 'SUCCESS', label: '成功' },
  { value: 'FAILED', label: '失败' },
  { value: 'CANCELLED', label: '已取消' },
  { value: 'TIMEOUT', label: '超时' }
]
