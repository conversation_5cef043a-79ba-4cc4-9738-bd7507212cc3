package com.lait.repository;

import com.lait.entity.CozeWorkflowExecution;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Coze工作流执行记录Repository
 */
@Repository
public interface CozeWorkflowExecutionRepository extends JpaRepository<CozeWorkflowExecution, Long> {

    /**
     * 根据工作流ID查找执行记录
     */
    List<CozeWorkflowExecution> findByWorkflowId(Long workflowId);

    /**
     * 根据工作流ID分页查找执行记录
     */
    Page<CozeWorkflowExecution> findByWorkflowId(Long workflowId, Pageable pageable);

    /**
     * 根据执行状态查找记录
     */
    List<CozeWorkflowExecution> findByStatus(CozeWorkflowExecution.ExecutionStatus status);

    /**
     * 根据执行者查找记录
     */
    List<CozeWorkflowExecution> findByExecutorId(Long executorId);

    /**
     * 查找指定时间范围内的执行记录
     */
    @Query("SELECT e FROM CozeWorkflowExecution e WHERE e.startTime BETWEEN :startTime AND :endTime")
    List<CozeWorkflowExecution> findByTimeRange(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询执行记录
     */
    @Query("SELECT e FROM CozeWorkflowExecution e WHERE " +
           "(:workflowId IS NULL OR e.workflowId = :workflowId) AND " +
           "(:status IS NULL OR e.status = :status) AND " +
           "(:executorId IS NULL OR e.executorId = :executorId) AND " +
           "(:startTime IS NULL OR e.startTime >= :startTime) AND " +
           "(:endTime IS NULL OR e.startTime <= :endTime)")
    Page<CozeWorkflowExecution> findExecutions(@Param("workflowId") Long workflowId,
                                              @Param("status") CozeWorkflowExecution.ExecutionStatus status,
                                              @Param("executorId") Long executorId,
                                              @Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              Pageable pageable);

    /**
     * 统计工作流执行次数
     */
    @Query("SELECT COUNT(e) FROM CozeWorkflowExecution e WHERE e.workflowId = :workflowId")
    long countByWorkflowId(@Param("workflowId") Long workflowId);

    /**
     * 统计工作流成功执行次数
     */
    @Query("SELECT COUNT(e) FROM CozeWorkflowExecution e WHERE e.workflowId = :workflowId AND e.status = 'SUCCESS'")
    long countSuccessByWorkflowId(@Param("workflowId") Long workflowId);

    /**
     * 统计工作流失败执行次数
     */
    @Query("SELECT COUNT(e) FROM CozeWorkflowExecution e WHERE e.workflowId = :workflowId AND e.status = 'FAILED'")
    long countFailureByWorkflowId(@Param("workflowId") Long workflowId);

    /**
     * 计算工作流平均执行时间
     */
    @Query("SELECT AVG(e.duration) FROM CozeWorkflowExecution e WHERE e.workflowId = :workflowId AND e.duration IS NOT NULL")
    Double avgDurationByWorkflowId(@Param("workflowId") Long workflowId);

    /**
     * 查找最近的执行记录
     */
    @Query("SELECT e FROM CozeWorkflowExecution e WHERE e.workflowId = :workflowId ORDER BY e.startTime DESC")
    List<CozeWorkflowExecution> findRecentExecutions(@Param("workflowId") Long workflowId, Pageable pageable);

    /**
     * 查找正在执行的记录
     */
    @Query("SELECT e FROM CozeWorkflowExecution e WHERE e.status IN ('PENDING', 'RUNNING')")
    List<CozeWorkflowExecution> findRunningExecutions();

    /**
     * 删除指定时间之前的执行记录
     */
    @Query("DELETE FROM CozeWorkflowExecution e WHERE e.startTime < :threshold")
    void deleteOldExecutions(@Param("threshold") LocalDateTime threshold);

    /**
     * 统计每日执行次数
     */
    @Query("SELECT DATE(e.startTime), COUNT(e) FROM CozeWorkflowExecution e " +
           "WHERE e.startTime BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE(e.startTime) ORDER BY DATE(e.startTime)")
    List<Object[]> countDailyExecutions(@Param("startDate") LocalDateTime startDate,
                                       @Param("endDate") LocalDateTime endDate);
}
