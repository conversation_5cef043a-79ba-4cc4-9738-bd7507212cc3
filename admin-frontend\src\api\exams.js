import request from './request'

// ========== 考试管理 ==========

// 获取考试列表
export function getExams(params) {
  return request({
    url: '/admin/exams',
    method: 'get',
    params
  })
}

// 获取考试详情
export function getExam(id) {
  return request({
    url: `/admin/exams/${id}`,
    method: 'get'
  })
}

// 创建考试
export function createExam(data) {
  return request({
    url: '/admin/exams',
    method: 'post',
    data
  })
}

// 更新考试
export function updateExam(id, data) {
  return request({
    url: `/admin/exams/${id}`,
    method: 'put',
    data
  })
}

// 删除考试
export function deleteExam(id) {
  return request({
    url: `/admin/exams/${id}`,
    method: 'delete'
  })
}

// 根据学科获取考试
export function getExamsBySubject(subjectId) {
  return request({
    url: `/admin/exams/subject/${subjectId}`,
    method: 'get'
  })
}

// 获取正在进行的考试
export function getOngoingExams() {
  return request({
    url: '/admin/exams/ongoing',
    method: 'get'
  })
}

// 获取即将开始的考试
export function getUpcomingExams(hours = 24) {
  return request({
    url: '/admin/exams/upcoming',
    method: 'get',
    params: { hours }
  })
}

// ========== 考试状态管理 ==========

// 发布考试
export function publishExam(id) {
  return request({
    url: `/admin/exams/${id}/publish`,
    method: 'put'
  })
}

// 开始考试
export function startExam(id) {
  return request({
    url: `/admin/exams/${id}/start`,
    method: 'put'
  })
}

// 结束考试
export function endExam(id) {
  return request({
    url: `/admin/exams/${id}/end`,
    method: 'put'
  })
}

// 取消考试
export function cancelExam(id) {
  return request({
    url: `/admin/exams/${id}/cancel`,
    method: 'put'
  })
}

// ========== 考试题目管理 ==========

// 添加题目到考试
export function addQuestionToExam(examId, questionId, points, order) {
  return request({
    url: `/admin/exams/${examId}/questions/${questionId}`,
    method: 'post',
    data: { points, order }
  })
}

// 从考试中移除题目
export function removeQuestionFromExam(examId, questionId) {
  return request({
    url: `/admin/exams/${examId}/questions/${questionId}`,
    method: 'delete'
  })
}

// 获取考试题目列表
export function getExamQuestions(examId) {
  return request({
    url: `/admin/exams/${examId}/questions`,
    method: 'get'
  })
}

// 批量添加题目到考试
export function addQuestionsToExam(examId, questionIds) {
  return request({
    url: `/admin/exams/${examId}/questions/batch`,
    method: 'post',
    data: { questionIds }
  })
}

// 更新考试题目顺序
export function updateQuestionOrder(examId, questionOrders) {
  return request({
    url: `/admin/exams/${examId}/questions/order`,
    method: 'put',
    data: questionOrders
  })
}

// ========== 考试参与管理 ==========

// 学生参加考试
export function joinExam(examId, studentId) {
  return request({
    url: `/admin/exams/${examId}/join`,
    method: 'post',
    params: { studentId }
  })
}

// 提交考试答案
export function submitExam(examId, studentId, answers) {
  return request({
    url: `/admin/exams/${examId}/submit`,
    method: 'post',
    params: { studentId },
    data: answers
  })
}

// 获取考试参与者列表
export function getExamParticipants(examId) {
  return request({
    url: `/admin/exams/${examId}/participants`,
    method: 'get'
  })
}

// ========== 考试统计 ==========

// 获取考试统计信息
export function getExamStatistics() {
  return request({
    url: '/admin/exams/statistics',
    method: 'get'
  })
}

// 获取考试详细统计
export function getExamDetailStatistics(examId) {
  return request({
    url: `/admin/exams/${examId}/statistics`,
    method: 'get'
  })
}

// 获取热门考试
export function getPopularExams(limit = 10) {
  return request({
    url: '/admin/exams/popular',
    method: 'get',
    params: { limit }
  })
}

// 获取最近创建的考试
export function getRecentExams(limit = 10) {
  return request({
    url: '/admin/exams/recent',
    method: 'get',
    params: { limit }
  })
}

// ========== 考试模板 ==========

// 创建考试模板
export function createExamTemplate(data) {
  return request({
    url: '/admin/exams/templates',
    method: 'post',
    data
  })
}

// 从模板创建考试
export function createExamFromTemplate(templateId, examInfo) {
  return request({
    url: `/admin/exams/templates/${templateId}/create`,
    method: 'post',
    data: examInfo
  })
}

// 获取考试模板列表
export function getExamTemplates() {
  return request({
    url: '/admin/exams/templates',
    method: 'get'
  })
}

// ========== 考试导入导出 ==========

// 导出考试配置
export function exportExamConfig(examId) {
  return request({
    url: `/admin/exams/${examId}/export`,
    method: 'post'
  })
}

// 导入考试配置
export function importExamConfig(config) {
  return request({
    url: '/admin/exams/import',
    method: 'post',
    data: { config }
  })
}

// 自动处理考试状态
export function autoProcessExamStatus() {
  return request({
    url: '/admin/exams/auto-process',
    method: 'post'
  })
}
