{"name": "lait-admin-frontend", "version": "1.0.0", "description": "智能学习系统管理界面", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@bytemd/plugin-gfm": "^1.22.0", "@bytemd/plugin-highlight": "^1.22.0", "@bytemd/vue-next": "^1.22.0", "@element-plus/icons-vue": "^2.1.0", "@vueup/vue-quill": "^1.2.0", "axios": "^1.4.0", "echarts": "^5.4.3", "element-plus": "^2.3.8", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "lait-admin-frontend": "file:", "marked": "^5.1.1", "monaco-editor": "^0.52.2", "pinia": "^2.1.6", "quill": "^2.0.3", "vue": "^3.3.4", "vue-echarts": "^6.6.1", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5"}}