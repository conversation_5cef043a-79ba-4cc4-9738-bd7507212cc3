<template>
  <div class="notes-page">
    <!-- 头部导航 -->
    <van-nav-bar title="我的笔记" left-arrow @click-left="$router.go(-1)">
      <template #right>
        <van-icon name="plus" @click="createNote" />
      </template>
    </van-nav-bar>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <van-row gutter="12">
        <van-col span="8">
          <div class="stat-card">
            <div class="stat-number">{{ stats.totalCount }}</div>
            <div class="stat-label">总笔记</div>
          </div>
        </van-col>
        <van-col span="8">
          <div class="stat-card">
            <div class="stat-number">{{ stats.sharedCount }}</div>
            <div class="stat-label">已分享</div>
          </div>
        </van-col>
        <van-col span="8">
          <div class="stat-card">
            <div class="stat-number">{{ stats.todayCount }}</div>
            <div class="stat-label">今日新增</div>
          </div>
        </van-col>
      </van-row>
    </div>

    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索笔记内容"
        @search="onSearch"
        @clear="onClearSearch"
      />
    </div>

    <!-- 筛选标签 -->
    <van-tabs v-model:active="activeTab" @change="onTabChange" sticky>
      <van-tab title="全部" name="all"></van-tab>
      <van-tab title="学习笔记" name="STUDY"></van-tab>
      <van-tab title="复习笔记" name="REVIEW"></van-tab>
      <van-tab title="总结笔记" name="SUMMARY"></van-tab>
      <van-tab title="错题笔记" name="ERROR"></van-tab>
    </van-tabs>

    <!-- 笔记列表 -->
    <div class="notes-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="note in notes"
            :key="note.id"
            class="note-item"
            @click="viewNote(note)"
          >
            <div class="note-header">
              <div class="note-title">{{ note.title }}</div>
              <div class="note-actions">
                <van-icon
                  name="share-o"
                  v-if="note.isShared"
                  color="#07c160"
                  size="16"
                />
                <van-icon
                  name="more-o"
                  @click.stop="showNoteActions(note)"
                  size="16"
                />
              </div>
            </div>

            <div class="note-content">
              <p class="note-preview">{{ note.content }}</p>
            </div>

            <div class="note-footer">
              <div class="note-meta">
                <van-tag
                  :type="getNoteTypeColor(note.noteType)"
                  size="small"
                >
                  {{ getNoteTypeText(note.noteType) }}
                </van-tag>
                <span class="note-subject" v-if="note.subjectName">
                  {{ note.subjectName }}
                </span>
                <span class="note-time">{{ formatTime(note.createdTime) }}</span>
              </div>
              <div class="note-stats">
                <span class="view-count">
                  <van-icon name="eye-o" size="12" />
                  {{ note.viewCount || 0 }}
                </span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && notes.length === 0"
      description="暂无笔记"
      image="search"
    >
      <van-button type="primary" @click="createNote">
        创建笔记
      </van-button>
    </van-empty>

    <!-- 浮动创建按钮 -->
    <div class="fab-button" @click="createNote">
      <van-icon name="plus" size="24" color="white" />
    </div>

    <!-- 笔记详情弹窗 -->
    <van-popup v-model:show="showDetailPopup" position="bottom" style="height: 85%">
      <div class="detail-popup" v-if="currentNote">
        <div class="detail-header">
          <van-nav-bar :title="currentNote.title" @click-left="showDetailPopup = false">
            <template #left>
              <van-icon name="cross" />
            </template>
            <template #right>
              <van-icon name="edit" @click="editNote(currentNote)" />
            </template>
          </van-nav-bar>
        </div>

        <div class="detail-content">
          <div class="detail-meta">
            <van-tag :type="getNoteTypeColor(currentNote.noteType)">
              {{ getNoteTypeText(currentNote.noteType) }}
            </van-tag>
            <span class="detail-subject" v-if="currentNote.subjectName">
              {{ currentNote.subjectName }}
            </span>
            <span class="detail-time">{{ formatTime(currentNote.createdTime) }}</span>
          </div>

          <div class="detail-text">
            {{ currentNote.content }}
          </div>

          <div class="detail-tags" v-if="currentNote.tags">
            <van-tag
              v-for="tag in currentNote.tags.split(',')"
              :key="tag"
              size="small"
              type="primary"
              plain
            >
              {{ tag.trim() }}
            </van-tag>
          </div>

          <div class="detail-actions">
            <van-button
              type="success"
              size="large"
              block
              @click="toggleShare(currentNote)"
            >
              {{ currentNote.isShared ? '取消分享' : '分享笔记' }}
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 笔记操作弹窗 -->
    <van-action-sheet
      v-model:show="showActionSheet"
      :actions="noteActions"
      @select="onActionSelect"
      cancel-text="取消"
    />

    <!-- 创建/编辑笔记弹窗 -->
    <van-popup v-model:show="showEditPopup" position="bottom" style="height: 90%">
      <div class="edit-popup">
        <div class="edit-header">
          <van-nav-bar :title="isEditing ? '编辑笔记' : '创建笔记'">
            <template #left>
              <van-button size="small" @click="cancelEdit">取消</van-button>
            </template>
            <template #right>
              <van-button size="small" type="primary" @click="saveNote">保存</van-button>
            </template>
          </van-nav-bar>
        </div>

        <div class="edit-content">
          <van-form>
            <van-field
              v-model="editForm.title"
              label="标题"
              placeholder="请输入笔记标题"
              required
            />

            <van-field label="学科">
              <template #input>
                <van-picker
                  v-model="editForm.subjectId"
                  :columns="subjectColumns"
                  @confirm="onSubjectConfirm"
                />
              </template>
            </van-field>

            <van-field label="类型">
              <template #input>
                <van-radio-group v-model="editForm.noteType" direction="horizontal">
                  <van-radio name="STUDY">学习</van-radio>
                  <van-radio name="REVIEW">复习</van-radio>
                  <van-radio name="SUMMARY">总结</van-radio>
                  <van-radio name="ERROR">错题</van-radio>
                </van-radio-group>
              </template>
            </van-field>

            <van-field
              v-model="editForm.content"
              type="textarea"
              label="内容"
              placeholder="请输入笔记内容"
              rows="10"
              autosize
              required
            />

            <van-field
              v-model="editForm.tags"
              label="标签"
              placeholder="请输入标签，用逗号分隔"
            />

            <van-field label="是否分享">
              <template #input>
                <van-switch v-model="editForm.isShared" />
              </template>
            </van-field>
          </van-form>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { getMyNotes, createNote as createNoteApi, updateNote as updateNoteApi, deleteNote as deleteNoteApi, shareNote, unshareNote, getMyNoteStats } from '@/api/notes'
import { getSubjects } from '@/api/subjects'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const showDetailPopup = ref(false)
const showActionSheet = ref(false)
const showEditPopup = ref(false)
const isEditing = ref(false)

const notes = ref([])
const subjects = ref([])
const currentNote = ref(null)
const activeTab = ref('all')
const searchKeyword = ref('')

// 统计数据
const stats = reactive({
  totalCount: 0,
  sharedCount: 0,
  todayCount: 0
})

// 分页参数
const pagination = reactive({
  page: 0,
  size: 20,
  hasMore: true
})

// 编辑表单
const editForm = reactive({
  id: null,
  title: '',
  content: '',
  noteType: 'STUDY',
  subjectId: null,
  tags: '',
  isShared: false
})

// 计算属性
const subjectColumns = computed(() => {
  return [
    { text: '请选择学科', value: null },
    ...subjects.value.map(subject => ({
      text: subject.name,
      value: subject.id
    }))
  ]
})

const noteActions = computed(() => [
  { name: '编辑', value: 'edit' },
  { name: currentNote.value?.isShared ? '取消分享' : '分享', value: 'share' },
  { name: '删除', value: 'delete', color: '#ee0a24' }
])

// 方法
const loadNotes = async (isRefresh = false) => {
  if (loading.value) return

  loading.value = true

  try {
    if (isRefresh) {
      pagination.page = 0
      pagination.hasMore = true
      finished.value = false
    }

    const params = {
      page: pagination.page,
      size: pagination.size,
      noteType: activeTab.value === 'all' ? null : activeTab.value,
      keyword: searchKeyword.value || null
    }

    const response = await getMyNotes(params)
    const newData = response.data.content || response.data

    if (isRefresh) {
      notes.value = newData
    } else {
      notes.value.push(...newData)
    }

    pagination.page++

    if (newData.length < pagination.size) {
      finished.value = true
      pagination.hasMore = false
    }

  } catch (error) {
    showToast('加载笔记失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await getMyNoteStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadSubjects = async () => {
  try {
    const response = await getSubjects()
    subjects.value = response.data
  } catch (error) {
    console.error('加载学科列表失败:', error)
  }
}

const onLoad = () => {
  if (!pagination.hasMore) {
    finished.value = true
    return
  }
  loadNotes()
}

const onRefresh = () => {
  loadNotes(true)
}

const onTabChange = () => {
  notes.value = []
  pagination.page = 0
  pagination.hasMore = true
  finished.value = false
  loadNotes(true)
}

const onSearch = () => {
  notes.value = []
  pagination.page = 0
  pagination.hasMore = true
  finished.value = false
  loadNotes(true)
}

const onClearSearch = () => {
  searchKeyword.value = ''
  onSearch()
}

const viewNote = (note) => {
  currentNote.value = note
  showDetailPopup.value = true
}

const showNoteActions = (note) => {
  currentNote.value = note
  showActionSheet.value = true
}

const onActionSelect = (action) => {
  showActionSheet.value = false

  switch (action.value) {
    case 'edit':
      editNote(currentNote.value)
      break
    case 'share':
      toggleShare(currentNote.value)
      break
    case 'delete':
      deleteNote(currentNote.value)
      break
  }
}

const createNote = () => {
  isEditing.value = false
  resetEditForm()

  // 如果从错题页面跳转过来，预设错题笔记类型
  if (route.query.questionId) {
    editForm.noteType = 'ERROR'
  }

  showEditPopup.value = true
}

const editNote = (note) => {
  isEditing.value = true
  Object.assign(editForm, {
    id: note.id,
    title: note.title,
    content: note.content,
    noteType: note.noteType,
    subjectId: note.subjectId,
    tags: note.tags || '',
    isShared: note.isShared
  })
  showDetailPopup.value = false
  showEditPopup.value = true
}

const saveNote = async () => {
  if (!editForm.title.trim()) {
    showToast('请输入笔记标题')
    return
  }

  if (!editForm.content.trim()) {
    showToast('请输入笔记内容')
    return
  }

  try {
    const data = { ...editForm }

    if (isEditing.value) {
      await updateNoteApi(editForm.id, data)
      showToast('更新成功')

      // 更新本地数据
      const index = notes.value.findIndex(item => item.id === editForm.id)
      if (index > -1) {
        Object.assign(notes.value[index], data)
      }
    } else {
      const response = await createNoteApi(data)
      showToast('创建成功')

      // 添加到列表顶部
      notes.value.unshift(response.data)
      stats.totalCount++
      stats.todayCount++
    }

    showEditPopup.value = false
  } catch (error) {
    showToast(isEditing.value ? '更新失败' : '创建失败')
  }
}

const cancelEdit = () => {
  showEditPopup.value = false
  resetEditForm()
}

const resetEditForm = () => {
  Object.assign(editForm, {
    id: null,
    title: '',
    content: '',
    noteType: 'STUDY',
    subjectId: null,
    tags: '',
    isShared: false
  })
}

const toggleShare = async (note) => {
  try {
    if (note.isShared) {
      await unshareNote(note.id)
      showToast('取消分享成功')
      note.isShared = false
      stats.sharedCount--
    } else {
      await shareNote(note.id)
      showToast('分享成功')
      note.isShared = true
      stats.sharedCount++
    }

    showDetailPopup.value = false
  } catch (error) {
    showToast('操作失败')
  }
}

const deleteNote = async (note) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这篇笔记吗？'
    })

    await deleteNoteApi(note.id)
    showToast('删除成功')

    // 从列表中移除
    const index = notes.value.findIndex(item => item.id === note.id)
    if (index > -1) {
      notes.value.splice(index, 1)
    }

    // 更新统计
    stats.totalCount--
    if (note.isShared) {
      stats.sharedCount--
    }

    showDetailPopup.value = false
  } catch (error) {
    if (error !== 'cancel') {
      showToast('删除失败')
    }
  }
}

const onSubjectConfirm = (value) => {
  editForm.subjectId = value.selectedValues[0]
}

// 辅助方法
const getNoteTypeText = (type) => {
  const map = {
    'STUDY': '学习笔记',
    'REVIEW': '复习笔记',
    'SUMMARY': '总结笔记',
    'ERROR': '错题笔记'
  }
  return map[type] || type
}

const getNoteTypeColor = (type) => {
  const map = {
    'STUDY': 'primary',
    'REVIEW': 'success',
    'SUMMARY': 'warning',
    'ERROR': 'danger'
  }
  return map[type] || 'default'
}

const formatTime = (timeString) => {
  const date = new Date(timeString)
  const now = new Date()
  const diff = now - date

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

// 生命周期
onMounted(() => {
  loadStats()
  loadSubjects()
  loadNotes(true)
})
</script>

<style scoped>
.notes-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px; /* 为浮动按钮留出空间 */
}

/* 统计卡片 */
.stats-section {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.stat-card {
  text-align: center;
  padding: 16px 8px;
  background: #f7f8fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

/* 搜索栏 */
.search-section {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

/* 笔记列表 */
.notes-list {
  padding: 0 16px;
}

.note-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.note-item:active {
  transform: scale(0.98);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.note-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  flex: 1;
  margin-right: 12px;
  line-height: 1.4;
}

.note-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
}

.note-content {
  margin-bottom: 12px;
}

.note-preview {
  font-size: 14px;
  line-height: 1.6;
  color: #646566;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.note-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #969799;
  flex: 1;
}

.note-subject {
  color: #646566;
}

.note-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #969799;
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 浮动创建按钮 */
.fab-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 56px;
  height: 56px;
  background: #1989fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.4);
  cursor: pointer;
  z-index: 100;
  transition: transform 0.2s ease;
}

.fab-button:active {
  transform: scale(0.95);
}

/* 详情弹窗 */
.detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 12px;
  color: #969799;
  flex-wrap: wrap;
}

.detail-subject {
  color: #646566;
}

.detail-text {
  font-size: 16px;
  line-height: 1.8;
  color: #323233;
  margin-bottom: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.detail-tags {
  margin-bottom: 20px;
}

.detail-tags .van-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.detail-actions {
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
}

/* 编辑弹窗 */
.edit-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.edit-content {
  flex: 1;
  overflow-y: auto;
}

.edit-content .van-form {
  padding: 16px;
}

.edit-content .van-field {
  margin-bottom: 16px;
}

.edit-content .van-field--textarea {
  margin-bottom: 20px;
}

/* 平板适配 */
@media (min-width: 768px) {
  .notes-page {
    max-width: 768px;
    margin: 0 auto;
  }

  .stats-section {
    padding: 24px;
  }

  .stat-card {
    padding: 24px 16px;
  }

  .stat-number {
    font-size: 28px;
  }

  .stat-label {
    font-size: 14px;
  }

  .search-section {
    padding: 24px;
  }

  .notes-list {
    padding: 0 24px;
  }

  .note-item {
    padding: 20px;
    margin-bottom: 16px;
  }

  .note-title {
    font-size: 18px;
  }

  .note-preview {
    font-size: 16px;
    -webkit-line-clamp: 4;
  }

  .note-meta {
    font-size: 14px;
  }

  .note-stats {
    font-size: 14px;
  }

  .fab-button {
    width: 64px;
    height: 64px;
    bottom: 100px;
    right: 30px;
  }

  .detail-content {
    padding: 24px;
  }

  .detail-meta {
    font-size: 14px;
  }

  .detail-text {
    font-size: 18px;
    line-height: 2;
  }

  .edit-content .van-form {
    padding: 24px;
  }
}

/* 大屏平板适配 */
@media (min-width: 1024px) {
  .notes-page {
    max-width: 1024px;
  }

  .stats-section .van-row {
    max-width: 600px;
    margin: 0 auto;
  }

  .notes-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 16px;
    padding: 0 24px;
  }

  .note-item {
    margin-bottom: 0;
    height: fit-content;
  }

  .fab-button {
    bottom: 120px;
    right: 40px;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 600px) {
  .detail-popup,
  .edit-popup {
    height: 95vh;
  }

  .stats-section {
    padding: 12px 16px;
  }

  .stat-card {
    padding: 12px 8px;
  }

  .stat-number {
    font-size: 20px;
  }

  .search-section {
    padding: 12px 16px;
  }

  .fab-button {
    bottom: 60px;
    width: 48px;
    height: 48px;
  }
}

/* 触摸优化 */
.van-button {
  min-height: 44px;
}

.note-item {
  min-height: 44px;
}

.van-cell {
  min-height: 54px;
}

.fab-button {
  min-width: 44px;
  min-height: 44px;
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .note-item,
  .fab-button {
    transition: none;
  }

  .note-item:active,
  .fab-button:active {
    transform: none;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .notes-page {
    background-color: #1a1a1a;
  }

  .stats-section,
  .search-section,
  .note-item {
    background: #2a2a2a;
    color: #ffffff;
  }

  .stat-card {
    background: #3a3a3a;
  }

  .note-title {
    color: #ffffff;
  }

  .note-preview {
    color: #cccccc;
  }

  .note-meta,
  .note-stats {
    color: #999999;
  }

  .detail-text {
    color: #ffffff;
  }
}
</style>
