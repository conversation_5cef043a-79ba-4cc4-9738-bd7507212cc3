package com.lait.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 题目实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "questions")
public class Question extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "学科ID不能为空")
    @Column(name = "subject_id", nullable = false)
    private Long subjectId;

    @NotBlank(message = "题目内容不能为空")
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;

    @Enumerated(EnumType.STRING)
    @Column(name = "question_type", nullable = false)
    private QuestionType questionType;

    @Column(columnDefinition = "TEXT")
    private String options; // JSON格式存储选项

    @NotBlank(message = "正确答案不能为空")
    @Column(name = "correct_answer", nullable = false)
    private String correctAnswer;

    @Column(columnDefinition = "TEXT")
    private String explanation; // 答案解析

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DifficultyLevel difficulty;

    @Column(name = "grade_level")
    private Integer gradeLevel;

    @Size(max = 200, message = "标签长度不能超过200个字符")
    private String tags; // 标签，逗号分隔

    @Column(name = "usage_count")
    private Integer usageCount = 0; // 使用次数

    @Column(name = "correct_count")
    private Integer correctCount = 0; // 正确次数

    @Column(name = "error_count")
    private Integer errorCount = 0; // 错误次数

    @Column(name = "points")
    private Integer points = 1; // 题目分值

    @Column(name = "time_limit")
    private Integer timeLimit; // 答题时间限制（秒）

    @Column(name = "image_url")
    private String imageUrl; // 题目图片URL

    @Column(name = "audio_url")
    private String audioUrl; // 题目音频URL

    @Column(name = "video_url")
    private String videoUrl; // 题目视频URL

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private QuestionStatus status = QuestionStatus.ACTIVE; // 题目状态

    @Column(name = "creator_id")
    private Long creatorId; // 创建者ID

    @Column(name = "chapter")
    private String chapter; // 章节

    @Column(name = "knowledge_points", columnDefinition = "TEXT")
    private String knowledgePoints; // 知识点（JSON格式）

    @Column(name = "source")
    private String source; // 题目来源

    /**
     * 题目类型枚举
     */
    public enum QuestionType {
        SINGLE_CHOICE("单选题"),
        MULTIPLE_CHOICE("多选题"),
        TRUE_FALSE("判断题"),
        FILL_BLANK("填空题"),
        SHORT_ANSWER("简答题");

        private final String description;

        QuestionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 难度等级枚举
     */
    public enum DifficultyLevel {
        EASY("简单"),
        MEDIUM("中等"),
        HARD("困难");

        private final String description;

        DifficultyLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 题目状态枚举
     */
    public enum QuestionStatus {
        ACTIVE("启用"),
        INACTIVE("禁用"),
        DRAFT("草稿"),
        REVIEWING("审核中"),
        PUBLISHED("已发布");

        private final String description;

        QuestionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
