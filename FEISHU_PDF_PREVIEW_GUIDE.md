# 飞书文档PDF分页预览和导出功能指南

## 功能概述

为飞书文档编辑页面新增了PDF分页预览和导出功能，提供更专业的文档预览和导出体验。

## 新增功能详情

### 1. PDF分页预览功能 📄

#### 功能特性
- **智能分页**: 自动按A4页面尺寸进行内容分页
- **实时预览**: 切换到PDF预览模式时实时生成分页效果
- **页面导航**: 支持上一页/下一页浏览
- **页码显示**: 显示当前页码和总页数
- **A4标准**: 按照标准A4纸张尺寸(595×842pt)进行分页
- **内容适配**: 自动处理长内容的分页显示

#### 预览模式
在预览面板中提供两种预览模式：

1. **普通预览**: 传统的连续滚动预览模式
2. **PDF分页预览**: 按PDF页面进行分页显示

#### 使用方法
1. 进入文档编辑页面
2. 切换到"预览"模式
3. 在预览工具栏选择"PDF分页预览"
4. 使用上一页/下一页按钮浏览各页内容
5. 查看页码信息了解文档总页数

### 2. PDF导出功能 📥

#### 功能特性
- **高质量导出**: 使用html2canvas + jsPDF生成高质量PDF
- **完整信息**: 包含文档标题、类型、创建者、导出时间等元信息
- **自动分页**: 智能处理长内容的分页
- **页码标注**: 每页底部自动添加页码
- **中文支持**: 完美支持中文字体渲染
- **格式保持**: 保持Markdown/HTML的原始格式和样式

#### 导出位置
PDF导出功能在两个位置提供：

1. **页面顶部**: 编辑页面右上角的"导出PDF"按钮
2. **预览工具栏**: 预览模式下工具栏的"导出PDF"按钮

#### 使用方法
1. 编辑完成文档内容
2. 点击"导出PDF"按钮
3. 等待PDF生成（显示加载状态）
4. PDF自动下载到本地

## 技术实现

### 核心技术栈
```json
{
  "jspdf": "^2.5.1",
  "html2canvas": "^1.4.1"
}
```

### 分页算法
- **内容测量**: 创建临时DOM容器测量内容高度
- **智能分页**: 根据A4页面高度自动计算分页点
- **元素完整性**: 尽量保持HTML元素的完整性
- **响应式适配**: 支持不同屏幕尺寸的预览

### PDF生成流程
1. **内容准备**: 添加文档头部信息和格式化内容
2. **Canvas渲染**: 使用html2canvas将HTML转换为图片
3. **PDF分页**: 按A4尺寸将图片分割到多个PDF页面
4. **页码添加**: 为每页添加页码信息
5. **文件保存**: 生成并下载PDF文件

## 界面设计

### 预览工具栏
```
[普通预览] [PDF分页预览]  |  [上一页] [1/3] [下一页]  |  [导出PDF]
```

- **左侧**: 预览模式切换
- **中间**: 分页导航控制（仅PDF预览模式显示）
- **右侧**: 导出功能按钮

### PDF页面样式
- **页面尺寸**: 595×842像素（A4标准）
- **内边距**: 40像素
- **背景色**: 白色
- **阴影效果**: 模拟真实纸张效果
- **页码位置**: 右下角

## 使用场景

### 适用场景
- **文档预览**: 查看文档的最终打印效果
- **格式检查**: 确认分页位置和内容布局
- **正式导出**: 生成用于分享或存档的PDF文件
- **打印准备**: 预览打印效果和页数

### 最佳实践
1. **内容编辑**: 先完成内容编辑，再进行PDF预览
2. **格式调整**: 根据PDF预览效果调整内容格式
3. **分页优化**: 注意避免重要内容被分页截断
4. **最终检查**: 导出前使用PDF预览模式最终检查

## 性能优化

### 分页性能
- **懒加载**: 仅在切换到PDF预览模式时生成分页
- **缓存机制**: 内容未变化时复用已生成的分页
- **DOM优化**: 使用临时DOM容器进行测量，避免影响主界面

### 导出性能
- **高质量渲染**: scale=2提供高分辨率输出
- **内存管理**: 及时清理临时DOM元素
- **错误处理**: 完善的错误处理和用户反馈

## 样式特性

### PDF页面样式
```css
.pdf-page {
  width: 595px;
  min-height: 842px;
  background: white;
  border: 1px solid #ddd;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### 响应式设计
- **桌面端**: 完整的A4页面尺寸显示
- **平板端**: 适当缩放保持比例
- **移动端**: 自适应宽度，保持可读性

## 快捷操作

### 键盘快捷键
- **Ctrl + P**: 快速导出PDF（浏览器默认打印，可自定义）
- **左右箭头**: 在PDF预览模式下切换页面
- **Esc**: 退出PDF预览模式

### 鼠标操作
- **滚轮**: 在普通预览模式下滚动内容
- **点击**: 使用分页按钮导航

## 故障排除

### 常见问题

**PDF预览空白**
- 检查文档内容是否为空
- 确认预览内容已正确渲染
- 尝试切换回普通预览模式

**导出失败**
- 检查浏览器是否支持html2canvas
- 确认网络连接正常
- 查看控制台错误信息

**分页异常**
- 检查内容是否包含特殊元素
- 尝试简化复杂的HTML结构
- 刷新页面重新生成分页

### 浏览器兼容性
- **Chrome 80+**: 完全支持
- **Firefox 75+**: 完全支持
- **Safari 13+**: 基本支持
- **Edge 80+**: 完全支持

## 后续优化计划

### 功能增强
- [ ] 支持自定义页面尺寸（A3、A5等）
- [ ] 添加页眉页脚自定义
- [ ] 支持水印功能
- [ ] 添加PDF书签导航
- [ ] 支持批量导出

### 性能优化
- [ ] 虚拟滚动优化大文档预览
- [ ] Web Worker后台处理PDF生成
- [ ] 增量分页更新
- [ ] 预加载下一页内容

### 用户体验
- [ ] 添加缩放功能
- [ ] 支持全屏预览
- [ ] 添加打印预览模式
- [ ] 支持PDF注释功能

## 总结

新增的PDF分页预览和导出功能为飞书文档编辑器带来了专业级的文档处理能力：

✅ **专业预览**: 提供真实的PDF分页预览效果
✅ **高质量导出**: 生成高质量的PDF文件
✅ **智能分页**: 自动处理内容分页和布局
✅ **用户友好**: 直观的界面和操作方式
✅ **性能优化**: 高效的渲染和内存管理
✅ **响应式设计**: 支持各种设备和屏幕尺寸

这些功能让用户能够：
- 📄 实时预览文档的PDF效果
- 🎯 精确控制文档的分页和布局
- 📥 导出高质量的PDF文件
- 🔍 详细检查文档的最终呈现效果

现在飞书文档编辑器具备了完整的"编辑-预览-导出"工作流程，为用户提供了专业的文档处理解决方案！
