package com.lait.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Coze工作流执行记录实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "coze_workflow_executions")
public class CozeWorkflowExecution extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 工作流ID
     */
    @Column(name = "workflow_id", nullable = false)
    private Long workflowId;

    /**
     * 执行ID (Coze平台的执行ID)
     */
    @Column(length = 100)
    private String executionId;

    /**
     * 执行状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ExecutionStatus status;

    /**
     * 输入参数 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String inputData;

    /**
     * 输出结果 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String outputData;

    /**
     * 错误信息
     */
    @Column(columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 执行开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime startTime;

    /**
     * 执行结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime endTime;

    /**
     * 执行时长 (毫秒)
     */
    private Long duration;

    /**
     * 执行者ID
     */
    @Column(name = "executor_id")
    private Long executorId;

    /**
     * 执行者名称
     */
    @Column(length = 50)
    private String executorName;

    /**
     * 执行类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ExecutionType executionType;

    /**
     * 触发方式
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private TriggerType triggerType;

    /**
     * 备注
     */
    @Column(length = 500)
    private String remarks;

    /**
     * 执行日志 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String executionLog;

    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        PENDING("等待中"),
        RUNNING("执行中"),
        SUCCESS("成功"),
        FAILED("失败"),
        CANCELLED("已取消"),
        TIMEOUT("超时");

        private final String description;

        ExecutionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 执行类型枚举
     */
    public enum ExecutionType {
        MANUAL("手动执行"),
        SCHEDULED("定时执行"),
        API("API调用"),
        WEBHOOK("Webhook触发");

        private final String description;

        ExecutionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 触发方式枚举
     */
    public enum TriggerType {
        USER("用户触发"),
        SYSTEM("系统触发"),
        EVENT("事件触发"),
        SCHEDULE("定时触发");

        private final String description;

        TriggerType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
