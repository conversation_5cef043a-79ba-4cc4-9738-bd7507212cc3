import request from './request'

// ========== 积分配置管理 ==========

// 获取积分配置列表
export function getPointsConfigs(params) {
  return request({
    url: '/admin/points/config',
    method: 'get',
    params
  })
}

// 获取积分配置详情
export function getPointsConfig(id) {
  return request({
    url: `/admin/points/config/${id}`,
    method: 'get'
  })
}

// 创建积分配置
export function createPointsConfig(data) {
  return request({
    url: '/admin/points/config',
    method: 'post',
    data
  })
}

// 更新积分配置
export function updatePointsConfig(id, data) {
  return request({
    url: `/admin/points/config/${id}`,
    method: 'put',
    data
  })
}

// 删除积分配置
export function deletePointsConfig(id) {
  return request({
    url: `/admin/points/config/${id}`,
    method: 'delete'
  })
}

// 获取启用的积分配置
export function getEnabledPointsConfigs() {
  return request({
    url: '/admin/points/config/enabled',
    method: 'get'
  })
}

// 根据分类获取积分配置
export function getPointsConfigsByCategory(category) {
  return request({
    url: `/admin/points/config/category/${category}`,
    method: 'get'
  })
}

// 批量更新积分配置状态
export function batchUpdatePointsConfigStatus(ids, enabled) {
  return request({
    url: '/admin/points/config/batch-update-status',
    method: 'put',
    data: { ids, enabled }
  })
}

// 初始化默认积分配置
export function initializeDefaultPointsConfig() {
  return request({
    url: '/admin/points/config/initialize',
    method: 'post'
  })
}

// ========== 用户积分管理 ==========

// 给用户添加积分
export function addUserPoints(userId, data) {
  return request({
    url: `/admin/points/users/${userId}/add`,
    method: 'post',
    data
  })
}

// 扣除用户积分
export function deductUserPoints(userId, data) {
  return request({
    url: `/admin/points/users/${userId}/deduct`,
    method: 'post',
    data
  })
}

// 获取用户当前积分
export function getUserCurrentPoints(userId) {
  return request({
    url: `/admin/points/users/${userId}/current`,
    method: 'get'
  })
}

// 获取用户积分记录
export function getUserPointsHistory(userId, params) {
  return request({
    url: `/admin/points/users/${userId}/history`,
    method: 'get',
    params
  })
}

// 分页查询积分记录
export function getPointsRecords(params) {
  return request({
    url: '/admin/points/records',
    method: 'get',
    params
  })
}

// 获取用户积分趋势
export function getUserPointsTrend(userId, days = 30) {
  return request({
    url: `/admin/points/users/${userId}/trend`,
    method: 'get',
    params: { days }
  })
}

// 获取积分排行榜
export function getPointsRanking(limit = 10) {
  return request({
    url: '/admin/points/ranking',
    method: 'get',
    params: { limit }
  })
}

// 重置用户积分
export function resetUserPoints(userId, reason) {
  return request({
    url: `/admin/points/users/${userId}/reset`,
    method: 'post',
    data: { reason }
  })
}

// 批量调整用户积分
export function batchAdjustUserPoints(userIds, points, reason) {
  return request({
    url: '/admin/points/users/batch-adjust',
    method: 'post',
    data: { userIds, points, reason }
  })
}

// ========== 统计分析 ==========

// 获取积分配置统计
export function getPointsConfigStatistics() {
  return request({
    url: '/admin/points/statistics/config',
    method: 'get'
  })
}

// 获取积分记录统计
export function getPointsRecordStatistics() {
  return request({
    url: '/admin/points/statistics/records',
    method: 'get'
  })
}

// 获取用户积分统计
export function getUserPointsStatistics(userId) {
  return request({
    url: `/admin/points/statistics/users/${userId}`,
    method: 'get'
  })
}
