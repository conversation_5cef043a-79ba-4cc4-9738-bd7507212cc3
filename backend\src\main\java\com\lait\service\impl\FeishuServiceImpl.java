package com.lait.service.impl;

import com.lait.dto.FeishuFileDto;
import com.lait.entity.FeishuDocument;
import com.lait.repository.FeishuDocumentRepository;
import com.lait.service.FeishuApiService;
import com.lait.service.FeishuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 飞书服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeishuServiceImpl implements FeishuService {

    private final FeishuDocumentRepository feishuDocumentRepository;
    private final FeishuApiService feishuApiService;


    @Override
    @Transactional
    public FeishuDocument createDocument(FeishuDocument document) {
        // 确保必需字段有默认值
        if (document.getAccessLevel() == null) {
            document.setAccessLevel(FeishuDocument.AccessLevel.INTERNAL);
        }
        if (document.getSyncStatus() == null) {
            document.setSyncStatus(FeishuDocument.SyncStatus.PENDING);
        }
        if (document.getViewCount() == null) {
            document.setViewCount(0L);
        }
        if (document.getVersion() == null) {
            document.setVersion(1);
        }
        if (document.getIsPublic() == null) {
            document.setIsPublic(false);
        }
        return feishuDocumentRepository.save(document);
    }

    @Override
    @Transactional
    public FeishuDocument updateDocument(Long id, FeishuDocument document) {
        FeishuDocument existingDocument = getDocumentById(id);

        // 保留现有的重要字段，避免被前端数据覆盖
        document.setId(id);
        document.setViewCount(existingDocument.getViewCount());
        document.setSyncStatus(existingDocument.getSyncStatus());
        document.setLastSyncAt(existingDocument.getLastSyncAt());
        document.setContent(existingDocument.getContent());
        document.setDocToken(document.getDocUrl().split("/").length > 0 ? document.getDocUrl().split("/")[document.getDocUrl().split("/").length - 1] : null);

        // 确保status字段不为空
        if (document.getStatus() == null) {
            document.setStatus(existingDocument.getStatus());
        }

        // 确保其他必需字段不为空
        if (document.getAccessLevel() == null) {
            document.setAccessLevel(existingDocument.getAccessLevel());
        }
        if (document.getIsPublic() == null) {
            document.setIsPublic(existingDocument.getIsPublic());
        }
        if (document.getVersion() == null) {
            document.setVersion(existingDocument.getVersion());
        }

        // 保留创建时间和创建者信息
        document.setCreatedTime(existingDocument.getCreatedTime());
        document.setCreatorId(existingDocument.getCreatorId());

        return feishuDocumentRepository.save(document);
    }

    @Override
    @Transactional
    public void deleteDocument(Long id) {
        FeishuDocument document = getDocumentById(id);
        document.setStatus(FeishuDocument.DocumentStatus.DELETED);
        feishuDocumentRepository.save(document);
    }

    @Override
    public FeishuDocument getDocumentById(Long id) {
        return feishuDocumentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("文档不存在: " + id));
    }

    @Override
    public FeishuDocument getDocumentByDocId(String docId) {
        return feishuDocumentRepository.findByDocId(docId)
                .orElseThrow(() -> new RuntimeException("文档不存在: " + docId));
    }

    @Override
    public Page<FeishuDocument> getDocuments(String title, FeishuDocument.DocumentType docType,
                                           FeishuDocument.DocumentStatus status, Long creatorId,
                                           Long subjectId, String category, Pageable pageable) {
        return feishuDocumentRepository.findDocumentsWithFilters(
                title, docType, status, creatorId, subjectId, category, pageable);
    }

    @Override
    public Page<FeishuDocument> searchDocuments(String keyword, Pageable pageable) {
        return feishuDocumentRepository.searchDocuments(keyword, pageable);
    }

    @Override
    public List<FeishuDocument> getPopularDocuments(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return feishuDocumentRepository.findPopularDocuments(pageable);
    }

    @Override
    public List<FeishuDocument> getRecentlyUpdatedDocuments(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return feishuDocumentRepository.findRecentlyUpdatedDocuments(pageable);
    }

    @Override
    @Transactional
    public FeishuDocument syncDocumentFromFeishu(String docId) {
        FeishuDocument document = getDocumentByDocId(docId);

        try {
            // 这里应该调用飞书API获取文档内容
            String content = feishuApiService.getDocumentContentByToken(document.getDocToken());
            document.setContent(content);
            document.setSyncStatus(FeishuDocument.SyncStatus.SYNCED);
            document.setLastSyncAt(LocalDateTime.now());

            return feishuDocumentRepository.save(document);
        } catch (Exception e) {
            log.error("同步飞书文档失败: {}", docId, e);
            document.setSyncStatus(FeishuDocument.SyncStatus.FAILED);
            document.setLastSyncAt(LocalDateTime.now());
            feishuDocumentRepository.save(document);
            throw new RuntimeException("同步文档失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void batchSyncDocuments(List<Long> documentIds) {
        for (Long documentId : documentIds) {
            try {
                FeishuDocument document = getDocumentById(documentId);
                syncDocumentFromFeishu(document.getDocId());
            } catch (Exception e) {
                log.error("批量同步文档失败: {}", documentId, e);
            }
        }
    }

    @Override
    public String getFeishuDocumentContent(String docId) {
        log.info("获取飞书文档内容: {}", docId);

        // 直接从数据库获取缓存的内容，不再检查过期时间
        Optional<FeishuDocument> documentOpt = feishuDocumentRepository.findByDocId(docId);
        if (documentOpt.isPresent()) {
            FeishuDocument document = documentOpt.get();
            if (document.getContent() != null && !document.getContent().isEmpty()) {
                log.info("从数据库缓存获取文档内容: {}, 内容长度: {}", docId, document.getContent().length());
                return document.getContent();
            } else {
                log.warn("文档存在但内容为空: {}", docId);
                throw new RuntimeException("文档内容为空，请先同步文档内容");
            }
        } else {
            log.warn("文档不存在: {}", docId);
            throw new RuntimeException("文档不存在: "+docId);
        }
    }

    /**
     * 生成空内容提示消息
     */
    private String generateEmptyContentMessage(String docId, String message) {
        StringBuilder content = new StringBuilder();
        content.append("# 文档内容提示\n\n");
        content.append("**文档ID**: ").append(docId).append("\n\n");
        content.append("**提示信息**: ").append(message).append("\n\n");
        content.append("## 解决方案\n\n");
        content.append("1. **如果文档不存在**：\n");
        content.append("   - 请先在飞书文档管理页面导入该文档\n");
        content.append("   - 或者检查文档ID是否正确\n\n");
        content.append("2. **如果文档内容为空**：\n");
        content.append("   - 请在飞书文档管理页面点击\"同步\"按钮\n");
        content.append("   - 或者等待系统自动同步文档内容\n\n");
        content.append("3. **如果问题持续存在**：\n");
        content.append("   - 请联系系统管理员\n");
        content.append("   - 或者检查飞书API配置是否正确\n\n");
        content.append("---\n\n");
        content.append("*生成时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("*");

        return content.toString();
    }

    /**
     * 生成模拟的飞书文档内容
     */
    private String generateMockFeishuContent(String docId) {
        StringBuilder content = new StringBuilder();
        content.append("# 飞书文档内容\n\n");
        content.append("**文档ID**: ").append(docId).append("\n\n");
        content.append("## 文档概述\n\n");
        content.append("这是一个示例飞书文档，展示了文档查看功能的实现。在实际应用中，这里的内容将通过飞书API从真实的飞书文档中获取。\n\n");
        content.append("## 主要内容\n\n");
        content.append("### 1. 功能特性\n");
        content.append("- 支持Markdown格式显示\n");
        content.append("- 实时同步飞书文档内容\n");
        content.append("- 支持多种文档类型\n");
        content.append("- 权限控制和访问管理\n\n");
        content.append("### 2. 技术实现\n");
        content.append("- 后端API集成\n");
        content.append("- 前端Vue.js组件\n");
        content.append("- 响应式设计\n");
        content.append("- 缓存机制\n\n");
        content.append("### 3. 使用说明\n");
        content.append("1. 点击文档列表中的\"查看\"按钮\n");
        content.append("2. 系统会自动获取文档内容\n");
        content.append("3. 内容以Markdown格式渲染显示\n");
        content.append("4. 支持全屏查看模式\n\n");
        content.append("## 示例代码\n\n");
        content.append("```javascript\n");
        content.append("// 获取文档内容的示例代码\n");
        content.append("async function getDocumentContent(docId) {\n");
        content.append("  try {\n");
        content.append("    const response = await api.getFeishuDocumentContent(docId);\n");
        content.append("    return response.data.content;\n");
        content.append("  } catch (error) {\n");
        content.append("    console.error('获取文档内容失败:', error);\n");
        content.append("  }\n");
        content.append("}\n");
        content.append("```\n\n");
        content.append("## 注意事项\n\n");
        content.append("> **重要**: 在生产环境中，需要配置真实的飞书API密钥和权限。\n\n");
        content.append("- 确保用户有访问文档的权限\n");
        content.append("- 定期同步文档内容以保持最新\n");
        content.append("- 处理网络异常和API限制\n\n");
        content.append("---\n\n");
        content.append("*最后更新时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("*");

        return content.toString();
    }

    @Override
    @Transactional
    public boolean updateFeishuDocumentContent(String docId, String content) {
        log.info("更新飞书文档内容: {}, 内容长度: {}", docId, content != null ? content.length() : 0);

        try {
            // 根据docId查找文档
            Optional<FeishuDocument> documentOpt = feishuDocumentRepository.findByDocId(docId);
            if (documentOpt.isPresent()) {
                FeishuDocument document = documentOpt.get();

                // 更新文档内容
                document.setContent(content);
                document.setUpdatedTime(LocalDateTime.now());
                document.setSyncStatus(FeishuDocument.SyncStatus.SYNCED);
                document.setLastSyncAt(LocalDateTime.now());

                // 保存到数据库
                feishuDocumentRepository.save(document);

                log.info("文档内容更新成功: {}", docId);
                return true;
            } else {
                log.warn("文档不存在，无法更新内容: {}", docId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新文档内容失败: {}", docId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getFeishuDocumentInfo(String docId) {
        // 简化实现，实际应该调用飞书API
        Map<String, Object> info = new HashMap<>();
        info.put("docId", docId);
        info.put("title", "文档标题");
        info.put("lastModified", LocalDateTime.now());
        return info;
    }

    @Override
    public Map<String, Object> createFeishuDocument(String title, String content, FeishuDocument.DocumentType docType) {
        // 简化实现，实际应该调用飞书API
        Map<String, Object> result = new HashMap<>();
        result.put("docId", "doc_" + System.currentTimeMillis());
        result.put("title", title);
        result.put("url", "https://feishu.cn/docs/" + result.get("docId"));
        return result;
    }

    @Override
    public Map<String, Object> getFeishuDocumentPermissions(String docId) {
        // 简化实现，实际应该调用飞书API
        Map<String, Object> permissions = new HashMap<>();
        permissions.put("docId", docId);
        permissions.put("accessLevel", "READ");
        permissions.put("collaborators", new ArrayList<>());
        return permissions;
    }

    @Override
    public boolean setFeishuDocumentPermissions(String docId, Map<String, Object> permissions) {
        // 简化实现，实际应该调用飞书API
        log.info("设置飞书文档权限: {}", docId);
        return true;
    }

    @Override
    @Transactional
    public void incrementDocumentViewCount(Long id) {
        feishuDocumentRepository.incrementViewCount(id);
    }

    @Override
    @Transactional
    public FeishuDocument incrementViewCount(Long id) {
        Optional<FeishuDocument> documentOpt = feishuDocumentRepository.findById(id);
        if (documentOpt.isPresent()) {
            FeishuDocument document = documentOpt.get();
            Long currentViewCount = document.getViewCount() != null ? document.getViewCount() : 0L;
            document.setViewCount(currentViewCount + 1);
            document.setUpdatedTime(LocalDateTime.now());

            FeishuDocument savedDocument = feishuDocumentRepository.save(document);
            log.info("文档查看次数已更新: {} -> {}", id, savedDocument.getViewCount());
            return savedDocument;
        } else {
            throw new RuntimeException("文档不存在: " + id);
        }
    }

    @Override
    @Transactional
    public void updateDocumentSyncStatus(Long id, FeishuDocument.SyncStatus syncStatus) {
        feishuDocumentRepository.updateSyncStatus(id, syncStatus, LocalDateTime.now());
    }

    @Override
    public List<FeishuDocument> getDocumentsNeedingSync() {
        LocalDateTime threshold = LocalDateTime.now().minusHours(24); // 24小时未同步的文档
        return feishuDocumentRepository.findDocumentsNeedingSync(threshold);
    }

    @Override
    @Transactional
    public void autoSyncDocuments() {
        List<FeishuDocument> documentsToSync = getDocumentsNeedingSync();
        for (FeishuDocument document : documentsToSync) {
            try {
                syncDocumentFromFeishu(document.getDocId());
            } catch (Exception e) {
                log.error("自动同步文档失败: {}", document.getId(), e);
            }
        }
    }

    @Override
    public Map<String, Object> getDocumentStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 总文档数
            long totalDocuments = feishuDocumentRepository.count();
            stats.put("totalDocuments", totalDocuments);

            // 活跃文档数 (状态为ACTIVE的文档)
            stats.put("activeDocuments", feishuDocumentRepository.countByStatus(FeishuDocument.DocumentStatus.ACTIVE));

            // 已同步文档数
            Long syncedCount = feishuDocumentRepository.countBySyncStatus(FeishuDocument.SyncStatus.SYNCED);
            stats.put("syncedDocuments", syncedCount != null ? syncedCount : 0L);

            // 总查看数 (所有文档的查看次数总和)
            Long totalViews = feishuDocumentRepository.sumViewCount();
            stats.put("totalViews", totalViews != null ? totalViews : 0L);

            // 文档类型分布（数组格式）
            List<Map<String, Object>> docTypeDistribution = new ArrayList<>();
            List<Object[]> typeStats = feishuDocumentRepository.countByDocType();
            for (Object[] stat : typeStats) {
                Map<String, Object> typeItem = new HashMap<>();
                typeItem.put("type", stat[0].toString());
                typeItem.put("count", stat[1]);
                docTypeDistribution.add(typeItem);
            }
            stats.put("docTypeDistribution", docTypeDistribution);

            // 同步状态分布（数组格式）
            List<Map<String, Object>> syncStatusDistribution = new ArrayList<>();
            for (FeishuDocument.SyncStatus syncStatus : FeishuDocument.SyncStatus.values()) {
                Long count = feishuDocumentRepository.countBySyncStatus(syncStatus);
                if (count != null && count > 0) {
                    Map<String, Object> statusItem = new HashMap<>();
                    statusItem.put("status", syncStatus.name());
                    statusItem.put("count", count);
                    syncStatusDistribution.add(statusItem);
                }
            }
            stats.put("syncStatusDistribution", syncStatusDistribution);

            // 学科分布（数组格式）
            List<Map<String, Object>> subjectDistribution = new ArrayList<>();
            List<Object[]> subjectStats = feishuDocumentRepository.countBySubject();
            for (Object[] stat : subjectStats) {
                Map<String, Object> subjectItem = new HashMap<>();
                subjectItem.put("subjectId", stat[0] != null ? stat[0] : 0L);
                subjectItem.put("subjectName", stat[0] != null ? stat[0].toString() : "未分类");
                subjectItem.put("count", stat[1]);
                subjectDistribution.add(subjectItem);
            }
            stats.put("subjectDistribution", subjectDistribution);

            log.info("成功获取文档统计信息 - 总文档数: {}", totalDocuments);

        } catch (Exception e) {
            log.error("获取文档统计信息失败", e);
            // 返回默认值
            stats.put("totalDocuments", 0L);
            stats.put("activeDocuments", 0L);
            stats.put("syncedDocuments", 0L);
            stats.put("totalViews", 0L);
            stats.put("docTypeDistribution", new ArrayList<>());
            stats.put("syncStatusDistribution", new ArrayList<>());
            stats.put("subjectDistribution", new ArrayList<>());
        }

        return stats;
    }

    @Override
    public Map<String, Object> getUserDocumentStatistics(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("userId", userId);

        // 简化实现，实际应该查询用户的文档统计
        stats.put("totalDocuments", 0);
        stats.put("createdDocuments", 0);
        stats.put("viewedDocuments", 0);

        return stats;
    }

    @Override
    public Map<String, Object> getSubjectDocumentStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 按学科统计文档数量
        List<Object[]> subjectStats = feishuDocumentRepository.countBySubject();
        Map<String, Long> subjectMap = new HashMap<>();
        for (Object[] stat : subjectStats) {
            subjectMap.put(stat[0] != null ? stat[0].toString() : "未分类", (Long) stat[1]);
        }
        stats.put("subjectStats", subjectMap);

        return stats;
    }

    @Override
    @Transactional
    public FeishuDocument importFeishuDocument(String docUrl) {
        try {
            // 验证输入参数
            if (docUrl == null || docUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("文档URL不能为空");
            }

            // 从URL中提取docId
            String docId = extractDocIdFromUrl(docUrl);
            if (docId == null || docId.trim().isEmpty()) {
                throw new IllegalArgumentException("无法从URL中提取有效的文档ID");
            }

            // 检查文档是否已存在
            if (feishuDocumentRepository.findByDocId(docId).isPresent()) {
                throw new RuntimeException("文档已存在: " + docId);
            }

            // 获取文档信息
            Map<String, Object> docInfo = getFeishuDocumentInfo(docId);
            if (docInfo == null || docInfo.get("title") == null) {
                throw new RuntimeException("无法获取文档信息: " + docId);
            }

            FeishuDocument document = new FeishuDocument();
            document.setDocId(docId);
            document.setTitle(docInfo.get("title").toString());
            document.setDocUrl(docUrl);
            document.setDocType(FeishuDocument.DocumentType.DOC);
            document.setStatus(FeishuDocument.DocumentStatus.ACTIVE);
            document.setAccessLevel(FeishuDocument.AccessLevel.INTERNAL); // 设置默认访问权限
            document.setSyncStatus(FeishuDocument.SyncStatus.PENDING);
            document.setIsPublic(false); // 明确设置为非公开
            document.setViewCount(0L); // 明确设置查看次数
            document.setVersion(1); // 明确设置版本号

            FeishuDocument savedDocument = feishuDocumentRepository.save(document);
            log.info("成功导入飞书文档: {}", docId);

            // 同步文档内容
            try {
                syncDocumentFromFeishu(docId);
            } catch (Exception e) {
                log.warn("文档导入成功但同步失败: {}", docId, e);
                // 不抛出异常，因为文档已经成功导入
            }

            return savedDocument;
        } catch (Exception e) {
            log.error("导入飞书文档失败: {}", docUrl, e);
            throw new RuntimeException("导入文档失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public List<FeishuDocument> batchImportFeishuDocuments(List<String> docUrls) {
        List<FeishuDocument> importedDocuments = new ArrayList<>();

        for (String docUrl : docUrls) {
            try {
                FeishuDocument document = importFeishuDocument(docUrl);
                importedDocuments.add(document);
            } catch (Exception e) {
                log.error("导入飞书文档失败: {}", docUrl, e);
            }
        }

        return importedDocuments;
    }

    @Override
    public String exportDocumentConfig(List<Long> documentIds) {
        List<FeishuDocument> documents;
        if (documentIds != null && !documentIds.isEmpty()) {
            documents = feishuDocumentRepository.findAllById(documentIds);
        } else {
            documents = feishuDocumentRepository.findAll();
        }

        // 简化实现，实际应该生成JSON或XML格式的配置
        StringBuilder config = new StringBuilder();
        config.append("Documents: ").append(documents.size()).append("\n");
        for (FeishuDocument doc : documents) {
            config.append("- ").append(doc.getTitle()).append(" (").append(doc.getDocId()).append(")\n");
        }

        return config.toString();
    }

    @Override
    @Transactional
    public List<FeishuDocument> importDocumentConfig(String config) {
        // 简化实现，实际应该解析配置文件
        List<FeishuDocument> importedDocuments = new ArrayList<>();

        // 这里应该解析配置并创建文档
        FeishuDocument document = new FeishuDocument();
        document.setTitle("导入的文档");
        document.setDocId("imported_" + System.currentTimeMillis());
        document.setDocType(FeishuDocument.DocumentType.DOC);
        document.setStatus(FeishuDocument.DocumentStatus.ACTIVE);
        document.setAccessLevel(FeishuDocument.AccessLevel.INTERNAL); // 设置默认访问权限
        document.setSyncStatus(FeishuDocument.SyncStatus.SYNCED);
        document.setIsPublic(false); // 明确设置为非公开
        document.setViewCount(0L); // 明确设置查看次数
        document.setVersion(1); // 明确设置版本号

        FeishuDocument savedDocument = feishuDocumentRepository.save(document);
        importedDocuments.add(savedDocument);

        return importedDocuments;
    }

    @Override
    public boolean hasDocumentAccess(Long userId, Long documentId) {
        FeishuDocument document = getDocumentById(documentId);

        // 简化实现，实际应该检查用户权限
        // 这里可以根据文档的访问级别和用户角色来判断
        return document.getAccessLevel() == FeishuDocument.AccessLevel.PUBLIC ||
               document.getCreatorId().equals(userId);
    }

    @Override
    public Page<FeishuDocument> getUserAccessibleDocuments(Long userId, Pageable pageable) {
        // 简化实现，实际应该根据用户权限过滤文档
        return feishuDocumentRepository.findDocumentsWithFilters(
                null, null, FeishuDocument.DocumentStatus.ACTIVE, userId, null, null, pageable);
    }

    @Override
    @Transactional
    public void setDocumentAccess(Long documentId, FeishuDocument.AccessLevel accessLevel) {
        FeishuDocument document = getDocumentById(documentId);
        document.setAccessLevel(accessLevel);
        feishuDocumentRepository.save(document);
    }

    @Override
    public List<Map<String, Object>> getDocumentCollaborators(String docId) {
        // 简化实现，实际应该调用飞书API获取协作者
        List<Map<String, Object>> collaborators = new ArrayList<>();

        Map<String, Object> collaborator = new HashMap<>();
        collaborator.put("email", "<EMAIL>");
        collaborator.put("permission", "READ");
        collaborator.put("name", "示例用户");
        collaborators.add(collaborator);

        return collaborators;
    }

    @Override
    public boolean addDocumentCollaborator(String docId, String userEmail, String permission) {
        // 简化实现，实际应该调用飞书API添加协作者
        log.info("添加文档协作者: docId={}, email={}, permission={}", docId, userEmail, permission);
        return true;
    }

    @Override
    public boolean removeDocumentCollaborator(String docId, String userEmail) {
        // 简化实现，实际应该调用飞书API移除协作者
        log.info("移除文档协作者: docId={}, email={}", docId, userEmail);
        return true;
    }

    /**
     * 从URL中提取文档ID
     */
    private String extractDocIdFromUrl(String docUrl) {
        // 简化实现，实际应该解析飞书文档URL
        if (docUrl.contains("/docs/")) {
            return docUrl.substring(docUrl.lastIndexOf("/") + 1);
        }
        return "doc_" + System.currentTimeMillis();
    }

    // ========== 飞书API集成实现 ==========

    @Override
    @Transactional
    public List<FeishuDocument> syncDocumentsFromFeishuApi(String folderId, Integer pageSize) {
        try {
            log.info("从飞书API同步文档列表 - folderId: {}, pageSize: {}", folderId, pageSize);

            List<FeishuFileDto> feishuFiles = feishuApiService.getFileList(folderId, null, pageSize);
            List<FeishuDocument> syncedDocuments = new ArrayList<>();

            for (FeishuFileDto feishuFile : feishuFiles) {
                // 跳过文件夹
                if (Boolean.TRUE.equals(feishuFile.getIsFolder())) {
                    continue;
                }

                // 检查文档是否已存在
                Optional<FeishuDocument> existingDoc = feishuDocumentRepository.findByDocId(feishuFile.getToken());

                FeishuDocument document;
                if (existingDoc.isPresent()) {
                    // 更新现有文档
                    document = existingDoc.get();
                    document.setTitle(feishuFile.getName());
                    document.setUpdatedTime(LocalDateTime.now());
                } else {
                    // 创建新文档
                    document = new FeishuDocument();
                    document.setDocId(feishuFile.getToken());
                    document.setTitle(feishuFile.getName());
                    document.setDocType(mapFeishuTypeToDocumentType(feishuFile.getType()));
                    document.setStatus(FeishuDocument.DocumentStatus.ACTIVE);
                    document.setAccessLevel(FeishuDocument.AccessLevel.INTERNAL);
                    document.setSyncStatus(FeishuDocument.SyncStatus.PENDING);
                    document.setIsPublic(false);
                    document.setViewCount(0L);
                    document.setVersion(1);
                    document.setCreatedTime(feishuFile.getCreateTime());
                }

                if (feishuFile.getUrl() != null) {
                    document.setDocUrl(feishuFile.getUrl());
                }

                document.setLastSyncAt(LocalDateTime.now());
                document.setSyncStatus(FeishuDocument.SyncStatus.SYNCED);

                FeishuDocument savedDocument = feishuDocumentRepository.save(document);
                syncedDocuments.add(savedDocument);

                log.debug("同步文档: {} - {}", savedDocument.getDocId(), savedDocument.getTitle());
            }

            log.info("成功从飞书API同步 {} 个文档", syncedDocuments.size());
            return syncedDocuments;

        } catch (Exception e) {
            log.error("从飞书API同步文档失败", e);
            throw new RuntimeException("同步文档失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getContentFromFeishuApi(String fileToken, String fileType) {
        log.info("从飞书API获取文档内容 - fileToken: {}, fileType: {}", fileToken, fileType);

        // 直接从数据库获取缓存的内容，不再检查过期时间
        Optional<FeishuDocument> documentOpt = feishuDocumentRepository.findByDocId(fileToken);
        if (documentOpt.isPresent()) {
            FeishuDocument document = documentOpt.get();
            if (document.getContent() != null && !document.getContent().isEmpty()) {
                log.info("从数据库缓存获取文档内容: {}, 内容长度: {}", fileToken, document.getContent().length());
                return document.getContent();
            } else {
                log.warn("文档存在但内容为空: {}", fileToken);
                return generateEmptyContentMessage(fileToken, "文档内容为空，请先同步文档内容");
            }
        } else {
            log.warn("文档不存在: {}", fileToken);
            return generateEmptyContentMessage(fileToken, "文档不存在，请先导入或同步文档");
        }
    }

    @Override
    public Map<String, Object> testFeishuApiConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("测试飞书API连接");

            boolean connected = feishuApiService.testConnection();
            result.put("connected", connected);
            result.put("timestamp", LocalDateTime.now());

            if (connected) {
                // 尝试获取用户信息
                try {
//                    Map<String, Object> userInfo = feishuApiService.getUserInfo();
                    Map<String, Object> userInfo = new HashMap<>();
                    result.put("userInfo", userInfo);
                } catch (Exception e) {
                    log.warn("获取用户信息失败", e);
                    result.put("userInfoError", e.getMessage());
                }

                // 尝试获取文件列表
                try {
                    List<FeishuFileDto> files = feishuApiService.getFileList(null, null, 5);
                    result.put("sampleFilesCount", files.size());
                    result.put("sampleFiles", files);
                } catch (Exception e) {
                    log.warn("获取文件列表失败", e);
                    result.put("fileListError", e.getMessage());
                }
            }

            log.info("飞书API连接测试完成 - 连接状态: {}", connected);
            return result;

        } catch (Exception e) {
            log.error("飞书API连接测试失败", e);
            result.put("connected", false);
            result.put("error", e.getMessage());
            result.put("timestamp", LocalDateTime.now());
            return result;
        }
    }

    @Override
    public Map<String, Object> getFeishuPersonalDocuments(String folderId, String pageToken, Integer pageSize) {
        try {
            log.info("获取飞书个人空间文档列表 - folderId: {}, pageToken: {}, pageSize: {}",
                    folderId, pageToken, pageSize);

            List<FeishuFileDto> files = feishuApiService.getFileList(folderId, pageToken, pageSize);

            Map<String, Object> result = new HashMap<>();
            result.put("files", files);
            result.put("total", files.size());
            result.put("folderId", folderId);
            result.put("pageToken", pageToken);
            result.put("pageSize", pageSize);
            result.put("timestamp", LocalDateTime.now());

            // 统计文件类型
            Map<String, Long> typeStats = files.stream()
                    .collect(Collectors.groupingBy(FeishuFileDto::getType, Collectors.counting()));

            result.put("typeStats", typeStats);

            log.info("成功获取飞书个人空间文档列表，共 {} 个文件", files.size());
            return result;

        } catch (Exception e) {
            log.error("获取飞书个人空间文档列表失败", e);
            throw new RuntimeException("获取文档列表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getDocumentContentByToken(String docToken) {
        log.info("根据docToken获取文档内容 - docToken: {}", docToken);

        // 直接从数据库获取缓存的内容，不再检查过期时间
        Optional<FeishuDocument> documentOpt = feishuDocumentRepository.findByDocId(docToken);
        if (documentOpt.isPresent()) {
            FeishuDocument document = documentOpt.get();
            if (document.getContent() != null && !document.getContent().isEmpty()) {
                log.info("从数据库缓存获取文档内容: {}, 内容长度: {}", docToken, document.getContent().length());
                return document.getContent();
            } else {

                throw new RuntimeException("文档内容为空，请先同步文档内容");
            }
        } else {
            log.warn("文档不存在: {}", docToken);
            throw new RuntimeException("文档不存在，请先导入或同步文档");
        }
    }

    @Override
    public List<FeishuDocument> getPopularDocuments(Integer limit) {
        try {
            log.info("获取热门飞书文档 - limit: {}", limit);

            Pageable pageable = PageRequest.of(0, limit != null ? limit : 10);
            Page<FeishuDocument> page = feishuDocumentRepository.findAllByOrderByViewCountDesc(pageable);

            List<FeishuDocument> popularDocs = page.getContent();
            log.info("成功获取热门飞书文档，数量: {}", popularDocs.size());

            return popularDocs;
        } catch (Exception e) {
            log.error("获取热门飞书文档失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 映射飞书文件类型到系统文档类型
     */
    private FeishuDocument.DocumentType mapFeishuTypeToDocumentType(String feishuType) {
        if (feishuType == null) {
            return FeishuDocument.DocumentType.DOC;
        }

        switch (feishuType.toLowerCase()) {
            case "doc":
            case "docx":
                return FeishuDocument.DocumentType.DOC;
            case "sheet":
                return FeishuDocument.DocumentType.SHEET;
            case "slides":
                return FeishuDocument.DocumentType.SLIDE;
            case "mindnote":
                return FeishuDocument.DocumentType.MINDMAP;
            case "bitable":
                return FeishuDocument.DocumentType.BITABLE;
            default:
                return FeishuDocument.DocumentType.DOC;
        }
    }

    /**
     * 根据docToken确定文档类型
     */
    private FeishuDocument.DocumentType determineDocTypeByToken(String docToken) {
        if (docToken == null) {
            return FeishuDocument.DocumentType.DOC;
        }

        if (docToken.startsWith("doccn")) {
            return FeishuDocument.DocumentType.DOC;
        } else if (docToken.startsWith("shtcn")) {
            return FeishuDocument.DocumentType.SHEET;
        } else if (docToken.startsWith("sldcn")) {
            return FeishuDocument.DocumentType.SLIDE;
        } else if (docToken.startsWith("bmncn")) {
            return FeishuDocument.DocumentType.MINDMAP;
        } else if (docToken.startsWith("bascn")) {
            return FeishuDocument.DocumentType.BITABLE;
        } else if (docToken.startsWith("wikicn")) {
            return FeishuDocument.DocumentType.DOC; // 知识库当作文档处理
        } else {
            return FeishuDocument.DocumentType.DOC;
        }
    }
}
