package com.lait.service.impl;

import com.lait.entity.StudyProgress;
import com.lait.entity.StudyRecord;
import com.lait.repository.StudyProgressRepository;
import com.lait.repository.StudyRecordRepository;
import com.lait.service.StudyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 学习服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StudyServiceImpl implements StudyService {

    private final StudyRecordRepository studyRecordRepository;
    private final StudyProgressRepository studyProgressRepository;

    // ========== 学习记录管理 ==========

    @Override
    @Transactional
    public StudyRecord recordStudy(StudyRecord studyRecord) {
        studyRecord.setCreatedTime(LocalDateTime.now());
        return studyRecordRepository.save(studyRecord);
    }

    @Override
    @Transactional
    public List<StudyRecord> batchRecordStudy(List<StudyRecord> studyRecords) {
        LocalDateTime now = LocalDateTime.now();
        studyRecords.forEach(record -> record.setCreatedTime(now));
        return studyRecordRepository.saveAll(studyRecords);
    }

    @Override
    public Page<StudyRecord> getUserStudyRecords(Long userId, Pageable pageable) {
        return studyRecordRepository.findByUserIdOrderByStudyDateDesc(userId, pageable);
    }

    @Override
    public Page<StudyRecord> getStudyRecords(Long userId, Long subjectId, StudyRecord.RecordType recordType,
                                           Boolean isCorrect, LocalDateTime startDate, LocalDateTime endDate,
                                           Pageable pageable) {
        // 简化实现，实际应该在Repository中添加相应的查询方法
        return studyRecordRepository.findAll(pageable);
    }

    @Override
    public List<StudyRecord> getUserWrongAnswers(Long userId, int limit) {
        // 简化实现
        return new ArrayList<>();
    }

    @Override
    public List<StudyRecord> getQuestionsForReview(Long userId, int days) {
        // 简化实现
        return new ArrayList<>();
    }

    // ========== 学习进度管理 ==========

    @Override
    @Transactional
    public StudyProgress updateStudyProgress(Long userId, Long subjectId, String chapter,
                                           boolean isCorrect, int timeSpent) {
        // 简化实现
        StudyProgress progress = new StudyProgress();
        progress.setUserId(userId);
        progress.setSubjectId(subjectId);
        progress.setChapter(chapter);
        progress.setCorrectQuestions(isCorrect ? 1 : 0);
        progress.setCompletedQuestions(1);
        progress.setTotalTimeSpent(timeSpent);
        progress.setLastStudyTime(LocalDateTime.now());
        return studyProgressRepository.save(progress);
    }

    @Override
    public List<StudyProgress> getUserStudyProgress(Long userId) {
        return studyProgressRepository.findByUserIdOrderByLastStudyTimeDesc(userId);
    }

    @Override
    public StudyProgress getUserSubjectProgress(Long userId, Long subjectId) {
        return studyProgressRepository.findByUserIdAndSubjectId(userId, subjectId).orElse(null);
    }

    @Override
    public StudyProgress getUserChapterProgress(Long userId, Long subjectId, String chapter) {
        return studyProgressRepository.findByUserIdAndSubjectIdAndChapter(userId, subjectId, chapter).orElse(null);
    }

    @Override
    public Page<StudyProgress> getStudyProgress(Long userId, Long subjectId, String chapter,
                                              StudyProgress.MasteryLevel masteryLevel, Pageable pageable) {
        // 简化实现
        return studyProgressRepository.findAll(pageable);
    }

    // ========== 学习统计分析 ==========

    @Override
    public Map<String, Object> getUserStudyStatistics(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("userId", userId);
        stats.put("totalStudyTime", 0);
        stats.put("totalQuestions", 0);
        stats.put("correctRate", 0.0);
        return stats;
    }

    @Override
    public Map<String, Object> getUserStudyStatistics(Long userId, LocalDateTime startDate, LocalDateTime endDate) {
        return getUserStudyStatistics(userId);
    }

    @Override
    public List<Map<String, Object>> getDailyStudyStatistics(Long userId, int days) {
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getSubjectStudyStatistics(Long userId) {
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getOverallProgress(Long userId) {
        Map<String, Object> progress = new HashMap<>();
        progress.put("userId", userId);
        progress.put("overallProgress", 0.0);
        return progress;
    }

    @Override
    public List<Map<String, Object>> getStudyLeaderboard(int limit) {
        return new ArrayList<>();
    }

    // ========== 学习推荐 ==========

    @Override
    public List<Map<String, Object>> recommendStudyContent(Long userId) {
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> recommendReviewQuestions(Long userId, int limit) {
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getWeakKnowledgePoints(Long userId) {
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getStrongAreas(Long userId) {
        return new ArrayList<>();
    }

    // ========== 学习计划 ==========

    @Override
    public Map<String, Object> generateStudyPlan(Long userId, Long subjectId, int days) {
        Map<String, Object> plan = new HashMap<>();
        plan.put("userId", userId);
        plan.put("subjectId", subjectId);
        plan.put("days", days);
        return plan;
    }

    @Override
    public void updateStudyPlanProgress(Long userId, Long planId, Map<String, Object> progress) {
        // 简化实现
    }

    @Override
    public List<Map<String, Object>> getUserStudyPlans(Long userId) {
        return new ArrayList<>();
    }

    // ========== 学习提醒 ==========

    @Override
    public List<StudyProgress> getUsersNeedingReminder(int days) {
        return new ArrayList<>();
    }

    @Override
    public void sendStudyReminder(Long userId, String message) {
        // 简化实现
    }

    @Override
    public void updateStudyStreak(Long userId) {
        // 简化实现
    }

    // ========== 学习成就 ==========

    @Override
    public List<Map<String, Object>> checkStudyAchievements(Long userId) {
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getUserAchievements(Long userId) {
        return new ArrayList<>();
    }

    @Override
    public void unlockAchievement(Long userId, String achievementType, Map<String, Object> data) {
        // 简化实现
    }

    // ========== 学习分析报告 ==========

    @Override
    public Map<String, Object> generateStudyReport(Long userId, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> report = new HashMap<>();
        report.put("userId", userId);
        report.put("startDate", startDate);
        report.put("endDate", endDate);
        return report;
    }

    @Override
    public Map<String, Object> generateClassStudyReport(String className, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> report = new HashMap<>();
        report.put("className", className);
        report.put("startDate", startDate);
        report.put("endDate", endDate);
        return report;
    }

    @Override
    public Map<String, Object> generateSubjectStudyReport(Long subjectId, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> report = new HashMap<>();
        report.put("subjectId", subjectId);
        report.put("startDate", startDate);
        report.put("endDate", endDate);
        return report;
    }

    // ========== 数据导出 ==========

    @Override
    public String exportUserStudyData(Long userId, LocalDateTime startDate, LocalDateTime endDate) {
        // 简化实现
        return "export_data_" + userId + ".csv";
    }

    @Override
    public String exportClassStudyData(String className, LocalDateTime startDate, LocalDateTime endDate) {
        // 简化实现
        return "export_class_" + className + ".csv";
    }

    @Override
    public Map<String, Object> getStudentStudyStatistics(Long studentId) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取学习记录
            List<StudyRecord> studyRecords = studyRecordRepository.findByUserIdOrderByStudyDateDesc(studentId, null).getContent();

            // 总学习时间（分钟）
            int totalStudyTime = studyRecords.stream()
                    .mapToInt(record -> record.getTimeSpent() != null ? record.getTimeSpent() : 0)
                    .sum();
            stats.put("totalStudyTime", totalStudyTime);

            // 完成题目数（包括练习和考试中的题目）
            long questionsCompleted = studyRecords.stream()
                    .filter(record -> record.getRecordType() == StudyRecord.RecordType.PRACTICE ||
                                    record.getRecordType() == StudyRecord.RecordType.EXAM)
                    .filter(record -> record.getQuestionId() != null)
                    .count();
            stats.put("questionsCompleted", questionsCompleted);

            // 正确率
            if (questionsCompleted > 0) {
                long correctAnswers = studyRecords.stream()
                        .filter(record -> record.getRecordType() == StudyRecord.RecordType.PRACTICE ||
                                        record.getRecordType() == StudyRecord.RecordType.EXAM)
                        .filter(record -> record.getQuestionId() != null)
                        .filter(record -> record.getIsCorrect() != null && record.getIsCorrect())
                        .count();
                double correctRate = (double) correctAnswers / questionsCompleted * 100;
                stats.put("correctRate", Math.round(correctRate * 100.0) / 100.0);
            } else {
                stats.put("correctRate", 0.0);
            }

            // 学习天数（简化计算：不同日期的记录数）
            long studyDays = studyRecords.stream()
                    .map(record -> record.getStudyDate().toLocalDate())
                    .distinct()
                    .count();
            stats.put("studyDays", studyDays);

            // 笔记数量（这里简化为0，实际应该查询笔记表）
            stats.put("notesCount", 0);

        } catch (Exception e) {
            log.error("获取学生学习统计失败", e);
            // 返回默认值
            stats.put("totalStudyTime", 0);
            stats.put("questionsCompleted", 0);
            stats.put("correctRate", 0.0);
            stats.put("studyDays", 0);
            stats.put("notesCount", 0);
        }

        return stats;
    }
}
