package com.lait.controller;

import com.lait.entity.Exam;
import com.lait.entity.ExamQuestion;
import com.lait.service.ExamService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 考试管理控制器
 */
@RestController
@RequestMapping("/admin/exams")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
public class ExamController {

    private final ExamService examService;

    /**
     * 创建考试
     */
    @PostMapping
    public ResponseEntity<Exam> createExam(@Valid @RequestBody Exam exam) {
        Exam createdExam = examService.createExam(exam);
        return ResponseEntity.ok(createdExam);
    }

    /**
     * 更新考试
     */
    @PutMapping("/{id}")
    public ResponseEntity<Exam> updateExam(@PathVariable Long id, @Valid @RequestBody Exam exam) {
        Exam updatedExam = examService.updateExam(id, exam);
        return ResponseEntity.ok(updatedExam);
    }

    /**
     * 删除考试
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteExam(@PathVariable Long id) {
        examService.deleteExam(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取考试详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Exam> getExam(@PathVariable Long id) {
        Exam exam = examService.getExamById(id);
        return ResponseEntity.ok(exam);
    }

    /**
     * 分页查询考试
     */
    @GetMapping
    public ResponseEntity<Page<Exam>> getExams(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Long subjectId,
            @RequestParam(required = false) Exam.ExamType examType,
            @RequestParam(required = false) Exam.ExamStatus status,
            @RequestParam(required = false) Long creatorId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Exam> exams = examService.getExams(title, subjectId, examType, status, creatorId, pageable);
        return ResponseEntity.ok(exams);
    }

    /**
     * 根据学科获取考试
     */
    @GetMapping("/subject/{subjectId}")
    public ResponseEntity<List<Exam>> getExamsBySubject(@PathVariable Long subjectId) {
        List<Exam> exams = examService.getExamsBySubject(subjectId);
        return ResponseEntity.ok(exams);
    }

    /**
     * 获取正在进行的考试
     */
    @GetMapping("/ongoing")
    public ResponseEntity<List<Exam>> getOngoingExams() {
        List<Exam> exams = examService.getOngoingExams();
        return ResponseEntity.ok(exams);
    }

    /**
     * 获取即将开始的考试
     */
    @GetMapping("/upcoming")
    public ResponseEntity<List<Exam>> getUpcomingExams(@RequestParam(defaultValue = "24") int hours) {
        List<Exam> exams = examService.getUpcomingExams(hours);
        return ResponseEntity.ok(exams);
    }

    /**
     * 发布考试
     */
    @PutMapping("/{id}/publish")
    public ResponseEntity<Void> publishExam(@PathVariable Long id) {
        examService.publishExam(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 开始考试
     */
    @PutMapping("/{id}/start")
    public ResponseEntity<Void> startExam(@PathVariable Long id) {
        examService.startExam(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 结束考试
     */
    @PutMapping("/{id}/end")
    public ResponseEntity<Void> endExam(@PathVariable Long id) {
        examService.endExam(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 取消考试
     */
    @PutMapping("/{id}/cancel")
    public ResponseEntity<Void> cancelExam(@PathVariable Long id) {
        examService.cancelExam(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 添加题目到考试
     */
    @PostMapping("/{examId}/questions/{questionId}")
    public ResponseEntity<Void> addQuestionToExam(@PathVariable Long examId,
                                                 @PathVariable Long questionId,
                                                 @RequestBody Map<String, Object> request) {
        Integer points = (Integer) request.get("points");
        Integer order = (Integer) request.get("order");
        examService.addQuestionToExam(examId, questionId, points, order);
        return ResponseEntity.ok().build();
    }

    /**
     * 从考试中移除题目
     */
    @DeleteMapping("/{examId}/questions/{questionId}")
    public ResponseEntity<Void> removeQuestionFromExam(@PathVariable Long examId,
                                                      @PathVariable Long questionId) {
        examService.removeQuestionFromExam(examId, questionId);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取考试题目列表
     */
    @GetMapping("/{examId}/questions")
    public ResponseEntity<List<ExamQuestion>> getExamQuestions(@PathVariable Long examId) {
        List<ExamQuestion> questions = examService.getExamQuestions(examId);
        return ResponseEntity.ok(questions);
    }

    /**
     * 批量添加题目到考试
     */
    @PostMapping("/{examId}/questions/batch")
    public ResponseEntity<Void> addQuestionsToExam(@PathVariable Long examId,
                                                   @RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> questionIds = (List<Long>) request.get("questionIds");
        examService.addQuestionsToExam(examId, questionIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 更新考试题目顺序
     */
    @PutMapping("/{examId}/questions/order")
    public ResponseEntity<Void> updateQuestionOrder(@PathVariable Long examId,
                                                   @RequestBody Map<Long, Integer> questionOrders) {
        examService.updateQuestionOrder(examId, questionOrders);
        return ResponseEntity.ok().build();
    }

    /**
     * 学生参加考试
     */
    @PostMapping("/{examId}/join")
    @PreAuthorize("hasRole('STUDENT')")
    public ResponseEntity<Map<String, Object>> joinExam(@PathVariable Long examId,
                                                       @RequestParam Long studentId) {
        Map<String, Object> result = examService.joinExam(examId, studentId);
        return ResponseEntity.ok(result);
    }

    /**
     * 提交考试答案
     */
    @PostMapping("/{examId}/submit")
    @PreAuthorize("hasRole('STUDENT')")
    public ResponseEntity<Map<String, Object>> submitExam(@PathVariable Long examId,
                                                         @RequestParam Long studentId,
                                                         @RequestBody Map<Long, String> answers) {
        Map<String, Object> result = examService.submitExam(examId, studentId, answers);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取考试参与者列表
     */
    @GetMapping("/{examId}/participants")
    public ResponseEntity<List<Map<String, Object>>> getExamParticipants(@PathVariable Long examId) {
        List<Map<String, Object>> participants = examService.getExamParticipants(examId);
        return ResponseEntity.ok(participants);
    }

    /**
     * 获取考试统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getExamStatistics() {
        Map<String, Object> statistics = examService.getExamStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取考试详细统计
     */
    @GetMapping("/{examId}/statistics")
    public ResponseEntity<Map<String, Object>> getExamDetailStatistics(@PathVariable Long examId) {
        Map<String, Object> statistics = examService.getExamDetailStatistics(examId);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取热门考试
     */
    @GetMapping("/popular")
    public ResponseEntity<List<Exam>> getPopularExams(@RequestParam(defaultValue = "10") int limit) {
        List<Exam> exams = examService.getPopularExams(limit);
        return ResponseEntity.ok(exams);
    }

    /**
     * 获取最近创建的考试
     */
    @GetMapping("/recent")
    public ResponseEntity<List<Exam>> getRecentExams(@RequestParam(defaultValue = "10") int limit) {
        List<Exam> exams = examService.getRecentExams(limit);
        return ResponseEntity.ok(exams);
    }

    /**
     * 创建考试模板
     */
    @PostMapping("/templates")
    public ResponseEntity<Exam> createExamTemplate(@Valid @RequestBody Exam exam) {
        Exam template = examService.createExamTemplate(exam);
        return ResponseEntity.ok(template);
    }

    /**
     * 从模板创建考试
     */
    @PostMapping("/templates/{templateId}/create")
    public ResponseEntity<Exam> createExamFromTemplate(@PathVariable Long templateId,
                                                      @Valid @RequestBody Exam examInfo) {
        Exam exam = examService.createExamFromTemplate(templateId, examInfo);
        return ResponseEntity.ok(exam);
    }

    /**
     * 获取考试模板列表
     */
    @GetMapping("/templates")
    public ResponseEntity<List<Exam>> getExamTemplates() {
        List<Exam> templates = examService.getExamTemplates();
        return ResponseEntity.ok(templates);
    }

    /**
     * 导出考试配置
     */
    @PostMapping("/{examId}/export")
    public ResponseEntity<String> exportExamConfig(@PathVariable Long examId) {
        String config = examService.exportExamConfig(examId);
        return ResponseEntity.ok(config);
    }

    /**
     * 导入考试配置
     */
    @PostMapping("/import")
    public ResponseEntity<Exam> importExamConfig(@RequestBody Map<String, String> request) {
        String config = request.get("config");
        Exam exam = examService.importExamConfig(config);
        return ResponseEntity.ok(exam);
    }

    /**
     * 自动处理考试状态
     */
    @PostMapping("/auto-process")
    public ResponseEntity<Void> autoProcessExamStatus() {
        examService.autoProcessExamStatus();
        return ResponseEntity.ok().build();
    }
}
