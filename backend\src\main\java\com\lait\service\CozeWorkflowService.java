package com.lait.service;

import com.lait.entity.CozeWorkflow;
import com.lait.entity.CozeWorkflowExecution;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Coze工作流服务接口
 */
public interface CozeWorkflowService {

    /**
     * 创建工作流
     */
    CozeWorkflow createWorkflow(CozeWorkflow workflow);

    /**
     * 更新工作流
     */
    CozeWorkflow updateWorkflow(Long id, CozeWorkflow workflow);

    /**
     * 删除工作流
     */
    void deleteWorkflow(Long id);

    /**
     * 根据ID获取工作流
     */
    CozeWorkflow getWorkflowById(Long id);

    /**
     * 分页查询工作流
     */
    Page<CozeWorkflow> getWorkflows(String name, CozeWorkflow.WorkflowType type,
                                   CozeWorkflow.WorkflowStatus status, String category,
                                   Long creatorId, Pageable pageable);

    /**
     * 获取所有工作流
     */
    List<CozeWorkflow> getAllWorkflows();

    /**
     * 根据状态获取工作流
     */
    List<CozeWorkflow> getWorkflowsByStatus(CozeWorkflow.WorkflowStatus status);

    /**
     * 根据类型获取工作流
     */
    List<CozeWorkflow> getWorkflowsByType(CozeWorkflow.WorkflowType type);

    /**
     * 获取公开的工作流
     */
    List<CozeWorkflow> getPublicWorkflows();

    /**
     * 执行工作流
     */
    CozeWorkflowExecution executeWorkflow(Long workflowId, Map<String, Object> inputData, Long executorId);

    /**
     * 异步执行工作流
     */
    String executeWorkflowAsync(Long workflowId, Map<String, Object> inputData, Long executorId);

    /**
     * 测试工作流
     */
    Map<String, Object> testWorkflow(Long workflowId, Map<String, Object> inputData);

    /**
     * 获取工作流执行记录
     */
    Page<CozeWorkflowExecution> getWorkflowExecutions(Long workflowId, CozeWorkflowExecution.ExecutionStatus status,
                                                     Long executorId, LocalDateTime startTime, LocalDateTime endTime,
                                                     Pageable pageable);

    /**
     * 获取执行记录详情
     */
    CozeWorkflowExecution getExecutionById(Long executionId);

    /**
     * 取消执行
     */
    void cancelExecution(Long executionId);

    /**
     * 重新执行
     */
    CozeWorkflowExecution retryExecution(Long executionId);

    /**
     * 复制工作流
     */
    CozeWorkflow duplicateWorkflow(Long workflowId, String newName);

    /**
     * 导入工作流
     */
    CozeWorkflow importWorkflow(String workflowConfig);

    /**
     * 导出工作流
     */
    String exportWorkflow(Long workflowId);

    /**
     * 激活工作流
     */
    void activateWorkflow(Long workflowId);

    /**
     * 停用工作流
     */
    void deactivateWorkflow(Long workflowId);

    /**
     * 归档工作流
     */
    void archiveWorkflow(Long workflowId);

    /**
     * 获取工作流统计信息
     */
    Map<String, Object> getWorkflowStatistics();

    /**
     * 获取工作流执行统计
     */
    Map<String, Object> getExecutionStatistics(Long workflowId);

    /**
     * 获取热门工作流
     */
    List<CozeWorkflow> getPopularWorkflows(int limit);

    /**
     * 获取最近执行的工作流
     */
    List<CozeWorkflow> getRecentlyExecutedWorkflows(int limit);

    /**
     * 清理过期执行记录
     */
    void cleanupOldExecutions(int daysToKeep);

    /**
     * 验证工作流配置
     */
    Map<String, Object> validateWorkflowConfig(String config);

    /**
     * 获取工作流模板
     */
    List<Map<String, Object>> getWorkflowTemplates();
}
