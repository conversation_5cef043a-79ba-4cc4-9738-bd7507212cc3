package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.dto.UserDTO;
import com.lait.entity.User;
import com.lait.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Validated
public class UserController {

    private final UserService userService;

    /**
     * 创建用户
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<UserDTO> createUser(@Valid @RequestBody UserDTO.CreateUserRequest request) {
        try {
            UserDTO user = userService.createUser(request);
            return ApiResponse.success("用户创建成功", user);
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @userService.getUserById(#id).username == authentication.name")
    public ApiResponse<UserDTO> getUserById(@PathVariable Long id) {
        try {
            UserDTO user = userService.getUserById(id);
            return ApiResponse.success(user);
        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return ApiResponse.notFound(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @userService.getUserById(#id).username == authentication.name")
    public ApiResponse<UserDTO> updateUser(@PathVariable Long id,
                                          @Valid @RequestBody UserDTO.UpdateUserRequest request) {
        try {
            UserDTO user = userService.updateUser(id, request);
            return ApiResponse.success("用户信息更新成功", user);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ApiResponse.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/{id}/password")
    @PreAuthorize("@userService.getUserById(#id).username == authentication.name")
    public ApiResponse<Void> changePassword(@PathVariable Long id,
                                           @Valid @RequestBody UserDTO.ChangePasswordRequest request) {
        try {
            userService.changePassword(id, request);
            return ApiResponse.success("密码修改成功");
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 分页查询用户
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ApiResponse<Page<UserDTO>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String keyword) {
        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<UserDTO> users;
            if (keyword != null && !keyword.trim().isEmpty()) {
                users = userService.searchUsers(keyword, pageable);
            } else if (role != null && !role.trim().isEmpty()) {
                users = userService.getUsersByRole(User.UserRole.valueOf(role.toUpperCase()), pageable);
            } else {
                users = userService.getUsers(pageable);
            }

            return ApiResponse.success(users);
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 根据年级获取学生
     */
    @GetMapping("/students/grade/{gradeLevel}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ApiResponse<List<UserDTO>> getStudentsByGrade(@PathVariable Integer gradeLevel) {
        try {
            List<UserDTO> students = userService.getStudentsByGrade(gradeLevel);
            return ApiResponse.success(students);
        } catch (Exception e) {
            log.error("获取年级学生失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 根据班级获取学生
     */
    @GetMapping("/students/class/{className}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ApiResponse<List<UserDTO>> getStudentsByClass(@PathVariable String className) {
        try {
            List<UserDTO> students = userService.getStudentsByClass(className);
            return ApiResponse.success(students);
        } catch (Exception e) {
            log.error("获取班级学生失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 激活用户
     */
    @PutMapping("/{id}/activate")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> activateUser(@PathVariable Long id) {
        try {
            userService.activateUser(id);
            return ApiResponse.success("用户激活成功");
        } catch (Exception e) {
            log.error("激活用户失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 停用用户
     */
    @PutMapping("/{id}/deactivate")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> deactivateUser(@PathVariable Long id) {
        try {
            userService.deactivateUser(id);
            return ApiResponse.success("用户停用成功");
        } catch (Exception e) {
            log.error("停用用户失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.existsByUsername(username);
        return ApiResponse.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        boolean exists = userService.existsByEmail(email);
        return ApiResponse.success(exists);
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> batchDeleteUsers(@RequestBody List<Long> userIds) {
        try {
            userService.batchDeleteUsers(userIds);
            return ApiResponse.success("批量删除用户成功");
        } catch (Exception e) {
            log.error("批量删除用户失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 批量更新用户状态
     */
    @PutMapping("/batch/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> batchUpdateUserStatus(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> userIds = (List<Long>) request.get("userIds");
            User.UserStatus status = User.UserStatus.valueOf((String) request.get("status"));
            userService.batchUpdateUserStatus(userIds, status);
            return ApiResponse.success("批量更新用户状态成功");
        } catch (Exception e) {
            log.error("批量更新用户状态失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/reset-password")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Map<String, String>> resetUserPassword(@PathVariable Long id) {
        try {
            String newPassword = userService.resetUserPassword(id);
            Map<String, String> result = new HashMap<>();
            result.put("newPassword", newPassword);
            return ApiResponse.success("密码重置成功", result);
        } catch (Exception e) {
            log.error("重置用户密码失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Map<String, Object>> getUserStatistics() {
        try {
            Map<String, Object> statistics = userService.getUserStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取用户学习统计
     */
    @GetMapping("/{id}/study-statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER') or @userService.getUserById(#id).username == authentication.name")
    public ApiResponse<Map<String, Object>> getUserStudyStatistics(@PathVariable Long id) {
        try {
            Map<String, Object> statistics = userService.getUserStudyStatistics(id);
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取用户学习统计失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 导出用户数据
     */
    @PostMapping("/export")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<String> exportUsers(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> userIds = (List<Long>) request.get("userIds");
            String format = (String) request.getOrDefault("format", "CSV");
            String exportData = userService.exportUsers(userIds, format);
            return ApiResponse.success("用户数据导出成功", exportData);
        } catch (Exception e) {
            log.error("导出用户数据失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 导入用户数据
     */
    @PostMapping("/import")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Map<String, Object>> importUsers(@RequestBody Map<String, String> request) {
        try {
            String data = request.get("data");
            String format = request.getOrDefault("format", "CSV");
            Map<String, Object> result = userService.importUsers(data, format);
            return ApiResponse.success("用户数据导入成功", result);
        } catch (Exception e) {
            log.error("导入用户数据失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
}
