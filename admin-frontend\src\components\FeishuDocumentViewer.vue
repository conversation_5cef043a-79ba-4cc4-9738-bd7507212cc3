<template>
  <div class="document-viewer">
    <!-- 文档头部信息 -->
    <div class="document-header">
      <div class="document-info">
        <h2 class="document-title">{{ document.title }}</h2>
        <div class="document-meta">
          <el-tag :type="getDocTypeColor(document.docType)" size="small">
            {{ getDocTypeText(document.docType) }}
          </el-tag>
          <el-tag :type="getStatusColor(document.status)" size="small">
            {{ getStatusText(document.status) }}
          </el-tag>
          <span class="meta-item">
            <el-icon><User /></el-icon>
            {{ document.creatorName || '未知' }}
          </span>
          <span class="meta-item">
            <el-icon><View /></el-icon>
            {{ document.viewCount || 0 }} 次查看
          </span>
          <span class="meta-item">
            <el-icon><Clock /></el-icon>
            {{ formatDateTime(document.updatedTime) }}
          </span>
        </div>
      </div>
      <div class="document-actions">
        <el-button
          type="primary"
          :icon="Refresh"
          @click="refreshContent"
          :loading="loading"
        >
          刷新内容
        </el-button>
        <el-button
          type="success"
          :icon="Download"
          @click="exportToPdf"
          :loading="exportingPdf"
        >
          导出PDF
        </el-button>
        <el-button
          type="info"
          :icon="Link"
          @click="openOriginalDoc"
        >
          打开原文档
        </el-button>
        <el-button
          :icon="fullscreen ? 'FullScreen' : 'ScaleToOriginal'"
          @click="toggleFullscreen"
        >
          {{ fullscreen ? '退出全屏' : '全屏查看' }}
        </el-button>
      </div>
    </div>

    <!-- 文档内容区域 -->
    <div class="document-content" :class="{ 'fullscreen': fullscreen }">
      <el-card v-loading="loading" element-loading-text="正在加载文档内容...">
        <div v-if="error" class="error-message">
          <el-alert
            :title="error"
            type="error"
            show-icon
            :closable="false"
          />
          <div class="error-actions">
            <el-button type="primary" @click="loadContent">重试</el-button>
          </div>
        </div>

        <div v-else-if="content" class="content-wrapper">
          <!-- Markdown渲染区域 -->
          <div class="markdown-content" v-html="renderedContent"></div>
        </div>

        <div v-else class="empty-content">
          <el-empty description="暂无文档内容" />
        </div>
      </el-card>
    </div>

    <!-- 全屏遮罩 -->
    <div v-if="fullscreen" class="fullscreen-overlay" @click="toggleFullscreen"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, View, Clock, Refresh, Link, Download } from '@element-plus/icons-vue'
import { getFeishuDocumentContent, incrementFeishuDocumentViewCount, getContentByDocToken } from '@/api/feishu'
import { marked } from 'marked'

// Props
const props = defineProps({
  document: {
    type: Object,
    required: true
  }
})

// 响应式数据
const loading = ref(false)
const content = ref('')
const error = ref('')
const fullscreen = ref(false)
const exportingPdf = ref(false)

// 计算属性
const renderedContent = computed(() => {
  if (!content.value) return ''

  try {
    // 配置marked选项
    marked.setOptions({
      breaks: true,
      gfm: true,
      headerIds: false,
      mangle: false
    })

    return marked(content.value)
  } catch (err) {
    console.error('Markdown渲染失败:', err)
    return `<pre>${content.value}</pre>`
  }
})

// 方法
const loadContent = async () => {
  // 验证必要的参数
  if (!props.document) {
    error.value = '文档对象不存在'
    return
  }

  if (!props.document.docToken && !props.document.id && !props.document.docId) {
    error.value = '文档标识符缺失（需要docToken、id或docId中的任意一个）'
    return
  }

  loading.value = true
  error.value = ''

  try {
    let response = null

    // 优先使用docToken获取内容
    if (props.document.id) {
      // 使用数据库ID获取内容
      console.log('使用文档ID加载文档内容:', props.document.id)
      response = await getFeishuDocumentContent(props.document.id)
    }
    debugger;
    if (response && response && response.content) {
      content.value = response.content
      console.log('文档内容加载成功，长度:', content.value.length)

      // 增加查看次数（仅当有数据库ID时）
      if (props.document.id) {
        try {
          await incrementFeishuDocumentViewCount(props.document.id)
        } catch (viewError) {
          console.warn('更新查看次数失败:', viewError)
        }
      }
    } else {
      error.value = '文档内容为空'
    }
  } catch (err) {
    console.error('加载文档内容失败:', err)
    error.value = err.response?.data?.message || err.message || '加载文档内容失败'
  } finally {
    loading.value = false
  }
}

const refreshContent = async () => {
  await loadContent()
  ElMessage.success('内容已刷新')
}

const openOriginalDoc = () => {
  if (props.document.docUrl) {
    window.open(props.document.docUrl, '_blank')
  } else {
    ElMessage.warning('原文档链接不可用')
  }
}

const toggleFullscreen = () => {
  fullscreen.value = !fullscreen.value

  if (fullscreen.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

// PDF导出功能
const exportToPdf = async () => {
  if (!content.value) {
    ElMessage.warning('文档内容为空，无法导出PDF')
    return
  }

  exportingPdf.value = true

  try {
    // 动态导入html2pdf库
    const html2pdf = (await import('html2pdf.js')).default

    // 创建一个临时的DOM元素用于PDF生成
    const element = document.createElement('div')
    element.innerHTML = `
      <div style="padding: 20px; font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; color: #333;">
        <h1 style="text-align: center; margin-bottom: 30px; color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
          ${props.document.title || '飞书文档'}
        </h1>
        <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #3498db;">
          <p style="margin: 5px 0;"><strong>文档类型:</strong> ${getDocTypeText(props.document.docType)}</p>
          <p style="margin: 5px 0;"><strong>创建者:</strong> ${props.document.creatorName || '未知'}</p>
          <p style="margin: 5px 0;"><strong>最后更新:</strong> ${formatDateTime(props.document.updatedTime)}</p>
          <p style="margin: 5px 0;"><strong>导出时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        <div style="margin-top: 30px;">
          ${renderedContent.value}
        </div>
      </div>
    `

    // PDF配置选项
    const options = {
      margin: [15, 15, 15, 15],
      filename: `${props.document.title || '飞书文档'}_${new Date().toISOString().slice(0, 10)}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2,
        useCORS: true,
        letterRendering: true,
        allowTaint: true
      },
      jsPDF: {
        unit: 'mm',
        format: 'a4',
        orientation: 'portrait',
        compress: true
      },
      pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
    }

    // 生成并下载PDF
    await html2pdf().set(options).from(element).save()

    ElMessage.success('PDF导出成功')
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败: ' + error.message)
  } finally {
    exportingPdf.value = false
  }
}

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape' && fullscreen.value) {
    toggleFullscreen()
  }
}

// 工具方法
const getDocTypeColor = (type) => {
  const colors = {
    DOC: 'primary',
    SHEET: 'success',
    SLIDE: 'warning',
    MINDMAP: 'info',
    BITABLE: 'danger'
  }
  return colors[type] || 'default'
}

const getDocTypeText = (type) => {
  const texts = {
    DOC: '文档',
    SHEET: '表格',
    SLIDE: '演示文稿',
    MINDMAP: '思维导图',
    BITABLE: '多维表格'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    ACTIVE: 'success',
    ARCHIVED: 'warning',
    DELETED: 'danger',
    DRAFT: 'info'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    ACTIVE: '正常',
    ARCHIVED: '已归档',
    DELETED: '已删除',
    DRAFT: '草稿'
  }
  return texts[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadContent()
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  if (fullscreen.value) {
    document.body.style.overflow = ''
  }
})
</script>

<style scoped>
.document-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}

.document-info {
  flex: 1;
}

.document-title {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.document-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 14px;
}

.document-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.document-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.document-content.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  background: #fff;
}

.document-content .el-card {
  height: 100%;
  border: none;
}

.document-content .el-card :deep(.el-card__body) {
  height: 100%;
  padding: 0;
}

.content-wrapper {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
}

.markdown-content {
  max-width: none;
  line-height: 1.6;
  color: #333;
}

/* Markdown样式 */
.markdown-content :deep(h1) {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee;
  color: #2c3e50;
}

.markdown-content :deep(h2) {
  font-size: 24px;
  font-weight: 600;
  margin: 30px 0 15px 0;
  color: #34495e;
}

.markdown-content :deep(h3) {
  font-size: 20px;
  font-weight: 600;
  margin: 25px 0 12px 0;
  color: #34495e;
}

.markdown-content :deep(p) {
  margin: 0 0 15px 0;
  text-align: justify;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0 0 15px 0;
  padding-left: 25px;
}

.markdown-content :deep(li) {
  margin: 5px 0;
}

.markdown-content :deep(blockquote) {
  margin: 15px 0;
  padding: 10px 15px;
  background: #f8f9fa;
  border-left: 4px solid #409eff;
  color: #666;
}

.markdown-content :deep(code) {
  background: #f1f2f3;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.markdown-content :deep(pre) {
  background: #f6f8fa;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 15px 0;
  border: 1px solid #e1e4e8;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
  font-size: 14px;
  line-height: 1.45;
}

.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content :deep(th) {
  background: #f5f5f5;
  font-weight: 600;
}

.error-message {
  padding: 20px;
  text-align: center;
}

.error-actions {
  margin-top: 15px;
}

.empty-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-header {
    flex-direction: column;
    gap: 15px;
  }

  .document-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .document-meta {
    gap: 10px;
  }

  .content-wrapper {
    padding: 15px;
  }

  .markdown-content :deep(h1) {
    font-size: 24px;
  }

  .markdown-content :deep(h2) {
    font-size: 20px;
  }

  .markdown-content :deep(h3) {
    font-size: 18px;
  }
}
</style>
