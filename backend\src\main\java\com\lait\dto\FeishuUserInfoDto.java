package com.lait.dto;

import lombok.Data;

/**
 * 飞书用户信息DTO
 */
@Data
public class FeishuUserInfoDto {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户OpenID
     */
    private String openId;
    
    /**
     * 用户UnionID
     */
    private String unionId;
    
    /**
     * 用户名称
     */
    private String name;
    
    /**
     * 用户英文名
     */
    private String enName;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 用户手机号
     */
    private String mobile;
    
    /**
     * 用户头像URL
     */
    private String avatarUrl;
    
    /**
     * 用户头像缩略图URL
     */
    private String avatarThumb;
    
    /**
     * 用户头像中等尺寸URL
     */
    private String avatarMiddle;
    
    /**
     * 用户头像大尺寸URL
     */
    private String avatarBig;
    
    /**
     * 租户Key
     */
    private String tenantKey;
    
    /**
     * 员工ID
     */
    private String employeeId;
    
    /**
     * 员工编号
     */
    private String employeeNo;
    
    /**
     * 部门ID列表
     */
    private String[] departmentIds;
    
    /**
     * 工作城市
     */
    private String workStation;
    
    /**
     * 国家或地区
     */
    private String country;
    
    /**
     * 时区
     */
    private String timeZone;
}
