package com.lait.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * 考试题目关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "exam_questions")
public class ExamQuestion extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "exam_id", nullable = false)
    private Long examId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "exam_id", insertable = false, updatable = false)
    private Exam exam;

    @Column(name = "question_id", nullable = false)
    private Long questionId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private Question question;

    @Column(name = "question_order")
    private Integer questionOrder;

    @Column(name = "points")
    private Integer points;

    @Column(name = "time_limit")
    private Integer timeLimit; // 单题时间限制（秒）

    @Column(name = "is_required")
    private Boolean isRequired = true;

    @Column(columnDefinition = "TEXT")
    private String customOptions; // 自定义选项（JSON格式）

    @Column(columnDefinition = "TEXT")
    private String customAnswer; // 自定义答案

    @Column(columnDefinition = "TEXT")
    private String customExplanation; // 自定义解析
}
