package com.lait.dto;

import com.lait.entity.Grade;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成绩数据传输对象
 */
@Data
public class GradeDTO {
    
    private Long id;
    private Long studentId;
    private String studentName;
    private Long subjectId;
    private String subjectName;
    private String examName;
    private String examType;
    private BigDecimal score;
    private BigDecimal totalScore;
    private Integer classRank;
    private Integer gradeRank;
    private String comments;
    private Grade.GradeStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * 创建成绩请求
     */
    @Data
    public static class CreateGradeRequest {
        
        @NotNull(message = "学生ID不能为空")
        private Long studentId;
        
        @NotNull(message = "学科ID不能为空")
        private Long subjectId;
        
        @Size(max = 100, message = "考试名称长度不能超过100个字符")
        private String examName;
        
        @Size(max = 50, message = "考试类型长度不能超过50个字符")
        private String examType;
        
        @NotNull(message = "分数不能为空")
        @DecimalMin(value = "0.0", message = "分数不能小于0")
        @DecimalMax(value = "999.99", message = "分数不能大于999.99")
        private BigDecimal score;
        
        @DecimalMin(value = "0.0", message = "总分不能小于0")
        @DecimalMax(value = "999.99", message = "总分不能大于999.99")
        private BigDecimal totalScore;
        
        private Integer classRank;
        private Integer gradeRank;
        
        @Size(max = 500, message = "评语长度不能超过500个字符")
        private String comments;
        
        private Grade.GradeStatus status = Grade.GradeStatus.DRAFT;
    }

    /**
     * 更新成绩请求
     */
    @Data
    public static class UpdateGradeRequest {
        
        @Size(max = 100, message = "考试名称长度不能超过100个字符")
        private String examName;
        
        @Size(max = 50, message = "考试类型长度不能超过50个字符")
        private String examType;
        
        @DecimalMin(value = "0.0", message = "分数不能小于0")
        @DecimalMax(value = "999.99", message = "分数不能大于999.99")
        private BigDecimal score;
        
        @DecimalMin(value = "0.0", message = "总分不能小于0")
        @DecimalMax(value = "999.99", message = "总分不能大于999.99")
        private BigDecimal totalScore;
        
        private Integer classRank;
        private Integer gradeRank;
        
        @Size(max = 500, message = "评语长度不能超过500个字符")
        private String comments;
        
        private Grade.GradeStatus status;
    }

    /**
     * 成绩查询请求
     */
    @Data
    public static class GradeQueryRequest {
        private Long studentId;
        private Long subjectId;
        private String examType;
        private Grade.GradeStatus status;
        private String startDate;
        private String endDate;
    }

    /**
     * 成绩统计信息
     */
    @Data
    public static class GradeStatistics {
        private Long studentId;
        private String studentName;
        private Long subjectId;
        private String subjectName;
        private BigDecimal averageScore;
        private BigDecimal highestScore;
        private BigDecimal lowestScore;
        private Integer totalExams;
        private Double improvementRate;
    }
}
