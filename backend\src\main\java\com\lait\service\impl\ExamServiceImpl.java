package com.lait.service.impl;

import com.lait.entity.Exam;
import com.lait.entity.ExamQuestion;
import com.lait.entity.Grade;
import com.lait.entity.Question;
import com.lait.repository.ExamQuestionRepository;
import com.lait.repository.ExamRepository;
import com.lait.repository.GradeRepository;
import com.lait.repository.QuestionRepository;
import com.lait.service.ExamService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 考试服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExamServiceImpl implements ExamService {

    private final ExamRepository examRepository;
    private final ExamQuestionRepository examQuestionRepository;
    private final QuestionRepository questionRepository;
    private final GradeRepository gradeRepository;

    @Override
    @Transactional
    public Exam createExam(Exam exam) {
        exam.setStatus(Exam.ExamStatus.DRAFT);
        exam.setQuestionCount(0);
        exam.setTotalPoints(0);
        exam.setParticipantCount(0);
        return examRepository.save(exam);
    }

    @Override
    @Transactional
    public Exam updateExam(Long id, Exam exam) {
        Exam existingExam = getExamById(id);

        // 只有草稿状态的考试才能修改基本信息
        if (existingExam.getStatus() != Exam.ExamStatus.DRAFT) {
            throw new RuntimeException("只有草稿状态的考试才能修改");
        }

        exam.setId(id);
        exam.setQuestionCount(existingExam.getQuestionCount());
        exam.setTotalPoints(existingExam.getTotalPoints());
        exam.setParticipantCount(existingExam.getParticipantCount());

        return examRepository.save(exam);
    }

    @Override
    @Transactional
    public void deleteExam(Long id) {
        Exam exam = getExamById(id);

        // 只有草稿状态的考试才能删除
        if (exam.getStatus() != Exam.ExamStatus.DRAFT) {
            throw new RuntimeException("只有草稿状态的考试才能删除");
        }

        // 删除考试题目关联
        examQuestionRepository.deleteByExamId(id);

        // 删除考试
        examRepository.deleteById(id);
    }

    @Override
    public Exam getExamById(Long id) {
        return examRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("考试不存在: " + id));
    }

    @Override
    public Page<Exam> getExams(String title, Long subjectId, Exam.ExamType examType,
                              Exam.ExamStatus status, Long creatorId, Pageable pageable) {
        return examRepository.findExamsWithFilters(title, subjectId, examType, status, creatorId, pageable);
    }

    @Override
    public List<Exam> getExamsBySubject(Long subjectId) {
        return examRepository.findBySubjectIdOrderByCreatedTimeDesc(subjectId);
    }

    @Override
    public List<Exam> getExamsByCreator(Long creatorId) {
        return examRepository.findByCreatorIdOrderByCreatedTimeDesc(creatorId);
    }

    @Override
    public List<Exam> getOngoingExams() {
        return examRepository.findOngoingExams(LocalDateTime.now());
    }

    @Override
    public List<Exam> getUpcomingExams(int hours) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.plusHours(hours);
        return examRepository.findUpcomingExams(now, threshold);
    }

    @Override
    @Transactional
    public void addQuestionToExam(Long examId, Long questionId, Integer points, Integer order) {
        Exam exam = getExamById(examId);
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在: " + questionId));

        // 检查题目是否已在考试中
        if (examQuestionRepository.existsByExamIdAndQuestionId(examId, questionId)) {
            throw new RuntimeException("题目已在考试中");
        }

        ExamQuestion examQuestion = new ExamQuestion();
        examQuestion.setExamId(examId);
        examQuestion.setQuestionId(questionId);
        examQuestion.setPoints(points != null ? points : question.getPoints());

        if (order == null) {
            Integer maxOrder = examQuestionRepository.getMaxQuestionOrder(examId);
            examQuestion.setQuestionOrder(maxOrder != null ? maxOrder + 1 : 1);
        } else {
            examQuestion.setQuestionOrder(order);
        }

        examQuestionRepository.save(examQuestion);

        // 更新考试统计信息
        updateExamStatistics(examId);
    }

    @Override
    @Transactional
    public void removeQuestionFromExam(Long examId, Long questionId) {
        if (!examQuestionRepository.existsByExamIdAndQuestionId(examId, questionId)) {
            throw new RuntimeException("题目不在考试中");
        }

        examQuestionRepository.deleteByExamIdAndQuestionId(examId, questionId);

        // 更新考试统计信息
        updateExamStatistics(examId);
    }

    @Override
    public List<ExamQuestion> getExamQuestions(Long examId) {
        return examQuestionRepository.findByExamIdOrderByQuestionOrder(examId);
    }

    @Override
    @Transactional
    public void addQuestionsToExam(Long examId, List<Long> questionIds) {
        for (Long questionId : questionIds) {
            if (!examQuestionRepository.existsByExamIdAndQuestionId(examId, questionId)) {
                addQuestionToExam(examId, questionId, null, null);
            }
        }
    }

    @Override
    @Transactional
    public void updateQuestionOrder(Long examId, Map<Long, Integer> questionOrders) {
        for (Map.Entry<Long, Integer> entry : questionOrders.entrySet()) {
            Long questionId = entry.getKey();
            Integer order = entry.getValue();
            examQuestionRepository.updateQuestionOrder(examId, questionId, order);
        }
    }

    @Override
    @Transactional
    public void publishExam(Long id) {
        Exam exam = getExamById(id);

        if (exam.getStatus() != Exam.ExamStatus.DRAFT) {
            throw new RuntimeException("只有草稿状态的考试才能发布");
        }

        if (exam.getQuestionCount() == 0) {
            throw new RuntimeException("考试必须包含题目才能发布");
        }

        exam.setStatus(Exam.ExamStatus.PUBLISHED);
        examRepository.save(exam);
    }

    @Override
    @Transactional
    public void startExam(Long id) {
        Exam exam = getExamById(id);

        if (exam.getStatus() != Exam.ExamStatus.PUBLISHED) {
            throw new RuntimeException("只有已发布的考试才能开始");
        }

        exam.setStatus(Exam.ExamStatus.ONGOING);
        if (exam.getStartTime() == null) {
            exam.setStartTime(LocalDateTime.now());
        }
        examRepository.save(exam);
    }

    @Override
    @Transactional
    public void endExam(Long id) {
        Exam exam = getExamById(id);

        if (exam.getStatus() != Exam.ExamStatus.ONGOING) {
            throw new RuntimeException("只有进行中的考试才能结束");
        }

        exam.setStatus(Exam.ExamStatus.ENDED);
        if (exam.getEndTime() == null) {
            exam.setEndTime(LocalDateTime.now());
        }
        examRepository.save(exam);
    }

    @Override
    @Transactional
    public void cancelExam(Long id) {
        Exam exam = getExamById(id);

        if (exam.getStatus() == Exam.ExamStatus.ENDED) {
            throw new RuntimeException("已结束的考试不能取消");
        }

        exam.setStatus(Exam.ExamStatus.CANCELLED);
        examRepository.save(exam);
    }

    @Override
    @Transactional
    public void autoProcessExamStatus() {
        LocalDateTime now = LocalDateTime.now();

        // 自动开始考试
        List<Exam> examsToStart = examRepository.findExamsToStart(now);
        for (Exam exam : examsToStart) {
            exam.setStatus(Exam.ExamStatus.ONGOING);
            examRepository.save(exam);
        }

        // 自动结束考试
        List<Exam> examsToEnd = examRepository.findExamsToEnd(now);
        for (Exam exam : examsToEnd) {
            exam.setStatus(Exam.ExamStatus.ENDED);
            examRepository.save(exam);
        }
    }

    @Override
    public Map<String, Object> joinExam(Long examId, Long studentId) {
        Exam exam = getExamById(examId);

        if (!canStudentJoinExam(examId, studentId)) {
            throw new RuntimeException("学生不能参加此考试");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("examId", examId);
        result.put("studentId", studentId);
        result.put("questions", getExamQuestions(examId));
        result.put("startTime", LocalDateTime.now());
        result.put("endTime", exam.getEndTime());
        result.put("duration", exam.getDurationMinutes());

        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> submitExam(Long examId, Long studentId, Map<Long, String> answers) {
        Exam exam = getExamById(examId);

        // 这里应该创建成绩记录，简化实现
        Map<String, Object> result = new HashMap<>();
        result.put("examId", examId);
        result.put("studentId", studentId);
        result.put("submitTime", LocalDateTime.now());
        result.put("score", 0); // 实际应该计算分数
        result.put("totalScore", exam.getTotalPoints());

        return result;
    }

    @Override
    public List<Map<String, Object>> getExamParticipants(Long examId) {
        // 简化实现，实际应该查询参与考试的学生
        return new ArrayList<>();
    }

    @Override
    public boolean canStudentJoinExam(Long examId, Long studentId) {
        Exam exam = getExamById(examId);

        // 检查考试状态
        if (exam.getStatus() != Exam.ExamStatus.ONGOING && exam.getStatus() != Exam.ExamStatus.PUBLISHED) {
            return false;
        }

        // 检查时间
        LocalDateTime now = LocalDateTime.now();
        if (exam.getStartTime() != null && now.isBefore(exam.getStartTime())) {
            return false;
        }
        if (exam.getEndTime() != null && now.isAfter(exam.getEndTime())) {
            return false;
        }

        return true;
    }

    @Override
    public Map<String, Object> getExamStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总考试数
        stats.put("totalExams", examRepository.count());

        // 按状态统计
        List<Object[]> statusStats = examRepository.countByStatus();
        Map<String, Long> statusMap = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("statusStats", statusMap);

        // 按类型统计
        List<Object[]> typeStats = examRepository.countByExamType();
        Map<String, Long> typeMap = new HashMap<>();
        for (Object[] stat : typeStats) {
            typeMap.put(stat[0].toString(), (Long) stat[1]);
        }
        stats.put("typeStats", typeMap);

        return stats;
    }

    @Override
    public Map<String, Object> getExamDetailStatistics(Long examId) {
        Exam exam = getExamById(examId);

        Map<String, Object> stats = new HashMap<>();
        stats.put("examId", examId);
        stats.put("title", exam.getTitle());
        stats.put("questionCount", exam.getQuestionCount());
        stats.put("totalPoints", exam.getTotalPoints());
        stats.put("participantCount", exam.getParticipantCount());

        // 这里可以添加更多详细统计
        stats.put("averageScore", 0.0);
        stats.put("passRate", 0.0);

        return stats;
    }

    @Override
    public Map<String, Object> getUserExamStatistics(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("userId", userId);

        // 简化实现，实际应该查询用户的考试记录
        stats.put("totalExams", 0);
        stats.put("passedExams", 0);
        stats.put("averageScore", 0.0);

        return stats;
    }

    @Override
    public List<Exam> getPopularExams(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return examRepository.findPopularExams(pageable);
    }

    @Override
    public List<Exam> getRecentExams(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return examRepository.findRecentExams(pageable);
    }

    @Override
    @Transactional
    public Exam createExamTemplate(Exam exam) {
        exam.setStatus(Exam.ExamStatus.TEMPLATE);
        return examRepository.save(exam);
    }

    @Override
    @Transactional
    public Exam createExamFromTemplate(Long templateId, Exam examInfo) {
        Exam template = getExamById(templateId);

        if (template.getStatus() != Exam.ExamStatus.TEMPLATE) {
            throw new RuntimeException("指定的考试不是模板");
        }

        // 复制模板信息
        Exam newExam = new Exam();
        newExam.setTitle(examInfo.getTitle());
        newExam.setDescription(examInfo.getDescription());
        newExam.setSubjectId(template.getSubjectId());
        newExam.setExamType(template.getExamType());
        newExam.setDurationMinutes(template.getDurationMinutes());
        newExam.setPassScore(template.getPassScore());
        newExam.setMaxAttempts(template.getMaxAttempts());
        newExam.setShuffleQuestions(template.getShuffleQuestions());
        newExam.setShuffleOptions(template.getShuffleOptions());
        newExam.setShowResultImmediately(template.getShowResultImmediately());
        newExam.setAllowReview(template.getAllowReview());
        newExam.setInstructions(template.getInstructions());
        newExam.setCreatorId(examInfo.getCreatorId());
        newExam.setStartTime(examInfo.getStartTime());
        newExam.setEndTime(examInfo.getEndTime());
        newExam.setStatus(Exam.ExamStatus.DRAFT);

        Exam savedExam = examRepository.save(newExam);

        // 复制题目
        List<ExamQuestion> templateQuestions = examQuestionRepository.findByExamIdOrderByQuestionOrder(templateId);
        for (ExamQuestion templateQuestion : templateQuestions) {
            ExamQuestion newQuestion = new ExamQuestion();
            newQuestion.setExamId(savedExam.getId());
            newQuestion.setQuestionId(templateQuestion.getQuestionId());
            newQuestion.setQuestionOrder(templateQuestion.getQuestionOrder());
            newQuestion.setPoints(templateQuestion.getPoints());
            newQuestion.setTimeLimit(templateQuestion.getTimeLimit());
            newQuestion.setIsRequired(templateQuestion.getIsRequired());
            examQuestionRepository.save(newQuestion);
        }

        // 更新统计信息
        updateExamStatistics(savedExam.getId());

        return getExamById(savedExam.getId());
    }

    @Override
    public List<Exam> getExamTemplates() {
        return examRepository.findByStatus(Exam.ExamStatus.TEMPLATE);
    }

    @Override
    public String exportExamConfig(Long examId) {
        Exam exam = getExamById(examId);
        List<ExamQuestion> questions = getExamQuestions(examId);

        // 简化实现，实际应该生成JSON或XML格式的配置
        StringBuilder config = new StringBuilder();
        config.append("Exam: ").append(exam.getTitle()).append("\n");
        config.append("Questions: ").append(questions.size()).append("\n");

        return config.toString();
    }

    @Override
    @Transactional
    public Exam importExamConfig(String config) {
        // 简化实现，实际应该解析配置文件
        Exam exam = new Exam();
        exam.setTitle("导入的考试");
        exam.setStatus(Exam.ExamStatus.DRAFT);

        return examRepository.save(exam);
    }

    @Override
    @Transactional
    public List<Exam> batchImportExams(String config) {
        // 简化实现
        return Arrays.asList(importExamConfig(config));
    }

    /**
     * 更新考试统计信息
     */
    private void updateExamStatistics(Long examId) {
        Long questionCount = examQuestionRepository.countByExamId(examId);
        Integer totalPoints = examQuestionRepository.sumPointsByExamId(examId);

        Exam exam = getExamById(examId);
        exam.setQuestionCount(questionCount.intValue());
        exam.setTotalPoints(totalPoints != null ? totalPoints : 0);
        examRepository.save(exam);
    }
}
