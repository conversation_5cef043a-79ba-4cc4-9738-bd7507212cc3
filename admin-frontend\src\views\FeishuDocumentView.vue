<template>
  <div class="document-view-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button
          :icon="ArrowLeft"
          @click="goBack"
          type="text"
          size="large"
        >
          返回列表
        </el-button>
        <el-divider direction="vertical" />
        <h1 v-if="document">{{ document.title }}</h1>
        <el-skeleton v-else animated>
          <template #template>
            <el-skeleton-item variant="h1" style="width: 300px" />
          </template>
        </el-skeleton>
      </div>
      <div class="header-right">
        <el-button
          v-if="document"
          type="primary"
          :icon="Edit"
          @click="editDocument"
        >
          编辑文档
        </el-button>
      </div>
    </div>

    <!-- 文档查看器 -->
    <div class="viewer-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton animated>
          <template #template>
            <div class="skeleton-header">
              <el-skeleton-item variant="h3" style="width: 40%" />
              <div class="skeleton-meta">
                <el-skeleton-item variant="text" style="width: 100px" />
                <el-skeleton-item variant="text" style="width: 80px" />
                <el-skeleton-item variant="text" style="width: 120px" />
              </div>
            </div>
            <el-skeleton-item variant="rect" style="height: 400px; margin-top: 20px" />
          </template>
        </el-skeleton>
      </div>

      <div v-else-if="error" class="error-container">
        <el-result
          icon="error"
          :title="error"
          sub-title="请检查文档ID是否正确，或稍后重试"
        >
          <template #extra>
            <el-button type="primary" @click="loadDocument">重新加载</el-button>
            <el-button @click="goBack">返回列表</el-button>
          </template>
        </el-result>
      </div>

      <FeishuDocumentViewer
        v-else-if="document"
        :document="document"
        class="document-viewer"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit } from '@element-plus/icons-vue'
import { getFeishuDocument } from '@/api/feishu'
import FeishuDocumentViewer from '@/components/FeishuDocumentViewer.vue'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const document = ref(null)
const error = ref('')

// 方法
const loadDocument = async () => {
  const documentId = route.params.id
  if (!documentId) {
    error.value = '文档ID参数缺失'
    return
  }

  loading.value = true
  error.value = ''

  try {
    debugger;
    console.log('加载文档信息:', documentId)
    const response = await getFeishuDocument(documentId)

    if (response && response) {
      document.value = response
      console.log('文档信息加载成功:', document.value)
      console.log('文档标识符检查:', {
        id: document.value.id,
        docId: document.value.docId,
        docToken: document.value.docToken
      })
    } else {
      error.value = '文档数据格式异常'
      console.error('响应数据格式错误:', response)
    }
  } catch (err) {
    console.error('加载文档信息失败:', err)
    error.value = err.response?.data?.message || err.message || '加载文档信息失败'
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/feishu')
}

const editDocument = () => {
  if (document.value) {
    router.push(`/feishu/edit/${document.value.id}`)
  }
}

// 生命周期
onMounted(() => {
  loadDocument()
})
</script>

<style scoped>
.document-view-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  max-width: 500px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-right {
  display: flex;
  gap: 10px;
}

.viewer-container {
  flex: 1;
  overflow: hidden;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-container {
  padding: 20px;
}

.skeleton-header {
  margin-bottom: 20px;
}

.skeleton-meta {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.error-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.document-viewer {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .header-left {
    width: 100%;
  }

  .header-left h1 {
    max-width: none;
    font-size: 18px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .viewer-container {
    margin: 10px;
  }
}
</style>
