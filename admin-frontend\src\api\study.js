import request from './request'

// ========== 学习记录管理 ==========

// 记录学习行为
export function recordStudy(data) {
  return request({
    url: '/admin/study/records',
    method: 'post',
    data
  })
}

// 批量记录学习行为
export function batchRecordStudy(studyRecords) {
  return request({
    url: '/admin/study/records/batch',
    method: 'post',
    data: studyRecords
  })
}

// 获取用户学习记录
export function getUserStudyRecords(userId, params) {
  return request({
    url: `/admin/study/records/users/${userId}`,
    method: 'get',
    params
  })
}

// 获取学习记录（带筛选）
export function getStudyRecords(params) {
  return request({
    url: '/admin/study/records',
    method: 'get',
    params
  })
}

// 获取用户错题记录
export function getUserWrongAnswers(userId, limit = 50) {
  return request({
    url: `/admin/study/records/users/${userId}/wrong-answers`,
    method: 'get',
    params: { limit }
  })
}

// 获取需要复习的题目
export function getQuestionsForReview(userId, days = 7) {
  return request({
    url: `/admin/study/records/users/${userId}/review`,
    method: 'get',
    params: { days }
  })
}

// ========== 学习进度管理 ==========

// 更新学习进度
export function updateStudyProgress(data) {
  return request({
    url: '/admin/study/progress',
    method: 'post',
    data
  })
}

// 获取用户学习进度
export function getUserStudyProgress(userId) {
  return request({
    url: `/admin/study/progress/users/${userId}`,
    method: 'get'
  })
}

// 获取用户在指定学科的进度
export function getUserSubjectProgress(userId, subjectId) {
  return request({
    url: `/admin/study/progress/users/${userId}/subjects/${subjectId}`,
    method: 'get'
  })
}

// 分页查询学习进度
export function getStudyProgress(params) {
  return request({
    url: '/admin/study/progress',
    method: 'get',
    params
  })
}

// ========== 学习统计分析 ==========

// 获取用户学习统计
export function getUserStudyStatistics(userId) {
  return request({
    url: `/admin/study/statistics/users/${userId}`,
    method: 'get'
  })
}

// 获取用户在指定时间范围内的学习统计
export function getUserStudyStatisticsRange(userId, startDate, endDate) {
  return request({
    url: `/admin/study/statistics/users/${userId}/range`,
    method: 'get',
    params: { startDate, endDate }
  })
}

// 获取用户每日学习统计
export function getDailyStudyStatistics(userId, days = 30) {
  return request({
    url: `/admin/study/statistics/users/${userId}/daily`,
    method: 'get',
    params: { days }
  })
}

// 获取用户各学科学习统计
export function getSubjectStudyStatistics(userId) {
  return request({
    url: `/admin/study/statistics/users/${userId}/subjects`,
    method: 'get'
  })
}

// 获取用户总体学习进度
export function getOverallProgress(userId) {
  return request({
    url: `/admin/study/statistics/users/${userId}/overall`,
    method: 'get'
  })
}

// 获取学习排行榜
export function getStudyLeaderboard(limit = 10) {
  return request({
    url: '/admin/study/statistics/leaderboard',
    method: 'get',
    params: { limit }
  })
}

// ========== 学习推荐 ==========

// 推荐学习内容
export function recommendStudyContent(userId) {
  return request({
    url: `/admin/study/recommendations/users/${userId}/content`,
    method: 'get'
  })
}

// 推荐复习题目
export function recommendReviewQuestions(userId, limit = 20) {
  return request({
    url: `/admin/study/recommendations/users/${userId}/review`,
    method: 'get',
    params: { limit }
  })
}

// 获取用户薄弱知识点
export function getWeakKnowledgePoints(userId) {
  return request({
    url: `/admin/study/analysis/users/${userId}/weak-points`,
    method: 'get'
  })
}

// 获取用户擅长领域
export function getStrongAreas(userId) {
  return request({
    url: `/admin/study/analysis/users/${userId}/strong-areas`,
    method: 'get'
  })
}

// ========== 学习计划 ==========

// 生成学习计划
export function generateStudyPlan(userId, data) {
  return request({
    url: `/admin/study/plans/users/${userId}`,
    method: 'post',
    data
  })
}

// 获取用户学习计划
export function getUserStudyPlans(userId) {
  return request({
    url: `/admin/study/plans/users/${userId}`,
    method: 'get'
  })
}

// ========== 学习分析报告 ==========

// 生成学习分析报告
export function generateStudyReport(userId, data) {
  return request({
    url: `/admin/study/reports/users/${userId}`,
    method: 'post',
    data
  })
}

// 生成班级学习报告
export function generateClassStudyReport(className, data) {
  return request({
    url: `/admin/study/reports/classes/${className}`,
    method: 'post',
    data
  })
}

// 导出用户学习数据
export function exportUserStudyData(userId, data) {
  return request({
    url: `/admin/study/export/users/${userId}`,
    method: 'post',
    data
  })
}
