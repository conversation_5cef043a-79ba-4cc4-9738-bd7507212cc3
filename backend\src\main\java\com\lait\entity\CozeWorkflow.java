package com.lait.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Coze工作流实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "coze_workflows")
public class CozeWorkflow extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 工作流名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * 工作流描述
     */
    @Column(length = 500)
    private String description;

    /**
     * 工作流ID (Coze平台的工作流ID)
     */
    @Column(length = 100)
    private String workflowId;

    /**
     * 工作流类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private WorkflowType type;

    /**
     * 工作流状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private WorkflowStatus status;

    /**
     * 工作流配置 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String config;

    /**
     * 输入参数配置 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String inputConfig;

    /**
     * 输出参数配置 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String outputConfig;

    /**
     * 关联的Token ID
     */
    @Column(name = "token_id")
    private Long tokenId;

    /**
     * 创建者ID
     */
    @Column(name = "creator_id")
    private Long creatorId;

    /**
     * 创建者名称
     */
    @Column(length = 50)
    private String creatorName;

    /**
     * 分类
     */
    @Column(length = 50)
    private String category;

    /**
     * 标签 (逗号分隔)
     */
    @Column(length = 200)
    private String tags;

    /**
     * 执行次数
     */
    @Column(nullable = false)
    private Long executionCount = 0L;

    /**
     * 最后执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime lastExecutedAt;

    /**
     * 平均执行时间 (毫秒)
     */
    private Long avgExecutionTime;

    /**
     * 成功执行次数
     */
    @Column(nullable = false)
    private Long successCount = 0L;

    /**
     * 失败执行次数
     */
    @Column(nullable = false)
    private Long failureCount = 0L;

    /**
     * 是否公开
     */
    @Column(nullable = false)
    private Boolean isPublic = false;

    /**
     * 版本号
     */
    @Column(nullable = false)
    private Integer version = 1;

    /**
     * 工作流类型枚举
     */
    public enum WorkflowType {
        QUESTION_GENERATION("题目生成"),
        CONTENT_ANALYSIS("内容分析"),
        STUDY_GUIDANCE("学习指导"),
        ANSWER_EXPLANATION("答案解释"),
        PERFORMANCE_ANALYSIS("表现分析"),
        CUSTOM("自定义");

        private final String description;

        WorkflowType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 工作流状态枚举
     */
    public enum WorkflowStatus {
        DRAFT("草稿"),
        ACTIVE("激活"),
        INACTIVE("停用"),
        TESTING("测试中"),
        ARCHIVED("已归档");

        private final String description;

        WorkflowStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
