<template>
  <div class="points-page">
    <div class="page-header">
      <h1>积分管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateConfigDialog">
          <el-icon><Plus /></el-icon>
          添加积分配置
        </el-button>
        <el-button @click="initializeDefaultConfig">
          <el-icon><Setting /></el-icon>
          初始化默认配置
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ configStats.totalCount || 0 }}</div>
              <div class="stat-label">积分配置</div>
            </div>
            <el-icon class="stat-icon"><Setting /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card active">
            <div class="stat-content">
              <div class="stat-number">{{ recordStats.totalRecords || 0 }}</div>
              <div class="stat-label">积分记录</div>
            </div>
            <el-icon class="stat-icon"><Coin /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ recordStats.totalEarned || 0 }}</div>
              <div class="stat-label">总获得积分</div>
            </div>
            <el-icon class="stat-icon"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-number">{{ recordStats.totalSpent || 0 }}</div>
              <div class="stat-label">总消费积分</div>
            </div>
            <el-icon class="stat-icon"><Minus /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 积分配置 -->
      <el-tab-pane label="积分配置" name="config">
        <el-card class="search-card">
          <el-form :model="configSearchForm" inline>
            <el-form-item label="配置名称">
              <el-input v-model="configSearchForm.displayName" placeholder="请输入配置名称" clearable />
            </el-form-item>
            <el-form-item label="分类">
              <el-select v-model="configSearchForm.category" placeholder="选择分类" clearable>
                <el-option label="学习行为" value="LEARNING" />
                <el-option label="练习行为" value="PRACTICE" />
                <el-option label="社交行为" value="SOCIAL" />
                <el-option label="成就奖励" value="ACHIEVEMENT" />
                <el-option label="扣分项目" value="PENALTY" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="configSearchForm.enabled" placeholder="选择状态" clearable>
                <el-option label="启用" :value="true" />
                <el-option label="停用" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleConfigSearch">搜索</el-button>
              <el-button @click="resetConfigSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="table-card">
          <div class="table-header">
            <div class="table-title">积分配置列表</div>
            <div class="table-actions">
              <el-button
                type="success"
                :disabled="selectedConfigs.length === 0"
                @click="batchUpdateConfigStatus(true)"
              >
                批量启用
              </el-button>
              <el-button
                type="warning"
                :disabled="selectedConfigs.length === 0"
                @click="batchUpdateConfigStatus(false)"
              >
                批量停用
              </el-button>
            </div>
          </div>

          <el-table
            v-loading="configLoading"
            :data="pointsConfigs"
            style="width: 100%"
            @selection-change="handleConfigSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="displayName" label="配置名称" min-width="150" show-overflow-tooltip />
            <el-table-column prop="configKey" label="配置键" min-width="150" show-overflow-tooltip />
            <el-table-column prop="category" label="分类" width="120">
              <template #default="{ row }">
                <el-tag :type="getCategoryColor(row.category)">
                  {{ getCategoryText(row.category) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="积分值" width="100" />
            <el-table-column prop="dailyLimit" label="每日限制" width="100">
              <template #default="{ row }">
                {{ row.dailyLimit || '无限制' }}
              </template>
            </el-table-column>
            <el-table-column prop="enabled" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.enabled ? 'success' : 'danger'">
                  {{ row.enabled ? '启用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sortOrder" label="排序" width="80" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="viewConfig(row)">查看</el-button>
                <el-button size="small" type="primary" @click="editConfig(row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteConfig(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page="configPagination.page"
            :page-size="configPagination.size"
            :total="configPagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleConfigSizeChange"
            @current-change="handleConfigCurrentChange"
          />
        </el-card>
      </el-tab-pane>

      <!-- 积分记录 -->
      <el-tab-pane label="积分记录" name="records">
        <el-card class="search-card">
          <el-form :model="recordSearchForm" inline>
            <el-form-item label="用户ID">
              <el-input v-model="recordSearchForm.userId" placeholder="请输入用户ID" clearable />
            </el-form-item>
            <el-form-item label="积分类型">
              <el-select v-model="recordSearchForm.pointsType" placeholder="选择类型" clearable>
                <el-option label="获得" value="EARN" />
                <el-option label="消费" value="SPEND" />
                <el-option label="扣除" value="PENALTY" />
                <el-option label="奖励" value="BONUS" />
                <el-option label="退还" value="REFUND" />
              </el-select>
            </el-form-item>
            <el-form-item label="来源">
              <el-input v-model="recordSearchForm.source" placeholder="请输入来源" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleRecordSearch">搜索</el-button>
              <el-button @click="resetRecordSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="table-card">
          <div class="table-header">
            <div class="table-title">积分记录列表</div>
            <div class="table-actions">
              <el-button type="primary" @click="showUserPointsDialog">
                用户积分管理
              </el-button>
              <el-button type="success" @click="showRankingDialog">
                积分排行榜
              </el-button>
            </div>
          </div>

          <el-table
            v-loading="recordLoading"
            :data="pointsRecords"
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="userId" label="用户ID" width="100" />
            <el-table-column prop="pointsChange" label="积分变化" width="120">
              <template #default="{ row }">
                <span :class="row.pointsChange > 0 ? 'text-success' : 'text-danger'">
                  {{ row.pointsChange > 0 ? '+' : '' }}{{ row.pointsChange }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="totalPoints" label="总积分" width="100" />
            <el-table-column prop="source" label="来源" min-width="120" show-overflow-tooltip />
            <el-table-column prop="pointsType" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getPointsTypeColor(row.pointsType)">
                  {{ getPointsTypeText(row.pointsType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
            <el-table-column prop="createdTime" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.createdTime) }}
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page="recordPagination.page"
            :page-size="recordPagination.size"
            :total="recordPagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleRecordSizeChange"
            @current-change="handleRecordCurrentChange"
          />
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑积分配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      :title="isEditConfig ? '编辑积分配置' : '创建积分配置'"
      width="600px"
      @close="resetConfigForm"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="configRules"
        label-width="120px"
      >
        <el-form-item label="配置名称" prop="displayName">
          <el-input v-model="configForm.displayName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置键" prop="configKey">
          <el-input v-model="configForm.configKey" placeholder="请输入配置键" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="configForm.category" placeholder="选择分类">
            <el-option label="学习行为" value="LEARNING" />
            <el-option label="练习行为" value="PRACTICE" />
            <el-option label="社交行为" value="SOCIAL" />
            <el-option label="成就奖励" value="ACHIEVEMENT" />
            <el-option label="扣分项目" value="PENALTY" />
          </el-select>
        </el-form-item>
        <el-form-item label="积分值" prop="points">
          <el-input-number v-model="configForm.points" :min="-1000" :max="1000" />
        </el-form-item>
        <el-form-item label="每日限制">
          <el-input-number v-model="configForm.dailyLimit" :min="0" placeholder="0表示无限制" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="configForm.sortOrder" :min="0" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="configForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="configForm.enabled">启用</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitConfigForm" :loading="configSubmitting">
          {{ isEditConfig ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Setting, Coin, TrendCharts, Minus } from '@element-plus/icons-vue'
import {
  getPointsConfigs,
  createPointsConfig,
  updatePointsConfig,
  deletePointsConfig,
  batchUpdatePointsConfigStatus,
  initializeDefaultPointsConfig,
  getPointsRecords,
  getPointsConfigStatistics,
  getPointsRecordStatistics
} from '@/api/points'

// 响应式数据
const activeTab = ref('config')
const configLoading = ref(false)
const recordLoading = ref(false)
const configSubmitting = ref(false)
const configDialogVisible = ref(false)
const isEditConfig = ref(false)

const pointsConfigs = ref([])
const pointsRecords = ref([])
const selectedConfigs = ref([])
const currentConfig = ref(null)
const configStats = ref({})
const recordStats = ref({})

// 搜索表单
const configSearchForm = reactive({
  displayName: '',
  category: '',
  enabled: null
})

const recordSearchForm = reactive({
  userId: '',
  pointsType: '',
  source: ''
})

// 分页
const configPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const recordPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const configForm = reactive({
  displayName: '',
  configKey: '',
  category: '',
  points: 0,
  dailyLimit: 0,
  sortOrder: 0,
  description: '',
  enabled: true
})

// 表单引用
const configFormRef = ref()

// 表单验证规则
const configRules = {
  displayName: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  configKey: [
    { required: true, message: '请输入配置键', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  points: [
    { required: true, message: '请输入积分值', trigger: 'blur' }
  ]
}

// 方法
const loadPointsConfigs = async () => {
  configLoading.value = true
  try {
    const params = {
      page: configPagination.page - 1,
      size: configPagination.size,
      ...configSearchForm
    }
    const response = await getPointsConfigs(params)
    pointsConfigs.value = response.content
    configPagination.total = response.totalElements
  } catch (error) {
    ElMessage.error('加载积分配置失败')
  } finally {
    configLoading.value = false
  }
}

const loadPointsRecords = async () => {
  recordLoading.value = true
  try {
    const params = {
      page: recordPagination.page - 1,
      size: recordPagination.size,
      ...recordSearchForm
    }
    const response = await getPointsRecords(params)
    pointsRecords.value = response.content
    recordPagination.total = response.totalElements
  } catch (error) {
    ElMessage.error('加载积分记录失败')
  } finally {
    recordLoading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const [configResponse, recordResponse] = await Promise.all([
      getPointsConfigStatistics(),
      getPointsRecordStatistics()
    ])
    configStats.value = configResponse
    recordStats.value = recordResponse
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const handleTabChange = (tab) => {
  if (tab === 'config') {
    loadPointsConfigs()
  } else if (tab === 'records') {
    loadPointsRecords()
  }
}

const handleConfigSearch = () => {
  configPagination.page = 1
  loadPointsConfigs()
}

const resetConfigSearch = () => {
  Object.assign(configSearchForm, {
    displayName: '',
    category: '',
    enabled: null
  })
  handleConfigSearch()
}

const handleRecordSearch = () => {
  recordPagination.page = 1
  loadPointsRecords()
}

const resetRecordSearch = () => {
  Object.assign(recordSearchForm, {
    userId: '',
    pointsType: '',
    source: ''
  })
  handleRecordSearch()
}

const handleConfigSizeChange = (size) => {
  configPagination.size = size
  configPagination.page = 1
  loadPointsConfigs()
}

const handleConfigCurrentChange = (page) => {
  configPagination.page = page
  loadPointsConfigs()
}

const handleRecordSizeChange = (size) => {
  recordPagination.size = size
  recordPagination.page = 1
  loadPointsRecords()
}

const handleRecordCurrentChange = (page) => {
  recordPagination.page = page
  loadPointsRecords()
}

const handleConfigSelectionChange = (selection) => {
  selectedConfigs.value = selection
}

const showCreateConfigDialog = () => {
  isEditConfig.value = false
  configDialogVisible.value = true
  resetConfigForm()
}

const editConfig = (config) => {
  isEditConfig.value = true
  configDialogVisible.value = true
  Object.assign(configForm, config)
  currentConfig.value = config
}

const viewConfig = (config) => {
  // 可以实现查看详情的逻辑
  ElMessage.info('查看功能待实现')
}

const submitConfigForm = async () => {
  if (!configFormRef.value) return

  try {
    await configFormRef.value.validate()
    configSubmitting.value = true

    if (isEditConfig.value) {
      await updatePointsConfig(currentConfig.value.id, configForm)
      ElMessage.success('更新成功')
    } else {
      await createPointsConfig(configForm)
      ElMessage.success('创建成功')
    }

    configDialogVisible.value = false
    loadPointsConfigs()
    loadStatistics()
  } catch (error) {
    ElMessage.error(isEditConfig.value ? '更新失败' : '创建失败')
  } finally {
    configSubmitting.value = false
  }
}

const deleteConfig = async (config) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除积分配置 "${config.displayName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deletePointsConfig(config.id)
    ElMessage.success('删除成功')
    loadPointsConfigs()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const batchUpdateConfigStatus = async (enabled) => {
  try {
    const ids = selectedConfigs.value.map(config => config.id)
    await batchUpdatePointsConfigStatus(ids, enabled)
    ElMessage.success('批量更新成功')
    loadPointsConfigs()
    loadStatistics()
  } catch (error) {
    ElMessage.error('批量更新失败')
  }
}

const initializeDefaultConfig = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要初始化默认积分配置吗？这将创建一些预设的积分规则。',
      '确认初始化',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await initializeDefaultPointsConfig()
    ElMessage.success('初始化成功')
    loadPointsConfigs()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('初始化失败')
    }
  }
}

const showUserPointsDialog = () => {
  ElMessage.info('用户积分管理功能待实现')
}

const showRankingDialog = () => {
  ElMessage.info('积分排行榜功能待实现')
}

const resetConfigForm = () => {
  Object.assign(configForm, {
    displayName: '',
    configKey: '',
    category: '',
    points: 0,
    dailyLimit: 0,
    sortOrder: 0,
    description: '',
    enabled: true
  })
  if (configFormRef.value) {
    configFormRef.value.clearValidate()
  }
}

// 辅助方法
const getCategoryColor = (category) => {
  const colors = {
    LEARNING: 'primary',
    PRACTICE: 'success',
    SOCIAL: 'warning',
    ACHIEVEMENT: 'info',
    PENALTY: 'danger'
  }
  return colors[category] || 'default'
}

const getCategoryText = (category) => {
  const texts = {
    LEARNING: '学习行为',
    PRACTICE: '练习行为',
    SOCIAL: '社交行为',
    ACHIEVEMENT: '成就奖励',
    PENALTY: '扣分项目'
  }
  return texts[category] || category
}

const getPointsTypeColor = (type) => {
  const colors = {
    EARN: 'success',
    SPEND: 'primary',
    PENALTY: 'danger',
    BONUS: 'warning',
    REFUND: 'info'
  }
  return colors[type] || 'default'
}

const getPointsTypeText = (type) => {
  const texts = {
    EARN: '获得',
    SPEND: '消费',
    PENALTY: '扣除',
    BONUS: '奖励',
    REFUND: '退还'
  }
  return texts[type] || type
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadPointsConfigs()
  loadStatistics()
})
</script>

<style scoped>
.points-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card .el-card__body {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  font-size: 40px;
  opacity: 0.3;
}

.stat-card.active .stat-number,
.stat-card.active .stat-icon {
  color: #409eff;
}

.stat-card.success .stat-number,
.stat-card.success .stat-icon {
  color: #67c23a;
}

.stat-card.warning .stat-number,
.stat-card.warning .stat-icon {
  color: #e6a23c;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-dialog .el-form {
  padding: 0 20px;
}

.el-tabs {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .points-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .el-table {
    font-size: 12px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .page-header h1 {
    color: #e5eaf3;
  }

  .stat-number {
    color: #e5eaf3;
  }

  .stat-label {
    color: #a3a6ad;
  }

  .table-title {
    color: #e5eaf3;
  }
}
</style>
