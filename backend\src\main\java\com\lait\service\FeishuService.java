package com.lait.service;

import com.lait.entity.FeishuDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 飞书服务接口
 */
public interface FeishuService {

    // ========== 文档管理 ==========

    /**
     * 创建飞书文档记录
     */
    FeishuDocument createDocument(FeishuDocument document);

    /**
     * 更新飞书文档记录
     */
    FeishuDocument updateDocument(Long id, FeishuDocument document);

    /**
     * 删除飞书文档记录
     */
    void deleteDocument(Long id);

    /**
     * 根据ID获取文档
     */
    FeishuDocument getDocumentById(Long id);

    /**
     * 根据文档ID获取文档
     */
    FeishuDocument getDocumentByDocId(String docId);

    /**
     * 分页查询文档
     */
    Page<FeishuDocument> getDocuments(String title, FeishuDocument.DocumentType docType,
                                     FeishuDocument.DocumentStatus status, Long creatorId,
                                     Long subjectId, String category, Pageable pageable);

    /**
     * 搜索文档
     */
    Page<FeishuDocument> searchDocuments(String keyword, Pageable pageable);

    /**
     * 获取热门文档
     */
    List<FeishuDocument> getPopularDocuments(int limit);

    /**
     * 获取最近更新的文档
     */
    List<FeishuDocument> getRecentlyUpdatedDocuments(int limit);

    // ========== 飞书API集成 ==========

    /**
     * 从飞书同步文档内容
     */
    FeishuDocument syncDocumentFromFeishu(String docId);

    /**
     * 批量同步文档
     */
    void batchSyncDocuments(List<Long> documentIds);

    /**
     * 获取飞书文档内容
     */
    String getFeishuDocumentContent(String docId);

    /**
     * 更新飞书文档内容
     */
    boolean updateFeishuDocumentContent(String docId, String content);

    /**
     * 获取飞书文档信息
     */
    Map<String, Object> getFeishuDocumentInfo(String docId);

    /**
     * 创建飞书文档
     */
    Map<String, Object> createFeishuDocument(String title, String content, FeishuDocument.DocumentType docType);

    /**
     * 获取飞书文档权限
     */
    Map<String, Object> getFeishuDocumentPermissions(String docId);

    /**
     * 设置飞书文档权限
     */
    boolean setFeishuDocumentPermissions(String docId, Map<String, Object> permissions);

    // ========== 文档操作 ==========

    /**
     * 增加文档查看次数
     */
    void incrementDocumentViewCount(Long id);

    /**
     * 增加文档查看次数并返回更新后的文档
     */
    FeishuDocument incrementViewCount(Long id);

    /**
     * 更新文档同步状态
     */
    void updateDocumentSyncStatus(Long id, FeishuDocument.SyncStatus syncStatus);

    /**
     * 检查文档是否需要同步
     */
    List<FeishuDocument> getDocumentsNeedingSync();

    /**
     * 自动同步文档
     */
    void autoSyncDocuments();

    // ========== 统计分析 ==========

    /**
     * 获取文档统计信息
     */
    Map<String, Object> getDocumentStatistics();

    /**
     * 获取用户文档统计
     */
    Map<String, Object> getUserDocumentStatistics(Long userId);

    /**
     * 获取学科文档统计
     */
    Map<String, Object> getSubjectDocumentStatistics();

    // ========== 导入导出 ==========

    /**
     * 导入飞书文档
     */
    FeishuDocument importFeishuDocument(String docUrl);

    /**
     * 批量导入飞书文档
     */
    List<FeishuDocument> batchImportFeishuDocuments(List<String> docUrls);

    /**
     * 导出文档配置
     */
    String exportDocumentConfig(List<Long> documentIds);

    /**
     * 导入文档配置
     */
    List<FeishuDocument> importDocumentConfig(String config);

    // ========== 权限管理 ==========

    /**
     * 检查用户是否有文档访问权限
     */
    boolean hasDocumentAccess(Long userId, Long documentId);

    /**
     * 获取用户可访问的文档
     */
    Page<FeishuDocument> getUserAccessibleDocuments(Long userId, Pageable pageable);

    /**
     * 设置文档访问权限
     */
    void setDocumentAccess(Long documentId, FeishuDocument.AccessLevel accessLevel);

    // ========== 文档协作 ==========

    /**
     * 获取文档协作者
     */
    List<Map<String, Object>> getDocumentCollaborators(String docId);

    /**
     * 添加文档协作者
     */
    boolean addDocumentCollaborator(String docId, String userEmail, String permission);

    /**
     * 移除文档协作者
     */
    boolean removeDocumentCollaborator(String docId, String userEmail);

    // ========== 飞书API集成 ==========

    /**
     * 从飞书API同步文档列表
     */
    List<FeishuDocument> syncDocumentsFromFeishuApi(String folderId, Integer pageSize);

    /**
     * 从飞书API获取文档内容
     */
    String getContentFromFeishuApi(String fileToken, String fileType);

    /**
     * 测试飞书API连接
     */
    Map<String, Object> testFeishuApiConnection();

    /**
     * 获取飞书个人空间文档列表
     */
    Map<String, Object> getFeishuPersonalDocuments(String folderId, String pageToken, Integer pageSize);

    /**
     * 根据docToken获取文档内容
     */
    String getDocumentContentByToken(String docToken);

    /**
     * 获取热门文档
     */
    List<FeishuDocument> getPopularDocuments(Integer limit);
}
