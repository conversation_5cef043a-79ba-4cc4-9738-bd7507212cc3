<template>
  <div class="feishu-api-manager">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>飞书API管理</h1>
      <p>管理飞书OpenAPI集成，获取个人空间文档列表</p>
    </div>

    <!-- API连接状态 -->
    <el-card class="connection-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>API连接状态</span>
          <el-button
            type="primary"
            :icon="Refresh"
            @click="testConnection"
            :loading="testing"
            size="small"
          >
            测试连接
          </el-button>
        </div>
      </template>

      <div class="connection-status">
        <div class="status-item">
          <el-icon :size="20" :color="connectionStatus.connected ? '#67C23A' : '#F56C6C'">
            <component :is="connectionStatus.connected ? 'SuccessFilled' : 'CircleCloseFilled'" />
          </el-icon>
          <span class="status-text">
            {{ connectionStatus.connected ? '已连接' : '未连接' }}
          </span>
        </div>

        <div v-if="connectionStatus.error" class="error-info">
          <el-alert
            :title="connectionStatus.error"
            type="error"
            show-icon
            :closable="false"
          />
        </div>

        <div v-if="connectionStatus.connected && connectionStatus.userInfo" class="user-info">
          <h4>用户信息</h4>
          <p><strong>姓名:</strong> {{ connectionStatus.userInfo.name }}</p>
          <p><strong>邮箱:</strong> {{ connectionStatus.userInfo.email }}</p>
        </div>

        <div v-if="configStatus" class="config-info">
          <h4>配置状态</h4>
          <div class="config-items">
            <div class="config-item">
              <el-icon :size="16" :color="configStatus.hasAppId ? '#67C23A' : '#F56C6C'">
                <component :is="configStatus.hasAppId ? 'SuccessFilled' : 'CircleCloseFilled'" />
              </el-icon>
              <span>App ID: {{ configStatus.hasAppId ? '已配置' : '未配置' }}</span>
            </div>
            <div class="config-item">
              <el-icon :size="16" :color="configStatus.hasAppSecret ? '#67C23A' : '#F56C6C'">
                <component :is="configStatus.hasAppSecret ? 'SuccessFilled' : 'CircleCloseFilled'" />
              </el-icon>
              <span>App Secret: {{ configStatus.hasAppSecret ? '已配置' : '未配置' }}</span>
            </div>
          </div>
          <div v-if="configStatus.message" class="config-message">
            <el-alert
              :title="configStatus.message"
              type="warning"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 个人空间文档列表 -->
    <el-card class="documents-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>个人空间文档</span>
          <div class="header-actions">
            <el-button
              type="success"
              :icon="Download"
              @click="loadPersonalDocuments"
              :loading="loadingDocuments"
              size="small"
            >
              获取文档列表
            </el-button>
            <el-button
              type="primary"
              :icon="Refresh"
              @click="syncDocuments"
              :loading="syncing"
              :disabled="!personalDocuments.length"
              size="small"
            >
              同步到本地
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索和筛选 -->
      <div class="search-filters">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchQuery"
              placeholder="搜索文档名称"
              :prefix-icon="Search"
              clearable
              @input="filterDocuments"
            />
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedType"
              placeholder="文件类型"
              clearable
              @change="filterDocuments"
            >
              <el-option label="全部" value="" />
              <el-option label="文档" value="doc" />
              <el-option label="表格" value="sheet" />
              <el-option label="演示文稿" value="slides" />
              <el-option label="多维表格" value="bitable" />
              <el-option label="思维笔记" value="mindnote" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="pageSize"
              placeholder="每页数量"
              type="number"
              :min="10"
              :max="200"
            />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="loadPersonalDocuments">
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 文档列表 -->
      <div class="documents-list">
        <div v-if="loadingDocuments" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>

        <div v-else-if="filteredDocuments.length === 0" class="empty-container">
          <el-empty description="暂无文档数据" />
        </div>

        <div v-else class="documents-table">
          <el-table
            :data="paginatedDocuments"
            stripe
            border
            style="width: 100%"
            :default-sort="{ prop: 'modifyTime', order: 'descending' }"
          >
            <el-table-column prop="name" label="文档名称" min-width="200">
              <template #default="{ row }">
                <div class="document-name">
                  <el-icon :size="16" :color="getTypeColor(row.type)">
                    <component :is="getTypeIcon(row.type)" />
                  </el-icon>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getTypeTagType(row.type)" size="small">
                  {{ getTypeText(row.type) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="size" label="大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.size) }}
              </template>
            </el-table-column>

            <el-table-column prop="modifyTime" label="修改时间" width="180" sortable>
              <template #default="{ row }">
                {{ formatDateTime(row.modifyTime) }}
              </template>
            </el-table-column>

            <el-table-column prop="creatorName" label="创建者" width="120" />

            <el-table-column label="文档Token" width="150" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="getDocTokenFromUrl(row.url)" class="doc-token-cell">
                  <el-tag size="small" type="info" class="doc-token-tag">
                    {{ getDocTokenFromUrl(row.url) }}
                  </el-tag>
                  <el-button
                    size="small"
                    text
                    type="primary"
                    @click="copyToClipboard(getDocTokenFromUrl(row.url))"
                    class="copy-btn"
                  >
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                </div>
                <span v-else class="text-muted">未提取</span>
              </template>
            </el-table-column>

            <el-table-column prop="url" label="文档URL" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div v-if="row.url" class="doc-url-cell">
                  <el-link :href="row.url" target="_blank" type="primary" class="doc-url-link">
                    {{ truncateUrl(row.url) }}
                  </el-link>
                  <el-button
                    size="small"
                    text
                    type="primary"
                    @click="copyToClipboard(row.url)"
                    class="copy-btn"
                  >
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                </div>
                <span v-else class="text-muted">未设置</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="previewDocument(row)"
                  :loading="previewingToken === row.token"
                >
                  预览
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="syncSingleDocument(row)"
                  :loading="syncingToken === row.token"
                >
                  同步
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="openOriginalDocument(row)"
                >
                  打开
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="filteredDocuments.length"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 文档预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文档预览"
      width="80%"
      :before-close="closePreviewDialog"
    >
      <div v-loading="loadingPreview" class="preview-content">
        <div v-if="previewContent" class="markdown-content" v-html="renderedPreviewContent"></div>
        <div v-else class="empty-preview">
          <el-empty description="无法获取文档内容" />
        </div>
      </div>
    </el-dialog>

    <!-- 同步结果对话框 -->
    <el-dialog
      v-model="syncResultDialogVisible"
      title="同步结果"
      width="60%"
    >
      <div class="sync-result">
        <el-result
          :icon="syncResult.success ? 'success' : 'error'"
          :title="syncResult.success ? '同步成功' : '同步失败'"
          :sub-title="syncResult.message"
        >
          <template #extra>
            <el-button type="primary" @click="syncResultDialogVisible = false">
              确定
            </el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Download, Search, SuccessFilled, CircleCloseFilled,
  Document, Grid, Monitor, Histogram, Connection, CopyDocument
} from '@element-plus/icons-vue'
import {
  testFeishuApiConnection,
  getFeishuPersonalDocuments,
  syncDocumentsFromFeishuApi,
  getContentFromFeishuApi,
  getFeishuApiConfigStatus
} from '@/api/feishu'
import { marked } from 'marked'

// 响应式数据
const testing = ref(false)
const loadingDocuments = ref(false)
const syncing = ref(false)
const loadingPreview = ref(false)
const previewingToken = ref('')
const syncingToken = ref('')

const connectionStatus = ref({
  connected: false,
  error: '',
  userInfo: null
})

const configStatus = ref(null)

const personalDocuments = ref([])
const searchQuery = ref('')
const selectedType = ref('')
const pageSize = ref(50)
const currentPage = ref(1)

const previewDialogVisible = ref(false)
const previewContent = ref('')
const currentPreviewDocument = ref(null)

const syncResultDialogVisible = ref(false)
const syncResult = ref({
  success: false,
  message: ''
})

// 计算属性
const filteredDocuments = computed(() => {
  let docs = personalDocuments.value

  if (searchQuery.value) {
    docs = docs.filter(doc =>
      doc.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (selectedType.value) {
    docs = docs.filter(doc => doc.type === selectedType.value)
  }

  return docs
})

const paginatedDocuments = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredDocuments.value.slice(start, end)
})

const renderedPreviewContent = computed(() => {
  if (!previewContent.value) return ''

  try {
    marked.setOptions({
      breaks: true,
      gfm: true,
      headerIds: false,
      mangle: false
    })

    return marked(previewContent.value)
  } catch (err) {
    console.error('Markdown渲染失败:', err)
    return `<pre>${previewContent.value}</pre>`
  }
})

// 方法
const testConnection = async () => {
  testing.value = true
  connectionStatus.value = { connected: false, error: '', userInfo: null }

  try {
    console.log('测试飞书API连接')
    const response = await testFeishuApiConnection()

    if (response && response.data) {
      connectionStatus.value = {
        connected: response.data.connected,
        error: response.data.error || '',
        userInfo: response.data.userInfo || null
      }

      if (response.data.connected) {
        ElMessage.success('飞书API连接成功')
      } else {
        ElMessage.error('飞书API连接失败: ' + (response.data.error || '未知错误'))
      }
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    connectionStatus.value = {
      connected: false,
      error: error.message || '连接测试失败',
      userInfo: null
    }
    ElMessage.error('连接测试失败: ' + error.message)
  } finally {
    testing.value = false
  }
}

const loadPersonalDocuments = async () => {
  loadingDocuments.value = true

  try {
    console.log('获取飞书个人空间文档列表')
    const response = await getFeishuPersonalDocuments({
      pageSize: pageSize.value
    })

    if (response && response.data && response.data.files) {
      personalDocuments.value = response.data.files
      ElMessage.success(`成功获取 ${response.data.files.length} 个文档`)
    } else {
      personalDocuments.value = []
      ElMessage.warning('未获取到文档数据')
    }
  } catch (error) {
    console.error('获取文档列表失败:', error)
    ElMessage.error('获取文档列表失败: ' + error.message)
    personalDocuments.value = []
  } finally {
    loadingDocuments.value = false
  }
}

const syncDocuments = async () => {
  if (personalDocuments.value.length === 0) {
    ElMessage.warning('没有可同步的文档')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要同步 ${personalDocuments.value.length} 个文档到本地数据库吗？`,
      '确认同步',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    syncing.value = true

    const response = await syncDocumentsFromFeishuApi({
      pageSize: pageSize.value
    })

    if (response && response.data) {
      syncResult.value = {
        success: response.data.success,
        message: response.data.success
          ? `成功同步 ${response.data.syncedCount} 个文档`
          : response.data.error || '同步失败'
      }
      syncResultDialogVisible.value = true

      if (response.data.success) {
        ElMessage.success(syncResult.value.message)
      } else {
        ElMessage.error(syncResult.value.message)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('同步文档失败:', error)
      ElMessage.error('同步文档失败: ' + error.message)
    }
  } finally {
    syncing.value = false
  }
}

const previewDocument = async (document) => {
  previewingToken.value = document.token
  previewContent.value = ''
  currentPreviewDocument.value = document

  try {
    console.log('预览文档:', document.name)
    const response = await getContentFromFeishuApi(document.token, document.type)

    if (response && response.data && response.data.content) {
      previewContent.value = response.data.content
      previewDialogVisible.value = true
    } else {
      ElMessage.warning('无法获取文档内容')
    }
  } catch (error) {
    console.error('预览文档失败:', error)
    ElMessage.error('预览文档失败: ' + error.message)
  } finally {
    previewingToken.value = ''
  }
}

const syncSingleDocument = async (document) => {
  syncingToken.value = document.token

  try {
    console.log('同步单个文档:', document.name)
    const response = await syncDocumentsFromFeishuApi({
      folderId: document.parentToken,
      pageSize: 1
    })

    if (response && response.data && response.data.success) {
      ElMessage.success(`文档 "${document.name}" 同步成功`)
    } else {
      ElMessage.error('同步失败: ' + (response.data?.error || '未知错误'))
    }
  } catch (error) {
    console.error('同步单个文档失败:', error)
    ElMessage.error('同步失败: ' + error.message)
  } finally {
    syncingToken.value = ''
  }
}

const openOriginalDocument = (document) => {
  if (document.url) {
    window.open(document.url, '_blank')
  } else {
    ElMessage.warning('文档链接不可用')
  }
}

const filterDocuments = () => {
  currentPage.value = 1
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const closePreviewDialog = () => {
  previewDialogVisible.value = false
  previewContent.value = ''
  currentPreviewDocument.value = null
}

// 工具方法
const getTypeIcon = (type) => {
  const icons = {
    doc: Document,
    docx: Document,
    sheet: Grid,
    slides: Monitor,
    bitable: Histogram,
    mindnote: Connection
  }
  return icons[type] || Document
}

const getTypeColor = (type) => {
  const colors = {
    doc: '#409EFF',
    docx: '#409EFF',
    sheet: '#67C23A',
    slides: '#E6A23C',
    bitable: '#F56C6C',
    mindnote: '#909399'
  }
  return colors[type] || '#409EFF'
}

const getTypeTagType = (type) => {
  const types = {
    doc: 'primary',
    docx: 'primary',
    sheet: 'success',
    slides: 'warning',
    bitable: 'danger',
    mindnote: 'info'
  }
  return types[type] || 'primary'
}

const getTypeText = (type) => {
  const texts = {
    doc: '文档',
    docx: '文档',
    sheet: '表格',
    slides: '演示',
    bitable: '多维表格',
    mindnote: '思维笔记'
  }
  return texts[type] || type
}

const formatFileSize = (size) => {
  if (!size) return '-'

  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(1)} ${units[index]}`
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// URL处理方法
const truncateUrl = (url) => {
  if (!url) return ''
  if (url.length <= 50) return url
  return url.substring(0, 47) + '...'
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级处理：创建临时文本域
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('内容已复制到剪贴板')
    } catch (fallbackError) {
      ElMessage.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

// 从URL中提取docToken
const getDocTokenFromUrl = (url) => {
  if (!url || url.trim() === '') {
    return ''
  }

  try {
    // 移除查询参数和锚点
    const cleanUrl = url.split('?')[0].split('#')[0]

    // 提取最后一段作为token
    const parts = cleanUrl.split('/')
    if (parts.length > 0) {
      const lastPart = parts[parts.length - 1]
      // 验证token格式
      if (isValidDocToken(lastPart)) {
        return lastPart
      }
    }
  } catch (error) {
    console.warn('提取docToken失败:', error)
  }

  return ''
}

// 验证docToken格式
const isValidDocToken = (token) => {
  if (!token || token.length < 10) {
    return false
  }

  // 飞书文档token通常以特定前缀开头
  return token.startsWith('doccn') ||  // 文档
         token.startsWith('shtcn') ||  // 表格
         token.startsWith('sldcn') ||  // 演示文稿
         token.startsWith('bmncn') ||  // 思维笔记
         token.startsWith('bascn') ||  // 多维表格
         token.startsWith('wikicn') || // 知识库
         token.startsWith('fldcn')     // 文件夹
}

const loadConfigStatus = async () => {
  try {
    console.log('加载飞书API配置状态')
    const response = await getFeishuApiConfigStatus()

    if (response && response.data) {
      configStatus.value = response.data
      console.log('配置状态:', response.data)
    }
  } catch (error) {
    console.error('加载配置状态失败:', error)
    configStatus.value = {
      hasAppId: false,
      hasAppSecret: false,
      configured: false,
      message: '无法获取配置状态'
    }
  }
}

// 生命周期
onMounted(() => {
  loadConfigStatus()
  testConnection()
})
</script>

<style scoped>
.feishu-api-manager {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.connection-card,
.documents-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.connection-status {
  padding: 10px 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
}

.error-info {
  margin: 15px 0;
}

.user-info {
  margin-top: 15px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

.user-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.user-info p {
  margin: 5px 0;
  color: #606266;
}

.config-info {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.config-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.config-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.config-message {
  margin-top: 10px;
}

.search-filters {
  margin-bottom: 20px;
}

.loading-container,
.empty-container {
  padding: 40px;
  text-align: center;
}

.documents-table {
  margin-top: 20px;
}

.document-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.preview-content {
  max-height: 600px;
  overflow-y: auto;
  padding: 20px;
}

.markdown-content {
  line-height: 1.6;
  color: #333;
}

.markdown-content :deep(h1) {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #eee;
  color: #2c3e50;
}

.markdown-content :deep(h2) {
  font-size: 20px;
  font-weight: 600;
  margin: 24px 0 12px 0;
  color: #34495e;
}

.markdown-content :deep(h3) {
  font-size: 18px;
  font-weight: 600;
  margin: 20px 0 10px 0;
  color: #34495e;
}

.markdown-content :deep(p) {
  margin: 0 0 12px 0;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0 0 12px 0;
  padding-left: 20px;
}

.markdown-content :deep(li) {
  margin: 4px 0;
}

.markdown-content :deep(blockquote) {
  margin: 12px 0;
  padding: 8px 12px;
  background: #f8f9fa;
  border-left: 4px solid #409eff;
  color: #666;
}

.markdown-content :deep(code) {
  background: #f1f2f3;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.markdown-content :deep(pre) {
  background: #f6f8fa;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid #e1e4e8;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
  font-size: 13px;
  line-height: 1.45;
}

.empty-preview {
  padding: 40px;
  text-align: center;
}

.sync-result {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feishu-api-manager {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .search-filters .el-row {
    flex-direction: column;
  }

  .search-filters .el-col {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* 文档URL相关样式 */
.doc-url-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.doc-url-link {
  flex: 1;
  min-width: 0;
  text-decoration: none;
}

.copy-btn {
  flex-shrink: 0;
  padding: 4px;
  min-height: auto;
}

.copy-btn:hover {
  background-color: var(--el-color-primary-light-9);
}

.text-muted {
  color: #909399;
}

/* 文档Token相关样式 */
.doc-token-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.doc-token-tag {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
