package com.lait.service;

import com.lait.dto.UserDTO;
import com.lait.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 创建用户
     */
    UserDTO createUser(UserDTO.CreateUserRequest request);

    /**
     * 根据ID获取用户
     */
    UserDTO getUserById(Long id);

    /**
     * 根据用户名获取用户
     */
    UserDTO getUserByUsername(String username);

    /**
     * 更新用户信息
     */
    UserDTO updateUser(Long id, UserDTO.UpdateUserRequest request);

    /**
     * 删除用户
     */
    void deleteUser(Long id);

    /**
     * 修改密码
     */
    void changePassword(Long id, UserDTO.ChangePasswordRequest request);

    /**
     * 分页查询用户
     */
    Page<UserDTO> getUsers(Pageable pageable);

    /**
     * 根据角色分页查询用户
     */
    Page<UserDTO> getUsersByRole(User.UserRole role, Pageable pageable);

    /**
     * 搜索用户
     */
    Page<UserDTO> searchUsers(String keyword, Pageable pageable);

    /**
     * 根据年级获取学生
     */
    List<UserDTO> getStudentsByGrade(Integer gradeLevel);

    /**
     * 根据班级获取学生
     */
    List<UserDTO> getStudentsByClass(String className);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 激活用户
     */
    void activateUser(Long id);

    /**
     * 停用用户
     */
    void deactivateUser(Long id);

    /**
     * 批量删除用户
     */
    void batchDeleteUsers(List<Long> userIds);

    /**
     * 批量更新用户状态
     */
    void batchUpdateUserStatus(List<Long> userIds, User.UserStatus status);

    /**
     * 重置用户密码
     */
    String resetUserPassword(Long id);

    /**
     * 获取用户统计信息
     */
    Map<String, Object> getUserStatistics();

    /**
     * 获取用户学习统计
     */
    Map<String, Object> getUserStudyStatistics(Long id);

    /**
     * 导出用户数据
     */
    String exportUsers(List<Long> userIds, String format);

    /**
     * 导入用户数据
     */
    Map<String, Object> importUsers(String data, String format);
}
