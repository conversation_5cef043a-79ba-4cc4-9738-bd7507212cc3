package com.lait.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 考试实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "exams")
public class Exam extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "考试名称不能为空")
    @Column(nullable = false)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @NotNull(message = "学科ID不能为空")
    @Column(name = "subject_id", nullable = false)
    private Long subjectId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subject_id", insertable = false, updatable = false)
    private Subject subject;

    @Column(name = "creator_id", nullable = false)
    private Long creatorId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id", insertable = false, updatable = false)
    private User creator;

    @Enumerated(EnumType.STRING)
    @Column(name = "exam_type", nullable = false)
    private ExamType examType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ExamStatus status = ExamStatus.DRAFT;

    @Column(name = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime startTime;

    @Column(name = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime endTime;

    @Column(name = "duration_minutes")
    private Integer durationMinutes;

    @Column(name = "total_points")
    private Integer totalPoints = 0;

    @Column(name = "pass_score")
    private Integer passScore;

    @Column(name = "question_count")
    private Integer questionCount = 0;

    @Column(name = "participant_count")
    private Integer participantCount = 0;

    @Column(name = "max_attempts")
    private Integer maxAttempts = 1;

    @Column(name = "shuffle_questions")
    private Boolean shuffleQuestions = false;

    @Column(name = "shuffle_options")
    private Boolean shuffleOptions = false;

    @Column(name = "show_result_immediately")
    private Boolean showResultImmediately = true;

    @Column(name = "allow_review")
    private Boolean allowReview = true;

    @Column(columnDefinition = "TEXT")
    private String instructions;

    @Column(name = "grade_level")
    private Integer gradeLevel;

    @Column(name = "class_name")
    private String className;

    @Column(columnDefinition = "JSON")
    private String settings;

    /**
     * 考试类型枚举
     */
    public enum ExamType {
        PRACTICE("练习"),
        QUIZ("小测"),
        MIDTERM("期中考试"),
        FINAL("期末考试"),
        MOCK("模拟考试"),
        HOMEWORK("作业");

        private final String description;

        ExamType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 考试状态枚举
     */
    public enum ExamStatus {
        DRAFT("草稿"),
        PUBLISHED("已发布"),
        ONGOING("进行中"),
        ENDED("已结束"),
        CANCELLED("已取消"),
        TEMPLATE("模板");

        private final String description;

        ExamStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
