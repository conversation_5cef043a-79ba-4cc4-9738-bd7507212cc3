package com.lait.service;

import com.lait.dto.FeishuFileDto;
import com.lait.dto.FeishuTokenDto;
import com.lait.dto.FeishuUserTokenDto;
import com.lait.dto.FeishuUserInfoDto;

import java.util.List;
import java.util.Map;

/**
 * 飞书API服务接口
 */
public interface FeishuApiService {

    /**
     * 获取租户访问令牌
     */
    FeishuTokenDto getTenantAccessToken();

    /**
     * 获取应用访问令牌
     */
    FeishuTokenDto getAppAccessToken();

    /**
     * 生成OAuth授权URL
     *
     * @param state 状态参数，用于防止CSRF攻击
     * @return 授权URL
     */
    String generateAuthUrl(String state);

    /**
     * 通过授权码获取用户访问令牌
     *
     * @param code 授权码
     * @return 用户访问令牌
     */
    FeishuUserTokenDto getUserAccessToken(String code);

    /**
     * 刷新用户访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的用户访问令牌
     */
    FeishuUserTokenDto refreshUserAccessToken(String refreshToken);

    /**
     * 获取用户信息
     *
     * @param userAccessToken 用户访问令牌
     * @return 用户信息
     */
    FeishuUserInfoDto getUserInfo(String userAccessToken);

    /**
     * 获取个人空间文件列表
     *
     * @param folderId 文件夹ID，为空时获取根目录
     * @param pageToken 分页标记
     * @param pageSize 每页大小
     * @return 文件列表
     */
    List<FeishuFileDto> getFileList(String folderId, String pageToken, Integer pageSize);

    /**
     * 获取文件内容
     *
     * @param fileToken 文件token
     * @param fileType 文件类型
     * @return 文件内容
     */
    String getFileContent(String fileToken, String fileType);

    /**
     * 获取文件元信息
     *
     * @param fileTokens 文件token列表
     * @return 文件元信息
     */
    Map<String, Object> getFileMetadata(List<String> fileTokens);

    /**
     * 搜索文件
     *
     * @param query 搜索关键词
     * @param pageToken 分页标记
     * @param pageSize 每页大小
     * @return 搜索结果
     */
    List<FeishuFileDto> searchFiles(String query, String pageToken, Integer pageSize);

    /**
     * 测试API连接
     *
     * @return 连接状态
     */
    boolean testConnection();

    /**
     * 根据docToken获取文档内容
     *
     * @param docToken 文档token
     * @return 文档内容
     */
    String getDocumentContentByToken(String docToken);
}
