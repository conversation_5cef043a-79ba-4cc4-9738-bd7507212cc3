package com.lait.controller;

import com.lait.entity.PointsConfig;
import com.lait.entity.UserPoints;
import com.lait.service.PointsService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 积分管理控制器
 */
@RestController
@RequestMapping("/admin/points")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class PointsController {

    private final PointsService pointsService;

    // ========== 积分配置管理 ==========

    /**
     * 创建积分配置
     */
    @PostMapping("/config")
    public ResponseEntity<PointsConfig> createPointsConfig(@Valid @RequestBody PointsConfig config) {
        PointsConfig createdConfig = pointsService.createPointsConfig(config);
        return ResponseEntity.ok(createdConfig);
    }

    /**
     * 更新积分配置
     */
    @PutMapping("/config/{id}")
    public ResponseEntity<PointsConfig> updatePointsConfig(@PathVariable Long id,
                                                          @Valid @RequestBody PointsConfig config) {
        PointsConfig updatedConfig = pointsService.updatePointsConfig(id, config);
        return ResponseEntity.ok(updatedConfig);
    }

    /**
     * 删除积分配置
     */
    @DeleteMapping("/config/{id}")
    public ResponseEntity<Void> deletePointsConfig(@PathVariable Long id) {
        pointsService.deletePointsConfig(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取积分配置详情
     */
    @GetMapping("/config/{id}")
    public ResponseEntity<PointsConfig> getPointsConfig(@PathVariable Long id) {
        PointsConfig config = pointsService.getPointsConfigById(id);
        return ResponseEntity.ok(config);
    }

    /**
     * 分页查询积分配置
     */
    @GetMapping("/config")
    public ResponseEntity<Page<PointsConfig>> getPointsConfigs(
            @RequestParam(required = false) String displayName,
            @RequestParam(required = false) PointsConfig.ConfigCategory category,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<PointsConfig> configs = pointsService.getPointsConfigs(displayName, category, enabled, pageable);
        return ResponseEntity.ok(configs);
    }

    /**
     * 获取启用的积分配置
     */
    @GetMapping("/config/enabled")
    public ResponseEntity<List<PointsConfig>> getEnabledPointsConfigs() {
        List<PointsConfig> configs = pointsService.getEnabledPointsConfigs();
        return ResponseEntity.ok(configs);
    }

    /**
     * 根据分类获取积分配置
     */
    @GetMapping("/config/category/{category}")
    public ResponseEntity<List<PointsConfig>> getPointsConfigsByCategory(
            @PathVariable PointsConfig.ConfigCategory category) {
        List<PointsConfig> configs = pointsService.getPointsConfigsByCategory(category);
        return ResponseEntity.ok(configs);
    }

    /**
     * 批量更新积分配置状态
     */
    @PutMapping("/config/batch-update-status")
    public ResponseEntity<Void> batchUpdatePointsConfigStatus(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        Boolean enabled = (Boolean) request.get("enabled");
        pointsService.batchUpdatePointsConfigStatus(ids, enabled);
        return ResponseEntity.ok().build();
    }

    /**
     * 初始化默认积分配置
     */
    @PostMapping("/config/initialize")
    public ResponseEntity<Void> initializeDefaultPointsConfig() {
        pointsService.initializeDefaultPointsConfig();
        return ResponseEntity.ok().build();
    }

    // ========== 用户积分管理 ==========

    /**
     * 给用户添加积分
     */
    @PostMapping("/users/{userId}/add")
    public ResponseEntity<UserPoints> addUserPoints(@PathVariable Long userId,
                                                   @RequestBody Map<String, Object> request) {
        String configKey = (String) request.get("configKey");
        Long relatedId = request.get("relatedId") != null ? ((Number) request.get("relatedId")).longValue() : null;
        String relatedType = (String) request.get("relatedType");
        String description = (String) request.get("description");

        UserPoints userPoints = pointsService.addPoints(userId, configKey, relatedId, relatedType, description);
        return ResponseEntity.ok(userPoints);
    }

    /**
     * 扣除用户积分
     */
    @PostMapping("/users/{userId}/deduct")
    public ResponseEntity<UserPoints> deductUserPoints(@PathVariable Long userId,
                                                      @RequestBody Map<String, Object> request) {
        Integer points = (Integer) request.get("points");
        String source = (String) request.get("source");
        Long relatedId = request.get("relatedId") != null ? ((Number) request.get("relatedId")).longValue() : null;
        String relatedType = (String) request.get("relatedType");
        String description = (String) request.get("description");

        UserPoints userPoints = pointsService.deductPoints(userId, points, source, relatedId, relatedType, description);
        return ResponseEntity.ok(userPoints);
    }

    /**
     * 获取用户当前积分
     */
    @GetMapping("/users/{userId}/current")
    public ResponseEntity<Map<String, Object>> getUserCurrentPoints(@PathVariable Long userId) {
        Integer points = pointsService.getUserCurrentPoints(userId);
        Map<String, Object> result = new HashMap<>();
        result.put("userId", userId);
        result.put("currentPoints", points);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取用户积分记录
     */
    @GetMapping("/users/{userId}/history")
    public ResponseEntity<Page<UserPoints>> getUserPointsHistory(@PathVariable Long userId,
                                                               @RequestParam(defaultValue = "0") int page,
                                                               @RequestParam(defaultValue = "20") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<UserPoints> history = pointsService.getUserPointsHistory(userId, pageable);
        return ResponseEntity.ok(history);
    }

    /**
     * 分页查询积分记录
     */
    @GetMapping("/records")
    public ResponseEntity<Page<UserPoints>> getPointsRecords(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) UserPoints.PointsType pointsType,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<UserPoints> records = pointsService.getPointsRecords(userId, pointsType, source, startTime, endTime, pageable);
        return ResponseEntity.ok(records);
    }

    /**
     * 获取用户积分趋势
     */
    @GetMapping("/users/{userId}/trend")
    public ResponseEntity<List<Map<String, Object>>> getUserPointsTrend(@PathVariable Long userId,
                                                                       @RequestParam(defaultValue = "30") int days) {
        List<Map<String, Object>> trend = pointsService.getUserPointsTrend(userId, days);
        return ResponseEntity.ok(trend);
    }

    /**
     * 获取积分排行榜
     */
    @GetMapping("/ranking")
    public ResponseEntity<List<Map<String, Object>>> getPointsRanking(
            @RequestParam(defaultValue = "10") int limit) {
        List<Map<String, Object>> ranking = pointsService.getPointsRanking(limit);
        return ResponseEntity.ok(ranking);
    }

    /**
     * 重置用户积分
     */
    @PostMapping("/users/{userId}/reset")
    public ResponseEntity<Void> resetUserPoints(@PathVariable Long userId,
                                               @RequestBody Map<String, String> request) {
        String reason = request.get("reason");
        pointsService.resetUserPoints(userId, reason);
        return ResponseEntity.ok().build();
    }

    /**
     * 批量调整用户积分
     */
    @PostMapping("/users/batch-adjust")
    public ResponseEntity<Void> batchAdjustUserPoints(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> userIds = (List<Long>) request.get("userIds");
        Integer points = (Integer) request.get("points");
        String reason = (String) request.get("reason");

        pointsService.batchAdjustUserPoints(userIds, points, reason);
        return ResponseEntity.ok().build();
    }

    // ========== 统计分析 ==========

    /**
     * 获取积分配置统计
     */
    @GetMapping("/statistics/config")
    public ResponseEntity<Map<String, Object>> getPointsConfigStatistics() {
        Map<String, Object> stats = pointsService.getPointsConfigStatistics();
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取积分记录统计
     */
    @GetMapping("/statistics/records")
    public ResponseEntity<Map<String, Object>> getPointsRecordStatistics() {
        Map<String, Object> stats = pointsService.getPointsRecordStatistics();
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取用户积分统计
     */
    @GetMapping("/statistics/users/{userId}")
    public ResponseEntity<Map<String, Object>> getUserPointsStatistics(@PathVariable Long userId) {
        Map<String, Object> stats = pointsService.getUserPointsStatistics(userId);
        return ResponseEntity.ok(stats);
    }
}
