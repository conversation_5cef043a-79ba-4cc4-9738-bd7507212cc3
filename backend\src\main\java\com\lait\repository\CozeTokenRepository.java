package com.lait.repository;

import com.lait.entity.CozeToken;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Coze Token数据访问层
 */
@Repository
public interface CozeTokenRepository extends JpaRepository<CozeToken, Long> {

    /**
     * 根据名称查找Token
     */
    Optional<CozeToken> findByName(String name);

    /**
     * 根据Token值查找
     */
    Optional<CozeToken> findByToken(String token);

    /**
     * 根据应用ID查找Token列表
     */
    List<CozeToken> findByAppId(String appId);

    /**
     * 查找默认Token
     */
    Optional<CozeToken> findByIsDefaultTrue();

    /**
     * 根据状态查找Token列表
     */
    List<CozeToken> findByStatus(CozeToken.TokenStatus status);

    /**
     * 根据Token类型查找
     */
    List<CozeToken> findByTokenType(CozeToken.TokenType tokenType);

    /**
     * 查找激活状态的Token
     */
    @Query("SELECT t FROM CozeToken t WHERE t.status = 'ACTIVE' AND (t.expiresAt IS NULL OR t.expiresAt > :now)")
    List<CozeToken> findActiveTokens(@Param("now") LocalDateTime now);

    /**
     * 查找即将过期的Token
     */
    @Query("SELECT t FROM CozeToken t WHERE t.status = 'ACTIVE' AND t.expiresAt IS NOT NULL AND t.expiresAt BETWEEN :now AND :threshold")
    List<CozeToken> findExpiringTokens(@Param("now") LocalDateTime now, @Param("threshold") LocalDateTime threshold);

    /**
     * 分页查询Token
     */
    @Query("SELECT t FROM CozeToken t WHERE " +
           "(:name IS NULL OR t.name LIKE %:name%) AND " +
           "(:tokenType IS NULL OR t.tokenType = :tokenType) AND " +
           "(:status IS NULL OR t.status = :status)")
    Page<CozeToken> findTokensWithFilters(@Param("name") String name,
                                         @Param("tokenType") CozeToken.TokenType tokenType,
                                         @Param("status") CozeToken.TokenStatus status,
                                         Pageable pageable);

    /**
     * 统计各状态Token数量
     */
    @Query("SELECT t.status, COUNT(t) FROM CozeToken t GROUP BY t.status")
    List<Object[]> countByStatus();

    /**
     * 统计各类型Token数量
     */
    @Query("SELECT t.tokenType, COUNT(t) FROM CozeToken t GROUP BY t.tokenType")
    List<Object[]> countByTokenType();

    /**
     * 更新Token使用信息
     */
    @Query("UPDATE CozeToken t SET t.lastUsedAt = :lastUsedAt, t.usageCount = t.usageCount + 1 WHERE t.id = :id")
    void updateUsageInfo(@Param("id") Long id, @Param("lastUsedAt") LocalDateTime lastUsedAt);

    /**
     * 检查Token名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查Token值是否存在
     */
    boolean existsByToken(String token);
}
