package com.lait.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户积分记录实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user_points")
public class UserPoints extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(nullable = false)
    private Long userId;

    /**
     * 用户信息
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", insertable = false, updatable = false)
    private User user;

    /**
     * 积分变化值 (正数为增加，负数为减少)
     */
    @Column(nullable = false)
    private Integer pointsChange;

    /**
     * 变化后的总积分
     */
    @Column(nullable = false)
    private Integer totalPoints;

    /**
     * 积分来源
     */
    @Column(nullable = false, length = 100)
    private String source;

    /**
     * 积分类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private PointsType pointsType;

    /**
     * 关联对象ID (如题目ID、笔记ID等)
     */
    private Long relatedId;

    /**
     * 关联对象类型
     */
    @Column(length = 50)
    private String relatedType;

    /**
     * 描述
     */
    @Column(length = 500)
    private String description;

    /**
     * 积分类型枚举
     */
    public enum PointsType {
        EARN("获得"),
        SPEND("消费"),
        PENALTY("扣除"),
        BONUS("奖励"),
        REFUND("退还");

        private final String description;

        PointsType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
