import request from './request'

// 获取用户列表
export function getUsers(params) {
  return request({
    url: '/users',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUser(id) {
  return request({
    url: `/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/users',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/users/${id}`,
    method: 'delete'
  })
}

// 批量删除用户
export function batchDeleteUsers(userIds) {
  return request({
    url: '/users/batch',
    method: 'delete',
    data: userIds
  })
}

// 批量更新用户状态
export function batchUpdateUserStatus(userIds, status) {
  return request({
    url: '/users/batch/status',
    method: 'put',
    data: { userIds, status }
  })
}

// 激活用户
export function activateUser(id) {
  return request({
    url: `/users/${id}/activate`,
    method: 'put'
  })
}

// 停用用户
export function deactivateUser(id) {
  return request({
    url: `/users/${id}/deactivate`,
    method: 'put'
  })
}

// 重置用户密码
export function resetUserPassword(id) {
  return request({
    url: `/users/${id}/reset-password`,
    method: 'put'
  })
}

// 检查用户名是否存在
export function checkUsernameExists(username) {
  return request({
    url: '/users/check-username',
    method: 'get',
    params: { username }
  })
}

// 检查邮箱是否存在
export function checkEmailExists(email) {
  return request({
    url: '/users/check-email',
    method: 'get',
    params: { email }
  })
}

// 获取用户统计信息
export function getUserStatistics() {
  return request({
    url: '/users/statistics',
    method: 'get'
  })
}

// 获取用户学习统计
export function getUserStudyStatistics(id) {
  return request({
    url: `/users/${id}/study-statistics`,
    method: 'get'
  })
}

// 导出用户数据
export function exportUsers(userIds, format = 'CSV') {
  return request({
    url: '/users/export',
    method: 'post',
    data: { userIds, format }
  })
}

// 导入用户数据
export function importUsers(data, format = 'CSV') {
  return request({
    url: '/users/import',
    method: 'post',
    data: { data, format }
  })
}
