package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.dto.WrongQuestionDTO;
import com.lait.entity.WrongQuestion;
import com.lait.service.WrongQuestionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 错题管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/wrong-questions")
@RequiredArgsConstructor
@Validated
public class WrongQuestionController {

    private final WrongQuestionService wrongQuestionService;

    /**
     * 创建错题记录
     */
    @PostMapping
    public ResponseEntity<ApiResponse<WrongQuestionDTO>> createWrongQuestion(
            @Valid @RequestBody WrongQuestionDTO.CreateWrongQuestionRequest request) {
        log.info("创建错题记录请求: 学生ID={}, 题目ID={}", request.getStudentId(), request.getQuestionId());
        WrongQuestionDTO wrongQuestionDTO = wrongQuestionService.createWrongQuestion(request);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestionDTO));
    }

    /**
     * 根据ID获取错题
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<WrongQuestionDTO>> getWrongQuestionById(@PathVariable Long id) {
        WrongQuestionDTO wrongQuestionDTO = wrongQuestionService.getWrongQuestionById(id);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestionDTO));
    }

    /**
     * 更新错题信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<WrongQuestionDTO>> updateWrongQuestion(
            @PathVariable Long id,
            @Valid @RequestBody WrongQuestionDTO.UpdateWrongQuestionRequest request) {
        log.info("更新错题记录请求: {}", id);
        WrongQuestionDTO wrongQuestionDTO = wrongQuestionService.updateWrongQuestion(id, request);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestionDTO));
    }

    /**
     * 删除错题记录
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteWrongQuestion(@PathVariable Long id) {
        log.info("删除错题记录请求: {}", id);
        wrongQuestionService.deleteWrongQuestion(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 分页查询错题
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Page<WrongQuestionDTO>>> getWrongQuestions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<WrongQuestionDTO> wrongQuestions = wrongQuestionService.getWrongQuestions(pageable);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestions));
    }

    /**
     * 根据学生ID分页查询错题
     */
    @GetMapping("/student/{studentId}")
    public ResponseEntity<ApiResponse<Page<WrongQuestionDTO>>> getWrongQuestionsByStudentId(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<WrongQuestionDTO> wrongQuestions = wrongQuestionService.getWrongQuestionsByStudentId(studentId, pageable);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestions));
    }

    /**
     * 搜索错题
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<Page<WrongQuestionDTO>>> searchWrongQuestions(
            @RequestBody WrongQuestionDTO.WrongQuestionQueryRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<WrongQuestionDTO> wrongQuestions = wrongQuestionService.searchWrongQuestions(request, pageable);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestions));
    }

    /**
     * 根据复习状态查询错题
     */
    @GetMapping("/student/{studentId}/status/{status}")
    public ResponseEntity<ApiResponse<Page<WrongQuestionDTO>>> getWrongQuestionsByStatus(
            @PathVariable Long studentId,
            @PathVariable WrongQuestion.ReviewStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<WrongQuestionDTO> wrongQuestions = wrongQuestionService.getWrongQuestionsByStatus(studentId, status, pageable);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestions));
    }

    /**
     * 获取待复习的错题
     */
    @GetMapping("/pending-review/student/{studentId}")
    public ResponseEntity<ApiResponse<List<WrongQuestionDTO>>> getPendingReviewQuestions(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<WrongQuestionDTO> wrongQuestions = wrongQuestionService.getPendingReviewQuestions(studentId, limit);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestions));
    }

    /**
     * 标记错题掌握状态
     */
    @PutMapping("/mark-mastered")
    public ResponseEntity<ApiResponse<Void>> markWrongQuestionMastered(
            @Valid @RequestBody WrongQuestionDTO.MarkMasteredRequest request) {
        log.info("标记错题掌握状态请求: {}", request.getWrongQuestionId());
        wrongQuestionService.markWrongQuestionMastered(request);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 批量标记错题掌握状态
     */
    @PutMapping("/batch-mark-mastered")
    public ResponseEntity<ApiResponse<Void>> batchMarkWrongQuestionsMastered(
            @Valid @RequestBody WrongQuestionDTO.BatchMarkMasteredRequest request) {
        log.info("批量标记错题掌握状态请求，数量: {}", request.getWrongQuestionIds().length);
        wrongQuestionService.batchMarkWrongQuestionsMastered(request);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 增加错误次数
     */
    @PostMapping("/increment-wrong")
    public ResponseEntity<ApiResponse<Void>> incrementWrongCount(
            @RequestParam Long studentId,
            @RequestParam Long questionId,
            @RequestParam(required = false) String studentAnswer) {
        log.info("增加错误次数请求: 学生ID={}, 题目ID={}", studentId, questionId);
        wrongQuestionService.incrementWrongCount(studentId, questionId, studentAnswer);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 获取学生错题统计
     */
    @GetMapping("/statistics/student/{studentId}")
    public ResponseEntity<ApiResponse<WrongQuestionDTO.WrongQuestionStatistics>> getStudentWrongQuestionStatistics(
            @PathVariable Long studentId) {
        
        WrongQuestionDTO.WrongQuestionStatistics statistics = wrongQuestionService.getStudentWrongQuestionStatistics(studentId);
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 获取学科错题统计
     */
    @GetMapping("/statistics/subject/student/{studentId}")
    public ResponseEntity<ApiResponse<List<WrongQuestionDTO.SubjectWrongStatistics>>> getSubjectWrongStatistics(
            @PathVariable Long studentId) {
        
        List<WrongQuestionDTO.SubjectWrongStatistics> statistics = wrongQuestionService.getSubjectWrongStatistics(studentId);
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 获取最常错的题目类型
     */
    @GetMapping("/most-wrong-types/student/{studentId}/subject/{subjectId}")
    public ResponseEntity<ApiResponse<List<String>>> getMostWrongQuestionTypes(
            @PathVariable Long studentId,
            @PathVariable Long subjectId) {
        
        List<String> questionTypes = wrongQuestionService.getMostWrongQuestionTypes(studentId, subjectId);
        return ResponseEntity.ok(ApiResponse.success(questionTypes));
    }

    /**
     * 获取复习建议
     */
    @GetMapping("/review-suggestions/student/{studentId}")
    public ResponseEntity<ApiResponse<List<WrongQuestionDTO>>> getReviewSuggestions(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<WrongQuestionDTO> suggestions = wrongQuestionService.getReviewSuggestions(studentId, limit);
        return ResponseEntity.ok(ApiResponse.success(suggestions));
    }

    /**
     * 重置错题状态
     */
    @PutMapping("/{id}/reset-status")
    public ResponseEntity<ApiResponse<Void>> resetWrongQuestionStatus(@PathVariable Long id) {
        log.info("重置错题状态请求: {}", id);
        wrongQuestionService.resetWrongQuestionStatus(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 批量删除错题记录
     */
    @DeleteMapping("/batch")
    public ResponseEntity<ApiResponse<Void>> batchDeleteWrongQuestions(
            @RequestBody Long[] wrongQuestionIds) {
        log.info("批量删除错题记录请求，数量: {}", wrongQuestionIds.length);
        wrongQuestionService.batchDeleteWrongQuestions(wrongQuestionIds);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 检查是否已存在错题记录
     */
    @GetMapping("/exists")
    public ResponseEntity<ApiResponse<Boolean>> existsWrongQuestion(
            @RequestParam Long studentId,
            @RequestParam Long questionId) {
        
        boolean exists = wrongQuestionService.existsWrongQuestion(studentId, questionId);
        return ResponseEntity.ok(ApiResponse.success(exists));
    }

    /**
     * 获取错题掌握率
     */
    @GetMapping("/mastery-rate/student/{studentId}/subject/{subjectId}")
    public ResponseEntity<ApiResponse<Double>> getWrongQuestionMasteryRate(
            @PathVariable Long studentId,
            @PathVariable Long subjectId) {
        
        Double masteryRate = wrongQuestionService.getWrongQuestionMasteryRate(studentId, subjectId);
        return ResponseEntity.ok(ApiResponse.success(masteryRate));
    }

    /**
     * 获取最近错题
     */
    @GetMapping("/recent/student/{studentId}")
    public ResponseEntity<ApiResponse<List<WrongQuestionDTO>>> getRecentWrongQuestions(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<WrongQuestionDTO> wrongQuestions = wrongQuestionService.getRecentWrongQuestions(studentId, limit);
        return ResponseEntity.ok(ApiResponse.success(wrongQuestions));
    }
}
