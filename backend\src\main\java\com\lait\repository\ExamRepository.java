package com.lait.repository;

import com.lait.entity.Exam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 考试数据访问层
 */
@Repository
public interface ExamRepository extends JpaRepository<Exam, Long> {

    /**
     * 根据学科ID查找考试
     */
    List<Exam> findBySubjectIdOrderByCreatedTimeDesc(Long subjectId);

    /**
     * 根据创建者ID查找考试
     */
    List<Exam> findByCreatorIdOrderByCreatedTimeDesc(Long creatorId);

    /**
     * 根据考试类型查找
     */
    List<Exam> findByExamType(Exam.ExamType examType);

    /**
     * 根据状态查找考试
     */
    List<Exam> findByStatus(Exam.ExamStatus status);

    /**
     * 查找正在进行的考试
     */
    @Query("SELECT e FROM Exam e WHERE e.status = 'ONGOING' AND e.startTime <= :now AND e.endTime >= :now")
    List<Exam> findOngoingExams(@Param("now") LocalDateTime now);

    /**
     * 查找即将开始的考试
     */
    @Query("SELECT e FROM Exam e WHERE e.status = 'PUBLISHED' AND e.startTime > :now AND e.startTime <= :threshold")
    List<Exam> findUpcomingExams(@Param("now") LocalDateTime now, @Param("threshold") LocalDateTime threshold);

    /**
     * 分页查询考试
     */
    @Query("SELECT e FROM Exam e WHERE " +
           "(:title IS NULL OR e.title LIKE %:title%) AND " +
           "(:subjectId IS NULL OR e.subjectId = :subjectId) AND " +
           "(:examType IS NULL OR e.examType = :examType) AND " +
           "(:status IS NULL OR e.status = :status) AND " +
           "(:creatorId IS NULL OR e.creatorId = :creatorId) " +
           "ORDER BY e.createdTime DESC")
    Page<Exam> findExamsWithFilters(@Param("title") String title,
                                   @Param("subjectId") Long subjectId,
                                   @Param("examType") Exam.ExamType examType,
                                   @Param("status") Exam.ExamStatus status,
                                   @Param("creatorId") Long creatorId,
                                   Pageable pageable);

    /**
     * 统计各状态考试数量
     */
    @Query("SELECT e.status, COUNT(e) FROM Exam e GROUP BY e.status")
    List<Object[]> countByStatus();

    /**
     * 统计各类型考试数量
     */
    @Query("SELECT e.examType, COUNT(e) FROM Exam e GROUP BY e.examType")
    List<Object[]> countByExamType();

    /**
     * 统计各学科考试数量
     */
    @Query("SELECT s.name, COUNT(e) FROM Exam e LEFT JOIN e.subject s GROUP BY s.name")
    List<Object[]> countBySubject();

    /**
     * 查找需要自动结束的考试
     */
    @Query("SELECT e FROM Exam e WHERE e.status = 'ONGOING' AND e.endTime < :now")
    List<Exam> findExamsToEnd(@Param("now") LocalDateTime now);

    /**
     * 查找需要自动开始的考试
     */
    @Query("SELECT e FROM Exam e WHERE e.status = 'PUBLISHED' AND e.startTime <= :now")
    List<Exam> findExamsToStart(@Param("now") LocalDateTime now);

    /**
     * 根据年级和班级查找考试
     */
    @Query("SELECT e FROM Exam e WHERE " +
           "(:gradeLevel IS NULL OR e.gradeLevel = :gradeLevel) AND " +
           "(:className IS NULL OR e.className = :className OR e.className IS NULL) " +
           "ORDER BY e.createdTime DESC")
    List<Exam> findByGradeLevelAndClassName(@Param("gradeLevel") Integer gradeLevel,
                                           @Param("className") String className);

    /**
     * 查找热门考试（参与人数最多）
     */
    @Query("SELECT e FROM Exam e WHERE e.status = 'PUBLISHED' OR e.status = 'ENDED' " +
           "ORDER BY e.participantCount DESC")
    List<Exam> findPopularExams(Pageable pageable);

    /**
     * 查找最近创建的考试
     */
    @Query("SELECT e FROM Exam e ORDER BY e.createdTime DESC")
    List<Exam> findRecentExams(Pageable pageable);
}
