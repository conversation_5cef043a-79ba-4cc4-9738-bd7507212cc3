package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.entity.CozeToken;
import com.lait.service.CozeTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Coze Token管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/coze-tokens")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class CozeTokenController {

    private final CozeTokenService cozeTokenService;

    /**
     * 创建Token
     */
    @PostMapping
    public ResponseEntity<ApiResponse<CozeToken>> createToken(@Valid @RequestBody CozeToken token) {
        try {
            CozeToken createdToken = cozeTokenService.createToken(token);
            return ResponseEntity.ok(ApiResponse.success(createdToken));
        } catch (Exception e) {
            log.error("创建Token失败", e);
            return ResponseEntity.ok(ApiResponse.error("创建Token失败: " + e.getMessage()));
        }
    }

    /**
     * 更新Token
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<CozeToken>> updateToken(@PathVariable Long id,
                                               @Valid @RequestBody CozeToken token) {
        try {
            CozeToken updatedToken = cozeTokenService.updateToken(id, token);
            return ResponseEntity.ok(ApiResponse.success(updatedToken));
        } catch (Exception e) {
            log.error("更新Token失败", e);
            return ResponseEntity.ok(ApiResponse.error("更新Token失败: " + e.getMessage()));
        }
    }

    /**
     * 删除Token
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteToken(@PathVariable Long id) {
        try {
            cozeTokenService.deleteToken(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("删除Token失败", e);
            return ResponseEntity.ok(ApiResponse.error("删除Token失败: " + e.getMessage()));
        }
    }

    /**
     * 获取Token详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<CozeToken>> getToken(@PathVariable Long id) {
        try {
            CozeToken token = cozeTokenService.getTokenById(id);
            return ResponseEntity.ok(ApiResponse.success(token));
        } catch (Exception e) {
            log.error("获取Token详情失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取Token详情失败: " + e.getMessage()));
        }
    }

    /**
     * 分页查询Token
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<CozeToken>>> getTokens(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) CozeToken.TokenType tokenType,
            @RequestParam(required = false) CozeToken.TokenStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<CozeToken> tokens = cozeTokenService.getTokens(name, tokenType, status, pageable);
            return ResponseEntity.ok(ApiResponse.success(tokens));
        } catch (Exception e) {
            log.error("获取Token列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取Token列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有激活的Token
     */
    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<CozeToken>>> getActiveTokens() {
        try {
            List<CozeToken> tokens = cozeTokenService.getActiveTokens();
            return ResponseEntity.ok(ApiResponse.success(tokens));
        } catch (Exception e) {
            log.error("获取激活Token列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取激活Token列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取默认Token
     */
    @GetMapping("/default")
    public ResponseEntity<ApiResponse<CozeToken>> getDefaultToken() {
        try {
            CozeToken token = cozeTokenService.getDefaultToken();
            return ResponseEntity.ok(ApiResponse.success(token));
        } catch (Exception e) {
            log.error("获取默认Token失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取默认Token失败: " + e.getMessage()));
        }
    }

    /**
     * 设置默认Token
     */
    @PutMapping("/{id}/set-default")
    public ResponseEntity<Void> setDefaultToken(@PathVariable Long id) {
        cozeTokenService.setDefaultToken(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 验证Token
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateToken(@RequestBody Map<String, String> request) {
        String token = request.get("token");
        boolean isValid = cozeTokenService.validateToken(token);
        Map<String, Object> result = new HashMap<>();
        result.put("valid", isValid);
        return ResponseEntity.ok(result);
    }

    /**
     * 刷新Token状态
     */
    @PutMapping("/{id}/refresh-status")
    public ResponseEntity<Void> refreshTokenStatus(@PathVariable Long id) {
        cozeTokenService.refreshTokenStatus(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 测试Token连接
     */
    @PostMapping("/{id}/test")
    public ResponseEntity<Map<String, Object>> testTokenConnection(@PathVariable Long id) {
        Map<String, Object> result = cozeTokenService.testTokenConnection(id);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取Token统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getTokenStatistics() {
        Map<String, Object> stats = cozeTokenService.getTokenStatistics();
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取即将过期的Token
     */
    @GetMapping("/expiring")
    public ResponseEntity<List<CozeToken>> getExpiringTokens(
            @RequestParam(defaultValue = "7") int days) {
        List<CozeToken> tokens = cozeTokenService.getExpiringTokens(days);
        return ResponseEntity.ok(tokens);
    }

    /**
     * 批量更新Token状态
     */
    @PutMapping("/batch-update-status")
    public ResponseEntity<Void> batchUpdateTokenStatus(
            @RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        CozeToken.TokenStatus status = CozeToken.TokenStatus.valueOf((String) request.get("status"));
        cozeTokenService.batchUpdateTokenStatus(ids, status);
        return ResponseEntity.ok().build();
    }

    /**
     * 导出Token配置
     */
    @PostMapping("/export")
    public ResponseEntity<String> exportTokenConfig(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        String config = cozeTokenService.exportTokenConfig(ids);
        return ResponseEntity.ok(config);
    }

    /**
     * 导入Token配置
     */
    @PostMapping("/import")
    public ResponseEntity<List<CozeToken>> importTokenConfig(@RequestBody Map<String, String> request) {
        String config = request.get("config");
        List<CozeToken> tokens = cozeTokenService.importTokenConfig(config);
        return ResponseEntity.ok(tokens);
    }
}
