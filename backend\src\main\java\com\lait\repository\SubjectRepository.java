package com.lait.repository;

import com.lait.entity.Subject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 学科数据访问层
 */
@Repository
public interface SubjectRepository extends JpaRepository<Subject, Long> {

    /**
     * 根据学科代码查找学科
     */
    Optional<Subject> findBySubjectCode(String subjectCode);

    /**
     * 根据年级查找学科
     */
    List<Subject> findByGradeLevel(Integer gradeLevel);

    /**
     * 根据状态查找学科
     */
    List<Subject> findByStatus(Subject.SubjectStatus status);

    /**
     * 根据年级和状态查找学科
     */
    List<Subject> findByGradeLevelAndStatus(Integer gradeLevel, Subject.SubjectStatus status);

    /**
     * 检查学科代码是否存在
     */
    boolean existsBySubjectCode(String subjectCode);

    /**
     * 分页查询学科（排除已删除）
     */
    @Query("SELECT s FROM Subject s WHERE s.isDeleted = false ORDER BY s.sortOrder ASC, s.createdTime DESC")
    Page<Subject> findAllActive(Pageable pageable);

    /**
     * 根据关键词搜索学科
     */
    @Query("SELECT s FROM Subject s WHERE s.isDeleted = false AND " +
           "(s.name LIKE %:keyword% OR s.description LIKE %:keyword% OR s.subjectCode LIKE %:keyword%)")
    Page<Subject> searchSubjects(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取所有启用的学科（按排序）
     */
    @Query("SELECT s FROM Subject s WHERE s.isDeleted = false AND s.status = 'ACTIVE' ORDER BY s.sortOrder ASC")
    List<Subject> findAllActiveSubjects();
}
