<template>
  <el-dialog
    v-model="dialogVisible"
    title="执行工作流"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="workflow" class="workflow-info">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="工作流名称">{{ workflow.name }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ getTypeLabel(workflow.type) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(workflow.status)">
            {{ getStatusLabel(workflow.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="执行次数">{{ workflow.executionCount || 0 }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <el-divider>输入参数</el-divider>

    <div class="input-section">
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="表单模式" name="form">
          <div v-if="inputFields.length > 0" class="form-inputs">
            <el-form :model="formInputs" label-width="120px">
              <el-form-item
                v-for="field in inputFields"
                :key="field.name"
                :label="field.label"
                :prop="field.name"
              >
                <el-input
                  v-if="field.type === 'string'"
                  v-model="formInputs[field.name]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                />
                <el-input-number
                  v-else-if="field.type === 'number'"
                  v-model="formInputs[field.name]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  style="width: 100%"
                />
                <el-select
                  v-else-if="field.type === 'select'"
                  v-model="formInputs[field.name]"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <el-input
                  v-else
                  v-model="formInputs[field.name]"
                  type="textarea"
                  :rows="3"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                />
              </el-form-item>
            </el-form>
          </div>
          <div v-else class="no-fields">
            <el-empty description="未配置输入字段，请使用JSON模式" />
          </div>
        </el-tab-pane>

        <el-tab-pane label="JSON模式" name="json">
          <el-input
            v-model="jsonInput"
            type="textarea"
            :rows="10"
            placeholder="请输入JSON格式的参数"
            @blur="validateJsonInput"
          />
          <div v-if="jsonValidation" class="validation-message">
            <el-text :type="jsonValidation.valid ? 'success' : 'danger'">
              {{ jsonValidation.message }}
            </el-text>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-divider>执行选项</el-divider>

    <div class="execution-options">
      <el-form :model="executionOptions" label-width="120px">
        <el-form-item label="执行模式">
          <el-radio-group v-model="executionOptions.mode">
            <el-radio label="sync">同步执行</el-radio>
            <el-radio label="async">异步执行</el-radio>
            <el-radio label="test">测试模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="executionOptions.mode === 'test'" label="测试说明">
          <el-text type="info">测试模式不会保存执行记录，仅用于验证工作流配置</el-text>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleExecute" :loading="executing">
          {{ getExecuteButtonText() }}
        </el-button>
      </div>
    </template>

    <!-- 执行结果对话框 -->
    <el-dialog
      v-model="showResultDialog"
      title="执行结果"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="executionResult" class="execution-result">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="执行状态">
            <el-tag :type="getExecutionStatusType(executionResult.status)">
              {{ getExecutionStatusLabel(executionResult.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="executionResult.duration" label="执行时长">
            {{ executionResult.duration }}ms
          </el-descriptions-item>
          <el-descriptions-item v-if="executionResult.startTime" label="开始时间">
            {{ formatDateTime(executionResult.startTime) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="executionResult.endTime" label="结束时间">
            {{ formatDateTime(executionResult.endTime) }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider>输出结果</el-divider>

        <div v-if="executionResult.outputData" class="output-data">
          <el-input
            :model-value="formatJson(executionResult.outputData)"
            type="textarea"
            :rows="8"
            readonly
          />
        </div>

        <div v-if="executionResult.errorMessage" class="error-message">
          <el-alert
            :title="executionResult.errorMessage"
            type="error"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showResultDialog = false">关闭</el-button>
          <el-button v-if="executionResult && executionResult.status === 'FAILED'" type="primary" @click="handleRetry">
            重新执行
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  executeWorkflow,
  executeWorkflowAsync,
  testWorkflow,
  workflowTypes,
  workflowStatuses,
  executionStatuses
} from '@/api/cozeWorkflow'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  workflow: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const executing = ref(false)
const activeTab = ref('form')
const showResultDialog = ref(false)
const executionResult = ref(null)

const formInputs = reactive({})
const executionOptions = reactive({
  mode: 'sync'
})

const jsonInput = ref('{}')
const jsonValidation = ref(null)
const inputFields = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const getTypeLabel = (type) => {
  const typeObj = workflowTypes.find(t => t.value === type)
  return typeObj ? typeObj.label : type
}

const getStatusLabel = (status) => {
  const statusObj = workflowStatuses.find(s => s.value === status)
  return statusObj ? statusObj.label : status
}

const getStatusTagType = (status) => {
  const statusMap = {
    'DRAFT': 'info',
    'ACTIVE': 'success',
    'INACTIVE': 'warning',
    'TESTING': 'primary',
    'ARCHIVED': 'danger'
  }
  return statusMap[status] || ''
}

const getExecutionStatusType = (status) => {
  const statusMap = {
    'PENDING': 'info',
    'RUNNING': 'primary',
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'CANCELLED': 'warning',
    'TIMEOUT': 'danger'
  }
  return statusMap[status] || ''
}

const getExecutionStatusLabel = (status) => {
  const statusObj = executionStatuses.find(s => s.value === status)
  return statusObj ? statusObj.label : status
}

const getExecuteButtonText = () => {
  switch (executionOptions.mode) {
    case 'async':
      return '异步执行'
    case 'test':
      return '测试执行'
    default:
      return '立即执行'
  }
}

const parseInputConfig = () => {
  if (!props.workflow || !props.workflow.inputConfig) {
    inputFields.value = []
    return
  }

  try {
    const config = JSON.parse(props.workflow.inputConfig)
    if (config.fields && Array.isArray(config.fields)) {
      inputFields.value = config.fields
      // 初始化表单数据
      config.fields.forEach(field => {
        formInputs[field.name] = field.defaultValue || ''
      })
    } else {
      inputFields.value = []
    }
  } catch (error) {
    console.error('解析输入配置失败:', error)
    inputFields.value = []
  }
}

const validateJsonInput = () => {
  if (!jsonInput.value || jsonInput.value.trim() === '') {
    jsonValidation.value = null
    return
  }

  try {
    JSON.parse(jsonInput.value)
    jsonValidation.value = {
      valid: true,
      message: 'JSON格式正确'
    }
  } catch (error) {
    jsonValidation.value = {
      valid: false,
      message: 'JSON格式错误: ' + error.message
    }
  }
}

const getInputData = () => {
  if (activeTab.value === 'form') {
    return { ...formInputs }
  } else {
    try {
      return JSON.parse(jsonInput.value)
    } catch (error) {
      throw new Error('JSON格式错误')
    }
  }
}

const handleExecute = async () => {
  try {
    const inputData = getInputData()
    executing.value = true

    let result
    switch (executionOptions.mode) {
      case 'async':
        result = await executeWorkflowAsync(props.workflow.id, inputData)
        ElMessage.success('异步执行已启动，执行ID: ' + result.data)
        emit('success')
        handleClose()
        return
      case 'test':
        result = await testWorkflow(props.workflow.id, inputData)
        break
      default:
        result = await executeWorkflow(props.workflow.id, inputData)
        break
    }

    if (result.data) {
      executionResult.value = result.data
      showResultDialog.value = true
      
      if (executionOptions.mode !== 'test') {
        emit('success')
      }
    }
  } catch (error) {
    ElMessage.error('执行失败: ' + (error.message || '未知错误'))
  } finally {
    executing.value = false
  }
}

const handleRetry = () => {
  showResultDialog.value = false
  handleExecute()
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.keys(formInputs).forEach(key => {
    delete formInputs[key]
  })
  jsonInput.value = '{}'
  jsonValidation.value = null
  activeTab.value = 'form'
  executionOptions.mode = 'sync'
  executionResult.value = null
  showResultDialog.value = false
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatJson = (jsonStr) => {
  try {
    const obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return jsonStr
  }
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal && props.workflow) {
    parseInputConfig()
  }
})
</script>

<style scoped>
.workflow-info {
  margin-bottom: 20px;
}

.input-section {
  margin-bottom: 20px;
}

.form-inputs {
  padding: 15px 0;
}

.no-fields {
  padding: 20px 0;
  text-align: center;
}

.validation-message {
  margin-top: 8px;
  font-size: 12px;
}

.execution-options {
  margin-bottom: 20px;
}

.execution-result {
  margin-bottom: 20px;
}

.output-data {
  margin-top: 15px;
}

.error-message {
  margin-top: 15px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}
</style>
