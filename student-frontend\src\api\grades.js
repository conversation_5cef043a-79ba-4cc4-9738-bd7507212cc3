import request from './request'

// 获取我的成绩列表
export function getMyGrades(params) {
  return request({
    url: '/student/grades/my',
    method: 'get',
    params
  })
}

// 获取成绩详情
export function getGrade(id) {
  return request({
    url: `/grades/${id}`,
    method: 'get'
  })
}

// 根据学科获取成绩
export function getGradesBySubject(subjectId, params) {
  return request({
    url: `/student/grades/subject/${subjectId}`,
    method: 'get',
    params
  })
}

// 获取成绩统计信息
export function getMyGradeStats() {
  return request({
    url: '/student/grades/my/stats',
    method: 'get'
  })
}

// 获取成绩趋势
export function getGradeTrend(params) {
  return request({
    url: '/grades/my/trend',
    method: 'get',
    params
  })
}

// 获取学科成绩分析
export function getSubjectGradeAnalysis(subjectId) {
  return request({
    url: `/grades/my/analysis/${subjectId}`,
    method: 'get'
  })
}
