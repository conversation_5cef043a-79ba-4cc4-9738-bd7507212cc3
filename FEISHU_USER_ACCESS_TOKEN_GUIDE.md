# 飞书用户访问令牌(user_access_token)集成指南

## 🎯 **功能概述**

我已经为飞书API服务添加了完整的用户访问令牌(user_access_token)支持，实现了OAuth2.0授权流程，允许应用代表用户访问飞书资源。

## 🔧 **新增功能**

### **1. OAuth2.0授权流程**
- ✅ 生成授权URL
- ✅ 处理授权回调
- ✅ 获取用户访问令牌
- ✅ 刷新用户访问令牌
- ✅ 获取用户信息
- ✅ 令牌验证

### **2. 令牌管理**
- ✅ 自动令牌缓存
- ✅ 过期检测
- ✅ 自动刷新机制
- ✅ 安全存储

### **3. 用户信息获取**
- ✅ 完整的用户资料
- ✅ 头像信息
- ✅ 部门信息
- ✅ 联系方式

## 📋 **API接口**

### **OAuth控制器 (`/feishu-oauth`)**

#### **1. 生成授权URL**
```http
GET /feishu-oauth/auth-url
```

**响应示例:**
```json
{
  "success": true,
  "authUrl": "https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=xxx&redirect_uri=xxx&scope=xxx&state=xxx",
  "state": "uuid-string"
}
```

#### **2. 处理授权回调**
```http
POST /feishu-oauth/callback
Content-Type: application/json

{
  "code": "authorization_code",
  "state": "uuid-string"
}
```

**响应示例:**
```json
{
  "success": true,
  "userToken": {
    "accessToken": "u-xxx",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "refreshToken": "ur-xxx",
    "scope": "drive:drive:readonly contact:user.id:readonly",
    "userId": "ou_xxx",
    "openId": "ou_xxx"
  },
  "userInfo": {
    "userId": "ou_xxx",
    "name": "张三",
    "email": "<EMAIL>",
    "avatarUrl": "https://xxx.jpg"
  }
}
```

#### **3. 刷新访问令牌**
```http
POST /feishu-oauth/refresh-token
Content-Type: application/json

{
  "refreshToken": "ur-xxx"
}
```

#### **4. 获取用户信息**
```http
POST /feishu-oauth/user-info
Content-Type: application/json

{
  "userAccessToken": "u-xxx"
}
```

#### **5. 验证令牌**
```http
POST /feishu-oauth/validate-token
Content-Type: application/json

{
  "userAccessToken": "u-xxx"
}
```

#### **6. 获取OAuth配置**
```http
GET /feishu-oauth/config
```

## 🔑 **配置说明**

### **application.yml配置**
```yaml
feishu:
  api:
    # 基本配置
    app-id: ${FEISHU_APP_ID:}
    app-secret: ${FEISHU_APP_SECRET:}
    enabled: true
    
    # OAuth配置
    redirect-uri: ${FEISHU_REDIRECT_URI:http://localhost:3000/feishu-oauth/callback}
    scopes:
      - "drive:drive:readonly"          # 云文档只读权限
      - "contact:user.id:readonly"      # 用户ID只读权限
      - "contact:user.base:readonly"    # 用户基本信息只读权限
    user-token-cache-time: 7200         # 用户令牌缓存时间(秒)
```

### **环境变量配置**
```bash
# 必需配置
export FEISHU_APP_ID="cli_xxx"
export FEISHU_APP_SECRET="xxx"

# 可选配置
export FEISHU_REDIRECT_URI="http://localhost:3000/feishu-oauth/callback"
```

## 🚀 **使用流程**

### **完整OAuth流程**

#### **步骤1: 生成授权URL**
```javascript
// 前端调用
const response = await fetch('/feishu-oauth/auth-url');
const { authUrl, state } = await response.json();

// 保存state用于验证
sessionStorage.setItem('oauth_state', state);

// 跳转到授权页面
window.location.href = authUrl;
```

#### **步骤2: 用户授权**
用户在飞书授权页面完成授权后，会重定向到配置的回调URL，携带授权码。

#### **步骤3: 处理回调**
```javascript
// 前端处理回调
const urlParams = new URLSearchParams(window.location.search);
const code = urlParams.get('code');
const state = urlParams.get('state');

// 验证state
const savedState = sessionStorage.getItem('oauth_state');
if (state !== savedState) {
  throw new Error('State验证失败');
}

// 获取用户令牌
const response = await fetch('/feishu-oauth/callback', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ code, state })
});

const { userToken, userInfo } = await response.json();

// 保存令牌
localStorage.setItem('feishu_user_token', JSON.stringify(userToken));
localStorage.setItem('feishu_user_info', JSON.stringify(userInfo));
```

#### **步骤4: 使用用户令牌**
```javascript
// 使用用户令牌调用API
const userToken = JSON.parse(localStorage.getItem('feishu_user_token'));

const response = await fetch('/feishu-api/user-documents', {
  headers: {
    'Authorization': `Bearer ${userToken.accessToken}`
  }
});
```

#### **步骤5: 令牌刷新**
```javascript
// 检查令牌是否过期并刷新
async function ensureValidToken() {
  const userToken = JSON.parse(localStorage.getItem('feishu_user_token'));
  
  if (!userToken) {
    // 重新授权
    return redirectToAuth();
  }
  
  // 检查是否即将过期(提前5分钟)
  const now = Date.now() / 1000;
  const expiresAt = userToken.timestamp + userToken.expiresIn;
  
  if (now >= expiresAt - 300) {
    // 刷新令牌
    const response = await fetch('/feishu-oauth/refresh-token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refreshToken: userToken.refreshToken })
    });
    
    const { userToken: newToken } = await response.json();
    localStorage.setItem('feishu_user_token', JSON.stringify(newToken));
    
    return newToken;
  }
  
  return userToken;
}
```

## 🔒 **权限范围**

### **当前支持的权限**
- `drive:drive:readonly`: 云文档只读权限
- `contact:user.id:readonly`: 用户ID只读权限  
- `contact:user.base:readonly`: 用户基本信息只读权限

### **可扩展权限**
根据需要可以添加更多权限：
- `drive:drive`: 云文档读写权限
- `calendar:calendar:readonly`: 日历只读权限
- `im:message:send_as_bot`: 发送消息权限
- `contact:contact:readonly`: 通讯录只读权限

## 🛡️ **安全考虑**

### **1. CSRF防护**
- 使用随机state参数
- 验证回调中的state值
- 防止跨站请求伪造

### **2. 令牌安全**
- 用户令牌自动过期
- 支持令牌刷新
- 安全的令牌存储

### **3. 权限最小化**
- 只请求必要的权限
- 明确的权限范围
- 用户可见的权限说明

## 📊 **数据结构**

### **用户访问令牌 (FeishuUserTokenDto)**
```java
{
  "accessToken": "u-xxx",      // 用户访问令牌
  "tokenType": "Bearer",       // 令牌类型
  "expiresIn": 7200,          // 过期时间(秒)
  "refreshToken": "ur-xxx",    // 刷新令牌
  "scope": "drive:drive:readonly", // 权限范围
  "userId": "ou_xxx",         // 用户ID
  "openId": "ou_xxx",         // OpenID
  "timestamp": 1640995200     // 获取时间戳
}
```

### **用户信息 (FeishuUserInfoDto)**
```java
{
  "userId": "ou_xxx",         // 用户ID
  "openId": "ou_xxx",         // OpenID
  "unionId": "on_xxx",        // UnionID
  "name": "张三",             // 用户名称
  "enName": "Zhang San",      // 英文名
  "email": "<EMAIL>", // 邮箱
  "mobile": "+86-13800138000", // 手机号
  "avatarUrl": "https://xxx.jpg", // 头像URL
  "tenantKey": "xxx",         // 租户Key
  "employeeId": "123456",     // 员工ID
  "departmentIds": ["od_xxx"], // 部门ID列表
  "workStation": "北京",       // 工作城市
  "country": "CN",            // 国家
  "timeZone": "Asia/Shanghai" // 时区
}
```

## 🔧 **故障排除**

### **常见问题**

#### **1. 授权URL生成失败**
- 检查App ID配置
- 确认redirect_uri配置正确
- 验证权限范围设置

#### **2. 授权码换取令牌失败**
- 确认App Secret配置
- 检查授权码是否过期
- 验证redirect_uri一致性

#### **3. 用户信息获取失败**
- 检查用户令牌有效性
- 确认权限范围包含用户信息权限
- 验证API调用权限

### **调试方法**
1. 查看后端日志获取详细错误信息
2. 使用浏览器开发者工具检查网络请求
3. 验证飞书开放平台应用配置
4. 测试OAuth流程各个步骤

通过这个完整的用户访问令牌集成，您的应用现在可以安全地代表用户访问飞书资源，提供更丰富的功能体验。
