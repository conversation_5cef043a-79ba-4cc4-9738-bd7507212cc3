package com.lait.service;

import com.lait.dto.QuestionDTO;
import com.lait.entity.Question;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 题目服务接口
 */
public interface QuestionService {

    /**
     * 创建题目
     */
    QuestionDTO createQuestion(QuestionDTO.CreateQuestionRequest request);

    /**
     * 根据ID获取题目
     */
    QuestionDTO getQuestionById(Long id);

    /**
     * 更新题目信息
     */
    QuestionDTO updateQuestion(Long id, QuestionDTO.UpdateQuestionRequest request);

    /**
     * 删除题目
     */
    void deleteQuestion(Long id);

    /**
     * 分页查询题目
     */
    Page<QuestionDTO> getQuestions(Pageable pageable);

    /**
     * 根据学科ID分页查询题目
     */
    Page<QuestionDTO> getQuestionsBySubjectId(Long subjectId, Pageable pageable);

    /**
     * 根据条件查询题目
     */
    Page<QuestionDTO> searchQuestions(QuestionDTO.QuestionQueryRequest request, Pageable pageable);

    /**
     * 根据难度等级查询题目
     */
    Page<QuestionDTO> getQuestionsByDifficulty(Question.DifficultyLevel difficulty, Pageable pageable);

    /**
     * 根据题目类型查询题目
     */
    Page<QuestionDTO> getQuestionsByType(Question.QuestionType type, Pageable pageable);

    /**
     * 根据年级查询题目
     */
    Page<QuestionDTO> getQuestionsByGradeLevel(Integer gradeLevel, Pageable pageable);

    /**
     * 随机获取题目
     */
    List<QuestionDTO> getRandomQuestions(Long subjectId, Question.DifficultyLevel difficulty, int count);

    /**
     * 批量导入题目
     */
    List<QuestionDTO> batchImportQuestions(QuestionDTO.BatchImportRequest request);

    /**
     * 获取题目统计信息
     */
    QuestionDTO.QuestionStatistics getQuestionStatistics(Long questionId);

    /**
     * 更新题目使用次数
     */
    void incrementUsageCount(Long questionId);

    /**
     * 更新题目正确次数
     */
    void incrementCorrectCount(Long questionId);

    /**
     * 获取热门题目
     */
    List<QuestionDTO> getPopularQuestions(Long subjectId, int limit);

    /**
     * 获取错误率最高的题目
     */
    List<QuestionDTO> getMostWrongQuestions(Long subjectId, int limit);

    /**
     * 根据标签查询题目
     */
    Page<QuestionDTO> getQuestionsByTags(String tags, Pageable pageable);

    /**
     * 复制题目
     */
    QuestionDTO duplicateQuestion(Long questionId);

    /**
     * 批量删除题目
     */
    void batchDeleteQuestions(Long[] questionIds);

    /**
     * 从飞书文档导入题目
     */
    List<QuestionDTO> importQuestionsFromFeishuDocument(String docId, Long subjectId);

    /**
     * 解析飞书文档内容为题目
     */
    List<QuestionDTO> parseFeishuDocumentContent(String content, Long subjectId);
}
