package com.lait.repository;

import com.lait.entity.StudyProgress;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 学习进度数据访问层
 */
@Repository
public interface StudyProgressRepository extends JpaRepository<StudyProgress, Long> {

    /**
     * 根据用户ID和学科ID查找进度
     */
    Optional<StudyProgress> findByUserIdAndSubjectId(Long userId, Long subjectId);

    /**
     * 根据用户ID和学科ID和章节查找进度
     */
    Optional<StudyProgress> findByUserIdAndSubjectIdAndChapter(Long userId, Long subjectId, String chapter);

    /**
     * 根据用户ID查找所有进度
     */
    List<StudyProgress> findByUserIdOrderByLastStudyTimeDesc(Long userId);

    /**
     * 根据学科ID查找所有进度
     */
    List<StudyProgress> findBySubjectIdOrderByCompletionPercentageDesc(Long subjectId);

    /**
     * 查找用户最近学习的进度
     */
    @Query("SELECT sp FROM StudyProgress sp WHERE sp.userId = :userId " +
           "ORDER BY sp.lastStudyTime DESC")
    List<StudyProgress> findRecentStudyProgress(@Param("userId") Long userId, Pageable pageable);

    /**
     * 分页查询学习进度
     */
    @Query("SELECT sp FROM StudyProgress sp WHERE " +
           "(:userId IS NULL OR sp.userId = :userId) AND " +
           "(:subjectId IS NULL OR sp.subjectId = :subjectId) AND " +
           "(:chapter IS NULL OR sp.chapter = :chapter) AND " +
           "(:masteryLevel IS NULL OR sp.masteryLevel = :masteryLevel) " +
           "ORDER BY sp.lastStudyTime DESC")
    Page<StudyProgress> findProgressWithFilters(@Param("userId") Long userId,
                                               @Param("subjectId") Long subjectId,
                                               @Param("chapter") String chapter,
                                               @Param("masteryLevel") StudyProgress.MasteryLevel masteryLevel,
                                               Pageable pageable);

    /**
     * 统计用户总体学习进度
     */
    @Query("SELECT AVG(sp.completionPercentage), AVG(sp.accuracyRate), SUM(sp.totalTimeSpent) " +
           "FROM StudyProgress sp WHERE sp.userId = :userId")
    Object[] getOverallProgress(@Param("userId") Long userId);

    /**
     * 查找完成度最高的用户
     */
    @Query("SELECT sp.userId, AVG(sp.completionPercentage) as avgCompletion " +
           "FROM StudyProgress sp GROUP BY sp.userId " +
           "ORDER BY avgCompletion DESC")
    List<Object[]> findTopPerformers(Pageable pageable);

    /**
     * 查找需要提醒学习的用户
     */
    @Query("SELECT sp FROM StudyProgress sp WHERE sp.lastStudyTime < :threshold " +
           "ORDER BY sp.lastStudyTime ASC")
    List<StudyProgress> findUsersNeedingReminder(@Param("threshold") LocalDateTime threshold);

    /**
     * 统计各掌握程度的用户数量
     */
    @Query("SELECT sp.masteryLevel, COUNT(DISTINCT sp.userId) " +
           "FROM StudyProgress sp GROUP BY sp.masteryLevel")
    List<Object[]> countUsersByMasteryLevel();

    /**
     * 查找学习连续天数最长的用户
     */
    @Query("SELECT sp.userId, MAX(sp.studyStreak) as maxStreak " +
           "FROM StudyProgress sp GROUP BY sp.userId " +
           "ORDER BY maxStreak DESC")
    List<Object[]> findLongestStreaks(Pageable pageable);

    /**
     * 统计各学科的平均完成度
     */
    @Query("SELECT s.name, AVG(sp.completionPercentage), AVG(sp.accuracyRate) " +
           "FROM StudyProgress sp LEFT JOIN sp.subject s " +
           "GROUP BY s.name")
    List<Object[]> getSubjectAverages();

    /**
     * 查找用户薄弱的学科
     */
    @Query("SELECT sp FROM StudyProgress sp WHERE sp.userId = :userId " +
           "AND sp.accuracyRate < :threshold " +
           "ORDER BY sp.accuracyRate ASC")
    List<StudyProgress> findWeakSubjects(@Param("userId") Long userId, 
                                        @Param("threshold") Double threshold);

    /**
     * 查找用户擅长的学科
     */
    @Query("SELECT sp FROM StudyProgress sp WHERE sp.userId = :userId " +
           "AND sp.accuracyRate >= :threshold " +
           "ORDER BY sp.accuracyRate DESC")
    List<StudyProgress> findStrongSubjects(@Param("userId") Long userId, 
                                          @Param("threshold") Double threshold);

    /**
     * 更新学习进度
     */
    @Query("UPDATE StudyProgress sp SET " +
           "sp.completedQuestions = sp.completedQuestions + 1, " +
           "sp.correctQuestions = sp.correctQuestions + :correctIncrement, " +
           "sp.totalTimeSpent = sp.totalTimeSpent + :timeSpent, " +
           "sp.lastStudyTime = :studyTime " +
           "WHERE sp.userId = :userId AND sp.subjectId = :subjectId")
    void updateProgress(@Param("userId") Long userId,
                       @Param("subjectId") Long subjectId,
                       @Param("correctIncrement") Integer correctIncrement,
                       @Param("timeSpent") Integer timeSpent,
                       @Param("studyTime") LocalDateTime studyTime);

    /**
     * 查找活跃学习者
     */
    @Query("SELECT sp FROM StudyProgress sp WHERE sp.lastStudyTime >= :threshold " +
           "ORDER BY sp.totalTimeSpent DESC")
    List<StudyProgress> findActiveLearners(@Param("threshold") LocalDateTime threshold, Pageable pageable);
}
