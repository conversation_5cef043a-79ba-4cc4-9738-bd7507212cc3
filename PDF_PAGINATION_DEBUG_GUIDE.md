# PDF分页预览问题排查指南

## 问题描述
PDF分页预览功能只显示第一页，无法正常浏览其他页面。

## 已实现的修复方案

### 1. 简化分页算法
- **问题**: 原始的分页算法过于复杂，基于DOM元素高度计算容易出错
- **解决方案**: 改为基于字符数量的简单分页策略
- **实现**: 每1500个字符为一页，确保分页逻辑的可靠性

### 2. 增强调试功能
- **添加调试信息面板**: 显示总页数、当前页、页面数组长度等关键信息
- **添加控制台日志**: 详细记录分页过程和结果
- **添加测试内容按钮**: 快速生成长文档用于测试分页

### 3. 改进用户界面
- **重新分页按钮**: 手动触发分页重新生成
- **测试内容按钮**: 创建包含多个章节的长文档
- **页面头部信息**: 显示每页的页码和内容长度

## 测试步骤

### 步骤1: 访问编辑页面
1. 打开浏览器访问 http://127.0.0.1:3001/
2. 进入飞书文档管理页面
3. 点击任意文档的"编辑内容"按钮

### 步骤2: 创建测试内容
1. 在编辑页面点击"测试内容"按钮
2. 系统会自动生成一个长文档（约4000+字符）
3. 切换到"预览"模式

### 步骤3: 测试PDF分页
1. 在预览模式下选择"PDF分页预览"
2. 查看调试信息面板，确认总页数 > 1
3. 使用"上一页"/"下一页"按钮测试页面切换
4. 观察页面内容是否正确显示

### 步骤4: 验证分页逻辑
1. 点击"重新分页"按钮
2. 查看控制台日志输出
3. 确认分页数据正确生成

## 关键代码修改

### 分页算法简化
```javascript
// 简单的分页策略：每1500个字符一页
const charsPerPage = 1500
const estimatedPages = Math.max(1, Math.ceil(contentLength / charsPerPage))

// 多页内容分割
for (let i = 0; i < estimatedPages; i++) {
  const start = i * charsPerPage
  const end = Math.min(start + charsPerPage, contentLength)
  const pageContent = content.slice(start, end)
  
  if (pageContent.trim()) {
    pages.push({
      content: pageContent,
      pageNumber: i + 1
    })
  }
}
```

### 调试信息显示
```vue
<div class="debug-info">
  <div>总页数: {{ totalPages }}</div>
  <div>当前页: {{ currentPage }}</div>
  <div>页面数组长度: {{ pdfPages.length }}</div>
  <div>页面尺寸: {{ pageWidth }}×{{ pageHeight }}</div>
</div>
```

### 测试内容生成
```javascript
const createTestContent = () => {
  const testContent = `# 测试文档标题
  
这是一个用于测试PDF分页功能的文档...
[包含4000+字符的完整测试内容]`
  
  editContent.value = testContent
  ElMessage.success('已创建测试内容，可以测试分页功能了')
}
```

## 预期结果

### 正常工作的分页功能应该：
1. **生成多页**: 测试内容应该生成3-4页
2. **页面切换**: 上一页/下一页按钮正常工作
3. **内容显示**: 每页显示不同的内容片段
4. **页码正确**: 页码信息准确显示
5. **调试信息**: 调试面板显示正确的页数信息

### 控制台日志应该显示：
```
开始生成PDF分页...
内容长度: 4000+
预计页数: 3
PDF分页完成，共 3 页
页面详情: [
  { pageNumber: 1, contentLength: 1500, preview: "# 测试文档标题..." },
  { pageNumber: 2, contentLength: 1500, preview: "### 2.1 技术实现..." },
  { pageNumber: 3, contentLength: 1000+, preview: "## 第四章 总结..." }
]
```

## 故障排除

### 如果仍然只显示第一页：

1. **检查响应式数据**:
   - 确认 `pdfPages.value` 数组包含多个元素
   - 确认 `totalPages.value` > 1
   - 确认 `currentPage.value` 可以正确切换

2. **检查Vue渲染**:
   - 确认 `v-show="index + 1 === currentPage"` 逻辑正确
   - 确认页面切换时 `currentPage` 值确实在变化

3. **检查CSS样式**:
   - 确认页面容器没有被隐藏
   - 确认页面高度设置正确

4. **检查分页数据**:
   - 使用浏览器开发者工具查看 `pdfPages` 数组
   - 确认每页的 `content` 字段包含不同内容

### 调试命令
在浏览器控制台执行：
```javascript
// 查看分页数据
console.log('PDF Pages:', window.vue?.$data?.pdfPages)
console.log('Current Page:', window.vue?.$data?.currentPage)
console.log('Total Pages:', window.vue?.$data?.totalPages)
```

## 下一步优化

如果基本分页功能正常工作，可以考虑：

1. **智能分页**: 基于段落和标题进行更智能的分页
2. **页面预览**: 添加页面缩略图预览
3. **分页标记**: 在编辑器中显示分页位置标记
4. **自定义分页**: 允许用户手动调整分页位置

## 总结

通过简化分页算法和增强调试功能，PDF分页预览问题应该得到解决。关键是确保：
- 分页逻辑简单可靠
- 调试信息充分
- 测试用例完整
- 用户反馈及时

现在可以访问编辑页面测试这些修复方案的效果。
