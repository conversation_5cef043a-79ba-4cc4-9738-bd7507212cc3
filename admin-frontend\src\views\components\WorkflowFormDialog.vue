<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑工作流' : '新建工作流'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工作流名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入工作流名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作流类型" prop="type">
            <el-select v-model="form.type" placeholder="选择工作流类型" style="width: 100%">
              <el-option
                v-for="type in workflowTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-input
              v-model="form.category"
              placeholder="请输入分类"
              maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签">
            <el-input
              v-model="form.tags"
              placeholder="多个标签用逗号分隔"
              maxlength="200"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入工作流描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="工作流ID">
        <el-input
          v-model="form.workflowId"
          placeholder="Coze平台的工作流ID（可选）"
          maxlength="100"
        />
      </el-form-item>

      <el-form-item label="配置信息">
        <div class="config-section">
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="基础配置" name="config">
              <el-input
                v-model="form.config"
                type="textarea"
                :rows="8"
                placeholder="请输入JSON格式的配置信息"
                @blur="validateConfig('config')"
              />
              <div v-if="configValidation.config" class="validation-message">
                <el-text :type="configValidation.config.valid ? 'success' : 'danger'">
                  {{ configValidation.config.message }}
                </el-text>
              </div>
            </el-tab-pane>

            <el-tab-pane label="输入配置" name="inputConfig">
              <el-input
                v-model="form.inputConfig"
                type="textarea"
                :rows="8"
                placeholder="请输入输入参数的JSON配置"
                @blur="validateConfig('inputConfig')"
              />
              <div v-if="configValidation.inputConfig" class="validation-message">
                <el-text :type="configValidation.inputConfig.valid ? 'success' : 'danger'">
                  {{ configValidation.inputConfig.message }}
                </el-text>
              </div>
            </el-tab-pane>

            <el-tab-pane label="输出配置" name="outputConfig">
              <el-input
                v-model="form.outputConfig"
                type="textarea"
                :rows="8"
                placeholder="请输入输出参数的JSON配置"
                @blur="validateConfig('outputConfig')"
              />
              <div v-if="configValidation.outputConfig" class="validation-message">
                <el-text :type="configValidation.outputConfig.valid ? 'success' : 'danger'">
                  {{ configValidation.outputConfig.message }}
                </el-text>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联Token">
            <el-select v-model="form.tokenId" placeholder="选择关联的Token" clearable style="width: 100%">
              <el-option
                v-for="token in tokens"
                :key="token.id"
                :label="token.name"
                :value="token.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设置">
            <el-checkbox v-model="form.isPublic">公开工作流</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { createWorkflow, updateWorkflow, validateWorkflowConfig, workflowTypes } from '@/api/cozeWorkflow'
import { getCozeTokens } from '@/api/cozeTokens'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  workflow: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const activeTab = ref('config')
const tokens = ref([])

const form = reactive({
  name: '',
  description: '',
  workflowId: '',
  type: '',
  category: '',
  tags: '',
  config: '{}',
  inputConfig: '{}',
  outputConfig: '{}',
  tokenId: null,
  isPublic: false
})

const configValidation = reactive({
  config: null,
  inputConfig: null,
  outputConfig: null
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入工作流名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择工作流类型', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const loadTokens = async () => {
  try {
    const response = await getCozeTokens({ status: 'ACTIVE' })
    if (response.data && response.data.content) {
      tokens.value = response.data.content
    }
  } catch (error) {
    console.error('加载Token列表失败:', error)
  }
}

const validateConfig = async (configType) => {
  const configValue = form[configType]
  if (!configValue || configValue.trim() === '') {
    configValidation[configType] = null
    return
  }

  try {
    const response = await validateWorkflowConfig(configValue)
    if (response.data) {
      configValidation[configType] = response.data
    }
  } catch (error) {
    configValidation[configType] = {
      valid: false,
      message: '验证失败'
    }
  }
}

const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (key === 'config' || key === 'inputConfig' || key === 'outputConfig') {
      form[key] = '{}'
    } else if (key === 'isPublic') {
      form[key] = false
    } else if (key === 'tokenId') {
      form[key] = null
    } else {
      form[key] = ''
    }
  })

  Object.keys(configValidation).forEach(key => {
    configValidation[key] = null
  })

  activeTab.value = 'config'
}

const initForm = () => {
  if (props.workflow && props.isEdit) {
    Object.keys(form).forEach(key => {
      if (props.workflow[key] !== undefined) {
        form[key] = props.workflow[key]
      }
    })
  } else {
    resetForm()
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    const formData = { ...form }

    // 确保JSON配置格式正确
    ['config', 'inputConfig', 'outputConfig'].forEach(key => {
      if (formData[key] && formData[key].trim() !== '') {
        try {
          JSON.parse(formData[key])
        } catch (e) {
          throw new Error(`${key}格式不正确，请输入有效的JSON`)
        }
      } else {
        formData[key] = '{}'
      }
    })

    let response
    if (props.isEdit) {
      response = await updateWorkflow(props.workflow.id, formData)
    } else {
      response = await createWorkflow(formData)
    }

    if (response.data) {
      ElMessage.success(props.isEdit ? '更新成功' : '创建成功')
      emit('success')
    }
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error(props.isEdit ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
  formRef.value?.clearValidate()
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// 生命周期
onMounted(() => {
  loadTokens()
})
</script>

<style scoped>
.config-section {
  width: 100%;
}

.config-section :deep(.el-tabs__content) {
  padding: 15px 0;
}

.validation-message {
  margin-top: 8px;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}
</style>
