<template>
  <div class="coze-tokens-page">
    <div class="page-header">
      <h1>Coze Token管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          添加Token
        </el-button>
        <el-button @click="refreshTokens">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">总Token数</div>
            </div>
            <el-icon class="stat-icon"><Key /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card active">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.statusStats?.ACTIVE || 0 }}</div>
              <div class="stat-label">激活状态</div>
            </div>
            <el-icon class="stat-icon"><CircleCheck /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.expiringCount || 0 }}</div>
              <div class="stat-label">即将过期</div>
            </div>
            <el-icon class="stat-icon"><Warning /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card danger">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.statusStats?.EXPIRED || 0 }}</div>
              <div class="stat-label">已过期</div>
            </div>
            <el-icon class="stat-icon"><CircleClose /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="Token名称">
          <el-input v-model="searchForm.name" placeholder="请输入Token名称" clearable />
        </el-form-item>
        <el-form-item label="Token类型">
          <el-select v-model="searchForm.tokenType" placeholder="选择类型" clearable>
            <el-option label="API密钥" value="API_KEY" />
            <el-option label="OAuth令牌" value="OAUTH_TOKEN" />
            <el-option label="Webhook令牌" value="WEBHOOK_TOKEN" />
            <el-option label="机器人令牌" value="BOT_TOKEN" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="激活" value="ACTIVE" />
            <el-option label="停用" value="INACTIVE" />
            <el-option label="已过期" value="EXPIRED" />
            <el-option label="已撤销" value="REVOKED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- Token列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">Token列表</div>
        <div class="table-actions">
          <el-button
            type="success"
            :disabled="selectedTokens.length === 0"
            @click="batchUpdateStatus('ACTIVE')"
          >
            批量激活
          </el-button>
          <el-button
            type="warning"
            :disabled="selectedTokens.length === 0"
            @click="batchUpdateStatus('INACTIVE')"
          >
            批量停用
          </el-button>
          <el-button
            type="info"
            @click="showExpiringTokens"
          >
            即将过期
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tokens"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="Token名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="tokenType" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTokenTypeColor(row.tokenType)">
              {{ getTokenTypeText(row.tokenType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="appName" label="应用名称" width="150" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.isDefault" type="success" size="small">默认</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="usageCount" label="使用次数" width="100" />
        <el-table-column prop="expiresAt" label="过期时间" width="180">
          <template #default="{ row }">
            <span v-if="row.expiresAt" :class="getExpirationClass(row.expiresAt)">
              {{ formatDateTime(row.expiresAt) }}
            </span>
            <span v-else class="text-muted">永不过期</span>
          </template>
        </el-table-column>
        <el-table-column prop="lastUsedAt" label="最后使用" width="180">
          <template #default="{ row }">
            <span v-if="row.lastUsedAt">{{ formatDateTime(row.lastUsedAt) }}</span>
            <span v-else class="text-muted">从未使用</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewToken(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editToken(row)">编辑</el-button>
            <el-button
              size="small"
              type="success"
              @click="testConnection(row)"
              :loading="row.testing"
            >
              测试
            </el-button>
            <el-button
              size="small"
              :type="row.isDefault ? 'warning' : 'info'"
              @click="toggleDefault(row)"
            >
              {{ row.isDefault ? '取消默认' : '设为默认' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteToken(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑Token' : '创建Token'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Token名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入Token名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Token类型" prop="tokenType">
              <el-select v-model="form.tokenType" placeholder="选择类型">
                <el-option label="API密钥" value="API_KEY" />
                <el-option label="OAuth令牌" value="OAUTH_TOKEN" />
                <el-option label="Webhook令牌" value="WEBHOOK_TOKEN" />
                <el-option label="机器人令牌" value="BOT_TOKEN" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="Token值" prop="token">
          <el-input
            v-model="form.token"
            type="textarea"
            :rows="3"
            placeholder="请输入Token值"
            show-password
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="应用ID">
              <el-input v-model="form.appId" placeholder="请输入应用ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应用名称">
              <el-input v-model="form.appName" placeholder="请输入应用名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="选择状态">
                <el-option label="激活" value="ACTIVE" />
                <el-option label="停用" value="INACTIVE" />
                <el-option label="已过期" value="EXPIRED" />
                <el-option label="已撤销" value="REVOKED" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="过期时间">
              <el-date-picker
                v-model="form.expiresAt"
                type="datetime"
                placeholder="选择过期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="每日限制">
              <el-input-number v-model="form.dailyLimit" :min="0" placeholder="0表示无限制" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每月限制">
              <el-input-number v-model="form.monthlyLimit" :min="0" placeholder="0表示无限制" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
          />
        </el-form-item>

        <el-form-item label="配置信息">
          <el-input
            v-model="form.config"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的配置信息"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.isDefault">设为默认Token</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- Token详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="Token详情"
      width="800px"
    >
      <div v-if="currentToken" class="token-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="Token名称">
            {{ currentToken.name }}
          </el-descriptions-item>
          <el-descriptions-item label="Token类型">
            <el-tag :type="getTokenTypeColor(currentToken.tokenType)">
              {{ getTokenTypeText(currentToken.tokenType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="应用ID">
            {{ currentToken.appId || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="应用名称">
            {{ currentToken.appName || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(currentToken.status)">
              {{ getStatusText(currentToken.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否默认">
            <el-tag v-if="currentToken.isDefault" type="success">是</el-tag>
            <span v-else>否</span>
          </el-descriptions-item>
          <el-descriptions-item label="使用次数">
            {{ currentToken.usageCount }}
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            <span v-if="currentToken.expiresAt" :class="getExpirationClass(currentToken.expiresAt)">
              {{ formatDateTime(currentToken.expiresAt) }}
            </span>
            <span v-else>永不过期</span>
          </el-descriptions-item>
          <el-descriptions-item label="最后使用时间">
            {{ currentToken.lastUsedAt ? formatDateTime(currentToken.lastUsedAt) : '从未使用' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(currentToken.createdTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ currentToken.description || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="token-value-section">
          <h4>Token值</h4>
          <el-input
            :value="showTokenValue ? currentToken.token : '●'.repeat(50)"
            readonly
            type="textarea"
            :rows="3"
          >
            <template #append>
              <el-button @click="toggleTokenValue">
                {{ showTokenValue ? '隐藏' : '显示' }}
              </el-button>
            </template>
          </el-input>
        </div>

        <div v-if="currentToken.config" class="config-section">
          <h4>配置信息</h4>
          <pre class="config-content">{{ formatJSON(currentToken.config) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Key, CircleCheck, Warning, CircleClose } from '@element-plus/icons-vue'
import {
  getCozeTokens,
  getCozeToken,
  createCozeToken,
  updateCozeToken,
  deleteCozeToken,
  setDefaultCozeToken,
  testCozeTokenConnection,
  getCozeTokenStatistics,
  batchUpdateCozeTokenStatus
} from '@/api/cozeTokens'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const showTokenValue = ref(false)

const tokens = ref([])
const selectedTokens = ref([])
const currentToken = ref(null)
const statistics = ref({})

// 搜索表单
const searchForm = reactive({
  name: '',
  tokenType: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({
  name: '',
  token: '',
  tokenType: '',
  appId: '',
  appName: '',
  status: 'ACTIVE',
  expiresAt: null,
  dailyLimit: null,
  monthlyLimit: null,
  description: '',
  config: '',
  isDefault: false
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入Token名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  token: [
    { required: true, message: '请输入Token值', trigger: 'blur' }
  ],
  tokenType: [
    { required: true, message: '请选择Token类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 方法
const loadTokens = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      ...searchForm
    }
    const response = await getCozeTokens(params)
    tokens.value = response.data.content
    pagination.total = response.data.totalElements
  } catch (error) {
    ElMessage.error('加载Token列表失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await getCozeTokenStatistics()
    statistics.value = response
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadTokens()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    tokenType: '',
    status: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadTokens()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadTokens()
}

const handleSelectionChange = (selection) => {
  selectedTokens.value = selection
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

const editToken = (token) => {
  isEdit.value = true
  dialogVisible.value = true
  currentToken.value = token
  Object.assign(form, {
    ...token,
    expiresAt: token.expiresAt ? new Date(token.expiresAt).toISOString().slice(0, 19) : null
  })
}

const viewToken = async (token) => {
  try {
    const response = await getCozeToken(token.id)
    currentToken.value = response.data
    detailDialogVisible.value = true
    showTokenValue.value = false
  } catch (error) {
    ElMessage.error('获取Token详情失败')
  }
}

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const data = { ...form }
    if (data.config) {
      try {
        JSON.parse(data.config)
      } catch (error) {
        ElMessage.error('配置信息必须是有效的JSON格式')
        return
      }
    }

    if (isEdit.value) {
      await updateCozeToken(currentToken.value.id, data)
      ElMessage.success('更新成功')
    } else {
      await createCozeToken(data)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadTokens()
    loadStatistics()
  } catch (error) {
    console.error('提交表单失败:', error)
    const errorMessage = error.response?.data?.message || error.message || (isEdit.value ? '更新失败' : '创建失败')
    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}

const deleteToken = async (token) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除Token "${token.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteCozeToken(token.id)
    ElMessage.success('删除成功')
    loadTokens()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const toggleDefault = async (token) => {
  try {
    if (token.isDefault) {
      ElMessage.warning('无法取消默认Token，请先设置其他Token为默认')
      return
    }

    await setDefaultCozeToken(token.id)
    ElMessage.success('设置默认Token成功')
    loadTokens()
  } catch (error) {
    ElMessage.error('设置默认Token失败')
  }
}

const testConnection = async (token) => {
  token.testing = true
  try {
    const response = await testCozeTokenConnection(token.id)
    if (response.data.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(`连接测试失败: ${response.data.message}`)
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  } finally {
    token.testing = false
  }
}

const batchUpdateStatus = async (status) => {
  try {
    const ids = selectedTokens.value.map(token => token.id)
    await batchUpdateCozeTokenStatus(ids, status)
    ElMessage.success('批量更新成功')
    loadTokens()
    loadStatistics()
  } catch (error) {
    ElMessage.error('批量更新失败')
  }
}

const showExpiringTokens = () => {
  searchForm.status = ''
  // 这里可以添加特殊的过期筛选逻辑
  handleSearch()
}

const refreshTokens = () => {
  loadTokens()
  loadStatistics()
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    token: '',
    tokenType: '',
    appId: '',
    appName: '',
    status: 'ACTIVE',
    expiresAt: null,
    dailyLimit: null,
    monthlyLimit: null,
    description: '',
    config: '',
    isDefault: false
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const toggleTokenValue = () => {
  showTokenValue.value = !showTokenValue.value
}

// 辅助方法
const getTokenTypeColor = (type) => {
  const colors = {
    API_KEY: 'primary',
    OAUTH_TOKEN: 'success',
    WEBHOOK_TOKEN: 'warning',
    BOT_TOKEN: 'info'
  }
  return colors[type] || 'default'
}

const getTokenTypeText = (type) => {
  const texts = {
    API_KEY: 'API密钥',
    OAUTH_TOKEN: 'OAuth令牌',
    WEBHOOK_TOKEN: 'Webhook令牌',
    BOT_TOKEN: '机器人令牌'
  }
  return texts[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    ACTIVE: 'success',
    INACTIVE: 'warning',
    EXPIRED: 'danger',
    REVOKED: 'info'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    ACTIVE: '激活',
    INACTIVE: '停用',
    EXPIRED: '已过期',
    REVOKED: '已撤销'
  }
  return texts[status] || status
}

const getExpirationClass = (expiresAt) => {
  const now = new Date()
  const expiration = new Date(expiresAt)
  const diffDays = Math.ceil((expiration - now) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'text-danger'
  if (diffDays <= 7) return 'text-warning'
  return ''
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatJSON = (jsonString) => {
  try {
    return JSON.stringify(JSON.parse(jsonString), null, 2)
  } catch (error) {
    return jsonString
  }
}

// 生命周期
onMounted(() => {
  loadTokens()
  loadStatistics()
})
</script>

<style scoped>
.coze-tokens-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card .el-card__body {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  font-size: 40px;
  opacity: 0.3;
}

.stat-card.active .stat-number {
  color: #67c23a;
}

.stat-card.active .stat-icon {
  color: #67c23a;
}

.stat-card.warning .stat-number {
  color: #e6a23c;
}

.stat-card.warning .stat-icon {
  color: #e6a23c;
}

.stat-card.danger .stat-number {
  color: #f56c6c;
}

.stat-card.danger .stat-icon {
  color: #f56c6c;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.text-danger {
  color: #f56c6c;
}

.text-warning {
  color: #e6a23c;
}

.text-muted {
  color: #909399;
}

.token-detail {
  padding: 20px 0;
}

.token-value-section {
  margin-top: 20px;
}

.token-value-section h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #303133;
}

.config-section {
  margin-top: 20px;
}

.config-section h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #303133;
}

.config-content {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-dialog .el-form {
  padding: 0 20px;
}

.el-descriptions {
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .coze-tokens-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .el-table {
    font-size: 12px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .page-header h1 {
    color: #e5eaf3;
  }

  .stat-number {
    color: #e5eaf3;
  }

  .stat-label {
    color: #a3a6ad;
  }

  .table-title {
    color: #e5eaf3;
  }

  .config-content {
    background: #1d1e1f;
    border-color: #4c4d4f;
    color: #a3a6ad;
  }
}
</style>
