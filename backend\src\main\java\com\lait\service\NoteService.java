package com.lait.service;

import com.lait.dto.NoteDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 笔记服务接口
 */
public interface NoteService {

    /**
     * 创建笔记
     */
    NoteDTO createNote(NoteDTO.CreateNoteRequest request);

    /**
     * 根据ID获取笔记
     */
    NoteDTO getNoteById(Long id);

    /**
     * 更新笔记信息
     */
    NoteDTO updateNote(Long id, NoteDTO.UpdateNoteRequest request);

    /**
     * 删除笔记
     */
    void deleteNote(Long id);

    /**
     * 分页查询笔记
     */
    Page<NoteDTO> getNotes(Pageable pageable);

    /**
     * 根据学生ID分页查询笔记
     */
    Page<NoteDTO> getNotesByStudentId(Long studentId, Pageable pageable);

    /**
     * 根据学科ID分页查询笔记
     */
    Page<NoteDTO> getNotesBySubjectId(Long subjectId, Pageable pageable);

    /**
     * 根据条件查询笔记
     */
    Page<NoteDTO> searchNotes(NoteDTO.NoteQueryRequest request, Pageable pageable);

    /**
     * 获取共享笔记
     */
    Page<NoteDTO> getSharedNotes(Pageable pageable);

    /**
     * 根据标签查询笔记
     */
    Page<NoteDTO> getNotesByTags(String tags, Pageable pageable);

    /**
     * 分享笔记到飞书
     */
    String shareNoteToFeishu(NoteDTO.ShareToFeishuRequest request);

    /**
     * 从飞书同步笔记
     */
    NoteDTO syncNoteFromFeishu(NoteDTO.SyncFromFeishuRequest request);

    /**
     * 获取学生笔记统计
     */
    NoteDTO.NoteStatistics getStudentNoteStatistics(Long studentId);

    /**
     * 获取最近笔记
     */
    List<NoteDTO> getRecentNotes(Long studentId, int limit);

    /**
     * 搜索笔记内容
     */
    Page<NoteDTO> searchNoteContent(String keyword, Pageable pageable);

    /**
     * 批量删除笔记
     */
    void batchDeleteNotes(Long[] noteIds);

    /**
     * 设置笔记分享状态
     */
    void setNoteSharedStatus(Long noteId, Boolean isShared);

    /**
     * 获取热门标签
     */
    List<String> getPopularTags(int limit);

    /**
     * 复制笔记
     */
    NoteDTO duplicateNote(Long noteId, Long targetStudentId);

    /**
     * 获取学科笔记数量
     */
    Long countNotesBySubject(Long subjectId);

    /**
     * 根据学生和学科获取笔记
     */
    Page<NoteDTO> getNotesByStudentAndSubject(Long studentId, Long subjectId, Pageable pageable);
}
