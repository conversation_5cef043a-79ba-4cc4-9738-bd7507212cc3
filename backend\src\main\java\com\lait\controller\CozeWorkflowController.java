package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.entity.CozeWorkflow;
import com.lait.entity.CozeWorkflowExecution;
import com.lait.service.CozeWorkflowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Coze工作流管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/coze-workflows")
@RequiredArgsConstructor
//@PreAuthorize("hasRole('ADMIN')")
public class CozeWorkflowController {

    private final CozeWorkflowService workflowService;

    /**
     * 创建工作流
     */
    @PostMapping
    public ResponseEntity<ApiResponse<CozeWorkflow>> createWorkflow(
            @Valid @RequestBody CozeWorkflow workflow,
            Authentication authentication) {
        try {
            // 设置创建者信息
            workflow.setCreatorName(authentication.getName());

            CozeWorkflow createdWorkflow = workflowService.createWorkflow(workflow);
            return ResponseEntity.ok(ApiResponse.success(createdWorkflow));
        } catch (Exception e) {
            log.error("创建工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("创建工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 更新工作流
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<CozeWorkflow>> updateWorkflow(
            @PathVariable Long id,
            @Valid @RequestBody CozeWorkflow workflow) {
        try {
            CozeWorkflow updatedWorkflow = workflowService.updateWorkflow(id, workflow);
            return ResponseEntity.ok(ApiResponse.success(updatedWorkflow));
        } catch (Exception e) {
            log.error("更新工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("更新工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 删除工作流
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteWorkflow(@PathVariable Long id) {
        try {
            workflowService.deleteWorkflow(id);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("删除工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("删除工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 获取工作流详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<CozeWorkflow>> getWorkflow(@PathVariable Long id) {
        try {
            CozeWorkflow workflow = workflowService.getWorkflowById(id);
            return ResponseEntity.ok(ApiResponse.success(workflow));
        } catch (Exception e) {
            log.error("获取工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 分页查询工作流
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<CozeWorkflow>>> getWorkflows(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) CozeWorkflow.WorkflowType type,
            @RequestParam(required = false) CozeWorkflow.WorkflowStatus status,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Long creatorId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ?
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));

            Page<CozeWorkflow> workflows = workflowService.getWorkflows(
                name, type, status, category, creatorId, pageable);
            return ResponseEntity.ok(ApiResponse.success(workflows));
        } catch (Exception e) {
            log.error("获取工作流列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取工作流列表失败: " + e.getMessage()));
        }
    }

    /**
     * 执行工作流
     */
    @PostMapping("/{id}/execute")
    public ResponseEntity<ApiResponse<CozeWorkflowExecution>> executeWorkflow(
            @PathVariable Long id,
            @RequestBody Map<String, Object> inputData,
            Authentication authentication) {
        try {
            // 这里应该获取当前用户ID，简化处理使用固定值
            Long executorId = 1L; // 实际应该从authentication中获取用户ID

            CozeWorkflowExecution execution = workflowService.executeWorkflow(id, inputData, executorId);
            return ResponseEntity.ok(ApiResponse.success(execution));
        } catch (Exception e) {
            log.error("执行工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("执行工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 异步执行工作流
     */
    @PostMapping("/{id}/execute-async")
    public ResponseEntity<ApiResponse<String>> executeWorkflowAsync(
            @PathVariable Long id,
            @RequestBody Map<String, Object> inputData,
            Authentication authentication) {
        try {
            Long executorId = 1L; // 实际应该从authentication中获取用户ID

            String executionId = workflowService.executeWorkflowAsync(id, inputData, executorId);
            return ResponseEntity.ok(ApiResponse.success(executionId));
        } catch (Exception e) {
            log.error("异步执行工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("异步执行工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 测试工作流
     */
    @PostMapping("/{id}/test")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testWorkflow(
            @PathVariable Long id,
            @RequestBody Map<String, Object> inputData) {
        try {
            Map<String, Object> result = workflowService.testWorkflow(id, inputData);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("测试工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("测试工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 复制工作流
     */
    @PostMapping("/{id}/duplicate")
    public ResponseEntity<ApiResponse<CozeWorkflow>> duplicateWorkflow(
            @PathVariable Long id,
            @RequestParam String newName) {
        try {
            CozeWorkflow duplicatedWorkflow = workflowService.duplicateWorkflow(id, newName);
            return ResponseEntity.ok(ApiResponse.success(duplicatedWorkflow));
        } catch (Exception e) {
            log.error("复制工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("复制工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 导入工作流
     */
    @PostMapping("/import")
    public ResponseEntity<ApiResponse<CozeWorkflow>> importWorkflow(
            @RequestBody String workflowConfig) {
        try {
            CozeWorkflow workflow = workflowService.importWorkflow(workflowConfig);
            return ResponseEntity.ok(ApiResponse.success(workflow));
        } catch (Exception e) {
            log.error("导入工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("导入工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 导出工作流
     */
    @GetMapping("/{id}/export")
    public ResponseEntity<ApiResponse<String>> exportWorkflow(@PathVariable Long id) {
        try {
            String config = workflowService.exportWorkflow(id);
            return ResponseEntity.ok(ApiResponse.success(config));
        } catch (Exception e) {
            log.error("导出工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("导出工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 激活工作流
     */
    @PostMapping("/{id}/activate")
    public ResponseEntity<ApiResponse<Void>> activateWorkflow(@PathVariable Long id) {
        try {
            workflowService.activateWorkflow(id);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("激活工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("激活工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 停用工作流
     */
    @PostMapping("/{id}/deactivate")
    public ResponseEntity<ApiResponse<Void>> deactivateWorkflow(@PathVariable Long id) {
        try {
            workflowService.deactivateWorkflow(id);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("停用工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("停用工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 归档工作流
     */
    @PostMapping("/{id}/archive")
    public ResponseEntity<ApiResponse<Void>> archiveWorkflow(@PathVariable Long id) {
        try {
            workflowService.archiveWorkflow(id);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("归档工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("归档工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 获取工作流执行记录
     */
    @GetMapping("/{id}/executions")
    public ResponseEntity<ApiResponse<Page<CozeWorkflowExecution>>> getWorkflowExecutions(
            @PathVariable Long id,
            @RequestParam(required = false) CozeWorkflowExecution.ExecutionStatus status,
            @RequestParam(required = false) Long executorId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "startTime"));
            Page<CozeWorkflowExecution> executions = workflowService.getWorkflowExecutions(
                id, status, executorId, startTime, endTime, pageable);
            return ResponseEntity.ok(ApiResponse.success(executions));
        } catch (Exception e) {
            log.error("获取工作流执行记录失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取工作流执行记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取执行记录详情
     */
    @GetMapping("/executions/{executionId}")
    public ResponseEntity<ApiResponse<CozeWorkflowExecution>> getExecution(@PathVariable Long executionId) {
        try {
            CozeWorkflowExecution execution = workflowService.getExecutionById(executionId);
            return ResponseEntity.ok(ApiResponse.success(execution));
        } catch (Exception e) {
            log.error("获取执行记录失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取执行记录失败: " + e.getMessage()));
        }
    }

    /**
     * 取消执行
     */
    @PostMapping("/executions/{executionId}/cancel")
    public ResponseEntity<ApiResponse<Void>> cancelExecution(@PathVariable Long executionId) {
        try {
            workflowService.cancelExecution(executionId);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("取消执行失败", e);
            return ResponseEntity.ok(ApiResponse.error("取消执行失败: " + e.getMessage()));
        }
    }

    /**
     * 重新执行
     */
    @PostMapping("/executions/{executionId}/retry")
    public ResponseEntity<ApiResponse<CozeWorkflowExecution>> retryExecution(@PathVariable Long executionId) {
        try {
            CozeWorkflowExecution execution = workflowService.retryExecution(executionId);
            return ResponseEntity.ok(ApiResponse.success(execution));
        } catch (Exception e) {
            log.error("重新执行失败", e);
            return ResponseEntity.ok(ApiResponse.error("重新执行失败: " + e.getMessage()));
        }
    }

    /**
     * 获取工作流统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getWorkflowStatistics() {
        try {
            Map<String, Object> stats = workflowService.getWorkflowStatistics();
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取工作流统计失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取工作流统计失败: " + e.getMessage()));
        }
    }

    /**
     * 获取工作流执行统计
     */
    @GetMapping("/{id}/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getExecutionStatistics(@PathVariable Long id) {
        try {
            Map<String, Object> stats = workflowService.getExecutionStatistics(id);
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取执行统计失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取执行统计失败: " + e.getMessage()));
        }
    }

    /**
     * 获取热门工作流
     */
    @GetMapping("/popular")
    public ResponseEntity<ApiResponse<List<CozeWorkflow>>> getPopularWorkflows(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<CozeWorkflow> workflows = workflowService.getPopularWorkflows(limit);
            return ResponseEntity.ok(ApiResponse.success(workflows));
        } catch (Exception e) {
            log.error("获取热门工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取热门工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 获取最近执行的工作流
     */
    @GetMapping("/recent")
    public ResponseEntity<ApiResponse<List<CozeWorkflow>>> getRecentlyExecutedWorkflows(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<CozeWorkflow> workflows = workflowService.getRecentlyExecutedWorkflows(limit);
            return ResponseEntity.ok(ApiResponse.success(workflows));
        } catch (Exception e) {
            log.error("获取最近执行工作流失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取最近执行工作流失败: " + e.getMessage()));
        }
    }

    /**
     * 验证工作流配置
     */
    @PostMapping("/validate-config")
    public ResponseEntity<ApiResponse<Map<String, Object>>> validateWorkflowConfig(
            @RequestBody String config) {
        try {
            Map<String, Object> result = workflowService.validateWorkflowConfig(config);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("验证工作流配置失败", e);
            return ResponseEntity.ok(ApiResponse.error("验证工作流配置失败: " + e.getMessage()));
        }
    }

    /**
     * 获取工作流模板
     */
    @GetMapping("/templates")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getWorkflowTemplates() {
        try {
            List<Map<String, Object>> templates = workflowService.getWorkflowTemplates();
            return ResponseEntity.ok(ApiResponse.success(templates));
        } catch (Exception e) {
            log.error("获取工作流模板失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取工作流模板失败: " + e.getMessage()));
        }
    }

    /**
     * 清理过期执行记录
     */
    @PostMapping("/cleanup")
    public ResponseEntity<ApiResponse<Void>> cleanupOldExecutions(
            @RequestParam(defaultValue = "30") int daysToKeep) {
        try {
            workflowService.cleanupOldExecutions(daysToKeep);
            return ResponseEntity.ok(ApiResponse.success());
        } catch (Exception e) {
            log.error("清理执行记录失败", e);
            return ResponseEntity.ok(ApiResponse.error("清理执行记录失败: " + e.getMessage()));
        }
    }
}
