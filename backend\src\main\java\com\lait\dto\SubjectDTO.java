package com.lait.dto;

import com.lait.entity.Subject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 学科数据传输对象
 */
@Data
public class SubjectDTO {

    private Long id;

    @NotBlank(message = "学科名称不能为空")
    @Size(max = 100, message = "学科名称长度不能超过100个字符")
    private String name;

    @Size(max = 500, message = "学科描述长度不能超过500个字符")
    private String description;

    private Integer gradeLevel;

    private String subjectCode;

    private Subject.SubjectStatus status;

    private Integer sortOrder;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;

    /**
     * 创建学科请求DTO
     */
    @Data
    public static class CreateSubjectRequest {
        @NotBlank(message = "学科名称不能为空")
        @Size(max = 100, message = "学科名称长度不能超过100个字符")
        private String name;

        @Size(max = 500, message = "学科描述长度不能超过500个字符")
        private String description;

        private Integer gradeLevel;

        private String subjectCode;

        private Integer sortOrder;
    }

    /**
     * 更新学科请求DTO
     */
    @Data
    public static class UpdateSubjectRequest {
        @Size(max = 100, message = "学科名称长度不能超过100个字符")
        private String name;

        @Size(max = 500, message = "学科描述长度不能超过500个字符")
        private String description;

        private Integer gradeLevel;

        private Subject.SubjectStatus status;

        private Integer sortOrder;
    }
}
