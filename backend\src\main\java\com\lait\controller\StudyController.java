package com.lait.controller;

import com.lait.entity.StudyProgress;
import com.lait.entity.StudyRecord;
import com.lait.service.StudyService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习管理控制器
 */
@RestController
@RequestMapping("/admin/study")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
public class StudyController {

    private final StudyService studyService;

    // ========== 学习记录管理 ==========

    /**
     * 记录学习行为
     */
    @PostMapping("/records")
    public ResponseEntity<StudyRecord> recordStudy(@Valid @RequestBody StudyRecord studyRecord) {
        StudyRecord record = studyService.recordStudy(studyRecord);
        return ResponseEntity.ok(record);
    }

    /**
     * 批量记录学习行为
     */
    @PostMapping("/records/batch")
    public ResponseEntity<List<StudyRecord>> batchRecordStudy(@RequestBody List<StudyRecord> studyRecords) {
        List<StudyRecord> records = studyService.batchRecordStudy(studyRecords);
        return ResponseEntity.ok(records);
    }

    /**
     * 获取用户学习记录
     */
    @GetMapping("/records/users/{userId}")
    public ResponseEntity<Page<StudyRecord>> getUserStudyRecords(@PathVariable Long userId,
                                                               @RequestParam(defaultValue = "0") int page,
                                                               @RequestParam(defaultValue = "20") int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<StudyRecord> records = studyService.getUserStudyRecords(userId, pageable);
        return ResponseEntity.ok(records);
    }

    /**
     * 获取学习记录（带筛选）
     */
    @GetMapping("/records")
    public ResponseEntity<Page<StudyRecord>> getStudyRecords(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long subjectId,
            @RequestParam(required = false) StudyRecord.RecordType recordType,
            @RequestParam(required = false) Boolean isCorrect,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<StudyRecord> records = studyService.getStudyRecords(userId, subjectId, recordType,
                                                               isCorrect, startDate, endDate, pageable);
        return ResponseEntity.ok(records);
    }

    /**
     * 获取用户错题记录
     */
    @GetMapping("/records/users/{userId}/wrong-answers")
    public ResponseEntity<List<StudyRecord>> getUserWrongAnswers(@PathVariable Long userId,
                                                               @RequestParam(defaultValue = "50") int limit) {
        List<StudyRecord> records = studyService.getUserWrongAnswers(userId, limit);
        return ResponseEntity.ok(records);
    }

    /**
     * 获取需要复习的题目
     */
    @GetMapping("/records/users/{userId}/review")
    public ResponseEntity<List<StudyRecord>> getQuestionsForReview(@PathVariable Long userId,
                                                                 @RequestParam(defaultValue = "7") int days) {
        List<StudyRecord> records = studyService.getQuestionsForReview(userId, days);
        return ResponseEntity.ok(records);
    }

    // ========== 学习进度管理 ==========

    /**
     * 更新学习进度
     */
    @PostMapping("/progress")
    public ResponseEntity<StudyProgress> updateStudyProgress(@RequestBody Map<String, Object> request) {
        Long userId = ((Number) request.get("userId")).longValue();
        Long subjectId = ((Number) request.get("subjectId")).longValue();
        String chapter = (String) request.get("chapter");
        Boolean isCorrect = (Boolean) request.get("isCorrect");
        Integer timeSpent = (Integer) request.get("timeSpent");

        StudyProgress progress = studyService.updateStudyProgress(userId, subjectId, chapter, isCorrect, timeSpent);
        return ResponseEntity.ok(progress);
    }

    /**
     * 获取用户学习进度
     */
    @GetMapping("/progress/users/{userId}")
    public ResponseEntity<List<StudyProgress>> getUserStudyProgress(@PathVariable Long userId) {
        List<StudyProgress> progress = studyService.getUserStudyProgress(userId);
        return ResponseEntity.ok(progress);
    }

    /**
     * 获取用户在指定学科的进度
     */
    @GetMapping("/progress/users/{userId}/subjects/{subjectId}")
    public ResponseEntity<StudyProgress> getUserSubjectProgress(@PathVariable Long userId,
                                                              @PathVariable Long subjectId) {
        StudyProgress progress = studyService.getUserSubjectProgress(userId, subjectId);
        return ResponseEntity.ok(progress);
    }

    /**
     * 分页查询学习进度
     */
    @GetMapping("/progress")
    public ResponseEntity<Page<StudyProgress>> getStudyProgress(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long subjectId,
            @RequestParam(required = false) String chapter,
            @RequestParam(required = false) StudyProgress.MasteryLevel masteryLevel,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<StudyProgress> progress = studyService.getStudyProgress(userId, subjectId, chapter, masteryLevel, pageable);
        return ResponseEntity.ok(progress);
    }

    // ========== 学习统计分析 ==========

    /**
     * 获取用户学习统计
     */
    @GetMapping("/statistics/users/{userId}")
    public ResponseEntity<Map<String, Object>> getUserStudyStatistics(@PathVariable Long userId) {
        Map<String, Object> statistics = studyService.getUserStudyStatistics(userId);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取用户在指定时间范围内的学习统计
     */
    @GetMapping("/statistics/users/{userId}/range")
    public ResponseEntity<Map<String, Object>> getUserStudyStatistics(
            @PathVariable Long userId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        Map<String, Object> statistics = studyService.getUserStudyStatistics(userId, startDate, endDate);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取用户每日学习统计
     */
    @GetMapping("/statistics/users/{userId}/daily")
    public ResponseEntity<List<Map<String, Object>>> getDailyStudyStatistics(@PathVariable Long userId,
                                                                            @RequestParam(defaultValue = "30") int days) {
        List<Map<String, Object>> statistics = studyService.getDailyStudyStatistics(userId, days);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取用户各学科学习统计
     */
    @GetMapping("/statistics/users/{userId}/subjects")
    public ResponseEntity<List<Map<String, Object>>> getSubjectStudyStatistics(@PathVariable Long userId) {
        List<Map<String, Object>> statistics = studyService.getSubjectStudyStatistics(userId);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取用户总体学习进度
     */
    @GetMapping("/statistics/users/{userId}/overall")
    public ResponseEntity<Map<String, Object>> getOverallProgress(@PathVariable Long userId) {
        Map<String, Object> progress = studyService.getOverallProgress(userId);
        return ResponseEntity.ok(progress);
    }

    /**
     * 获取学习排行榜
     */
    @GetMapping("/statistics/leaderboard")
    public ResponseEntity<List<Map<String, Object>>> getStudyLeaderboard(@RequestParam(defaultValue = "10") int limit) {
        List<Map<String, Object>> leaderboard = studyService.getStudyLeaderboard(limit);
        return ResponseEntity.ok(leaderboard);
    }

    // ========== 学习推荐 ==========

    /**
     * 推荐学习内容
     */
    @GetMapping("/recommendations/users/{userId}/content")
    public ResponseEntity<List<Map<String, Object>>> recommendStudyContent(@PathVariable Long userId) {
        List<Map<String, Object>> recommendations = studyService.recommendStudyContent(userId);
        return ResponseEntity.ok(recommendations);
    }

    /**
     * 推荐复习题目
     */
    @GetMapping("/recommendations/users/{userId}/review")
    public ResponseEntity<List<Map<String, Object>>> recommendReviewQuestions(@PathVariable Long userId,
                                                                             @RequestParam(defaultValue = "20") int limit) {
        List<Map<String, Object>> recommendations = studyService.recommendReviewQuestions(userId, limit);
        return ResponseEntity.ok(recommendations);
    }

    /**
     * 获取用户薄弱知识点
     */
    @GetMapping("/analysis/users/{userId}/weak-points")
    public ResponseEntity<List<Map<String, Object>>> getWeakKnowledgePoints(@PathVariable Long userId) {
        List<Map<String, Object>> weakPoints = studyService.getWeakKnowledgePoints(userId);
        return ResponseEntity.ok(weakPoints);
    }

    /**
     * 获取用户擅长领域
     */
    @GetMapping("/analysis/users/{userId}/strong-areas")
    public ResponseEntity<List<Map<String, Object>>> getStrongAreas(@PathVariable Long userId) {
        List<Map<String, Object>> strongAreas = studyService.getStrongAreas(userId);
        return ResponseEntity.ok(strongAreas);
    }

    // ========== 学习计划 ==========

    /**
     * 生成学习计划
     */
    @PostMapping("/plans/users/{userId}")
    public ResponseEntity<Map<String, Object>> generateStudyPlan(@PathVariable Long userId,
                                                                @RequestBody Map<String, Object> request) {
        Long subjectId = ((Number) request.get("subjectId")).longValue();
        Integer days = (Integer) request.get("days");
        Map<String, Object> plan = studyService.generateStudyPlan(userId, subjectId, days);
        return ResponseEntity.ok(plan);
    }

    /**
     * 获取用户学习计划
     */
    @GetMapping("/plans/users/{userId}")
    public ResponseEntity<List<Map<String, Object>>> getUserStudyPlans(@PathVariable Long userId) {
        List<Map<String, Object>> plans = studyService.getUserStudyPlans(userId);
        return ResponseEntity.ok(plans);
    }

    // ========== 学习分析报告 ==========

    /**
     * 生成学习分析报告
     */
    @PostMapping("/reports/users/{userId}")
    public ResponseEntity<Map<String, Object>> generateStudyReport(@PathVariable Long userId,
                                                                  @RequestBody Map<String, Object> request) {
        LocalDateTime startDate = LocalDateTime.parse((String) request.get("startDate"));
        LocalDateTime endDate = LocalDateTime.parse((String) request.get("endDate"));
        Map<String, Object> report = studyService.generateStudyReport(userId, startDate, endDate);
        return ResponseEntity.ok(report);
    }

    /**
     * 生成班级学习报告
     */
    @PostMapping("/reports/classes/{className}")
    public ResponseEntity<Map<String, Object>> generateClassStudyReport(@PathVariable String className,
                                                                       @RequestBody Map<String, Object> request) {
        LocalDateTime startDate = LocalDateTime.parse((String) request.get("startDate"));
        LocalDateTime endDate = LocalDateTime.parse((String) request.get("endDate"));
        Map<String, Object> report = studyService.generateClassStudyReport(className, startDate, endDate);
        return ResponseEntity.ok(report);
    }

    /**
     * 导出用户学习数据
     */
    @PostMapping("/export/users/{userId}")
    public ResponseEntity<String> exportUserStudyData(@PathVariable Long userId,
                                                     @RequestBody Map<String, Object> request) {
        LocalDateTime startDate = LocalDateTime.parse((String) request.get("startDate"));
        LocalDateTime endDate = LocalDateTime.parse((String) request.get("endDate"));
        String data = studyService.exportUserStudyData(userId, startDate, endDate);
        return ResponseEntity.ok(data);
    }
}
