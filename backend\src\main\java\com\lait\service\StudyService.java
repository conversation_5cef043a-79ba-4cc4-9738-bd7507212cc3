package com.lait.service;

import com.lait.entity.StudyProgress;
import com.lait.entity.StudyRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习服务接口
 */
public interface StudyService {

    // ========== 学习记录管理 ==========

    /**
     * 记录学习行为
     */
    StudyRecord recordStudy(StudyRecord studyRecord);

    /**
     * 批量记录学习行为
     */
    List<StudyRecord> batchRecordStudy(List<StudyRecord> studyRecords);

    /**
     * 获取用户学习记录
     */
    Page<StudyRecord> getUserStudyRecords(Long userId, Pageable pageable);

    /**
     * 获取用户学习记录（带筛选）
     */
    Page<StudyRecord> getStudyRecords(Long userId, Long subjectId, StudyRecord.RecordType recordType,
                                     Boolean isCorrect, LocalDateTime startDate, LocalDateTime endDate,
                                     Pageable pageable);

    /**
     * 获取用户错题记录
     */
    List<StudyRecord> getUserWrongAnswers(Long userId, int limit);

    /**
     * 获取需要复习的题目
     */
    List<StudyRecord> getQuestionsForReview(Long userId, int days);

    // ========== 学习进度管理 ==========

    /**
     * 更新学习进度
     */
    StudyProgress updateStudyProgress(Long userId, Long subjectId, String chapter,
                                     boolean isCorrect, int timeSpent);

    /**
     * 获取用户学习进度
     */
    List<StudyProgress> getUserStudyProgress(Long userId);

    /**
     * 获取用户在指定学科的进度
     */
    StudyProgress getUserSubjectProgress(Long userId, Long subjectId);

    /**
     * 获取用户在指定章节的进度
     */
    StudyProgress getUserChapterProgress(Long userId, Long subjectId, String chapter);

    /**
     * 分页查询学习进度
     */
    Page<StudyProgress> getStudyProgress(Long userId, Long subjectId, String chapter,
                                        StudyProgress.MasteryLevel masteryLevel, Pageable pageable);

    // ========== 学习统计分析 ==========

    /**
     * 获取用户学习统计
     */
    Map<String, Object> getUserStudyStatistics(Long userId);

    /**
     * 获取用户在指定时间范围内的学习统计
     */
    Map<String, Object> getUserStudyStatistics(Long userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取用户每日学习统计
     */
    List<Map<String, Object>> getDailyStudyStatistics(Long userId, int days);

    /**
     * 获取用户各学科学习统计
     */
    List<Map<String, Object>> getSubjectStudyStatistics(Long userId);

    /**
     * 获取用户总体学习进度
     */
    Map<String, Object> getOverallProgress(Long userId);

    /**
     * 获取学习排行榜
     */
    List<Map<String, Object>> getStudyLeaderboard(int limit);

    // ========== 学习推荐 ==========

    /**
     * 推荐学习内容
     */
    List<Map<String, Object>> recommendStudyContent(Long userId);

    /**
     * 推荐复习题目
     */
    List<Map<String, Object>> recommendReviewQuestions(Long userId, int limit);

    /**
     * 获取用户薄弱知识点
     */
    List<Map<String, Object>> getWeakKnowledgePoints(Long userId);

    /**
     * 获取用户擅长领域
     */
    List<Map<String, Object>> getStrongAreas(Long userId);

    // ========== 学习计划 ==========

    /**
     * 生成学习计划
     */
    Map<String, Object> generateStudyPlan(Long userId, Long subjectId, int days);

    /**
     * 更新学习计划进度
     */
    void updateStudyPlanProgress(Long userId, Long planId, Map<String, Object> progress);

    /**
     * 获取用户学习计划
     */
    List<Map<String, Object>> getUserStudyPlans(Long userId);

    // ========== 学习提醒 ==========

    /**
     * 获取需要学习提醒的用户
     */
    List<StudyProgress> getUsersNeedingReminder(int days);

    /**
     * 发送学习提醒
     */
    void sendStudyReminder(Long userId, String message);

    /**
     * 更新学习连续天数
     */
    void updateStudyStreak(Long userId);

    // ========== 学习成就 ==========

    /**
     * 检查学习成就
     */
    List<Map<String, Object>> checkStudyAchievements(Long userId);

    /**
     * 获取用户学习成就
     */
    List<Map<String, Object>> getUserAchievements(Long userId);

    /**
     * 解锁学习成就
     */
    void unlockAchievement(Long userId, String achievementType, Map<String, Object> data);

    // ========== 学习分析报告 ==========

    /**
     * 生成学习分析报告
     */
    Map<String, Object> generateStudyReport(Long userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 生成班级学习报告
     */
    Map<String, Object> generateClassStudyReport(String className, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 生成学科学习报告
     */
    Map<String, Object> generateSubjectStudyReport(Long subjectId, LocalDateTime startDate, LocalDateTime endDate);

    // ========== 数据导出 ==========

    /**
     * 导出用户学习数据
     */
    String exportUserStudyData(Long userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 导出班级学习数据
     */
    String exportClassStudyData(String className, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取学生学习统计信息
     */
    Map<String, Object> getStudentStudyStatistics(Long studentId);
}
