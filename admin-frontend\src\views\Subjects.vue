<template>
  <div class="subjects-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>学科管理</span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增学科
          </el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入学科名称或代码"
              clearable
              @clear="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 学科表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="学科名称" width="150" />
        <el-table-column prop="subjectCode" label="学科代码" width="120" />
        <el-table-column prop="gradeLevel" label="适用年级" width="100" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'info'">
              {{ row.status === 'ACTIVE' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="showEditDialog(row)">
              编辑
            </el-button>
            <el-button
              :type="row.status === 'ACTIVE' ? 'warning' : 'success'"
              size="small"
              @click="toggleSubjectStatus(row)"
            >
              {{ row.status === 'ACTIVE' ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" size="small" @click="deleteSubject(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑学科对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="学科名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="学科代码" prop="subjectCode">
          <el-input v-model="form.subjectCode" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="适用年级" prop="gradeLevel">
          <el-input-number v-model="form.gradeLevel" :min="1" :max="6" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入学科描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getSubjects, createSubject, updateSubject, deleteSubject as deleteSubjectApi, activateSubject, deactivateSubject } from '@/api/subjects'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  name: '',
  subjectCode: '',
  gradeLevel: null,
  description: '',
  sortOrder: 0
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入学科名称', trigger: 'blur' },
    { max: 100, message: '学科名称长度不能超过100个字符', trigger: 'blur' }
  ],
  subjectCode: [
    { required: true, message: '请输入学科代码', trigger: 'blur' },
    { max: 20, message: '学科代码长度不能超过20个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑学科' : '新增学科')

// 方法
const loadSubjects = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      keyword: searchForm.keyword
    }
    const response = await getSubjects(params)
    tableData.value = response.data.content || response.data
    pagination.total = response.data.totalElements || response.data.length
  } catch (error) {
    ElMessage.error('加载学科列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadSubjects()
}

const resetSearch = () => {
  searchForm.keyword = ''
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadSubjects()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadSubjects()
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  isEdit.value = true
  Object.assign(form, row)
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    name: '',
    subjectCode: '',
    gradeLevel: null,
    description: '',
    sortOrder: 0
  })
  formRef.value?.resetFields()
}

const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const data = { ...form }
        if (isEdit.value) {
          await updateSubject(form.id, data)
          ElMessage.success('学科更新成功')
        } else {
          await createSubject(data)
          ElMessage.success('学科创建成功')
        }
        dialogVisible.value = false
        loadSubjects()
      } catch (error) {
        ElMessage.error(isEdit.value ? '学科更新失败' : '学科创建失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

const toggleSubjectStatus = async (row) => {
  try {
    const action = row.status === 'ACTIVE' ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}学科 ${row.name} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (row.status === 'ACTIVE') {
      await deactivateSubject(row.id)
    } else {
      await activateSubject(row.id)
    }
    ElMessage.success(`学科${action}成功`)
    loadSubjects()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const deleteSubject = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除学科 ${row.name} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteSubjectApi(row.id)
    ElMessage.success('学科删除成功')
    loadSubjects()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadSubjects()
})
</script>

<style scoped>
.subjects-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
