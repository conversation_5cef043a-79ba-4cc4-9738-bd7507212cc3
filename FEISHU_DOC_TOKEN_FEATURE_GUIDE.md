# 飞书文档Token功能实现指南

## 🎯 **功能概述**

我已经完成了飞书文档Token相关的三个核心功能：

1. **docToken逻辑调整**: docToken自动从docUrl的最后一段提取
2. **前端显示docToken**: 在列表、新增、编辑页面显示docToken
3. **根据docToken抓取内容**: 文档查看功能根据docToken获取内容

## ✨ **核心功能实现**

### **1. docToken自动提取逻辑**

#### **后端实体类增强**
```java
@Entity
public class FeishuDocument {
    // 自动提取docToken的setter方法
    public void setDocUrl(String docUrl) {
        this.docUrl = docUrl;
        this.docToken = extractDocTokenFromUrl(docUrl);
    }
    
    // 支持的URL格式和对应的token前缀
    private String extractDocTokenFromUrl(String url) {
        // 支持格式：
        // - https://bytedance.feishu.cn/docs/doccnxxxxxx
        // - https://bytedance.feishu.cn/sheets/shtcnxxxxxx  
        // - https://bytedance.feishu.cn/slides/sldcnxxxxxx
        // - https://bytedance.feishu.cn/mindnotes/bmncnxxxxxx
        // - https://bytedance.feishu.cn/base/bascnxxxxxx
    }
}
```

#### **支持的文档类型**
| 文档类型 | Token前缀 | URL示例 |
|---------|----------|---------|
| 飞书文档 | `doccn` | `/docs/doccnxxxxxx` |
| 飞书表格 | `shtcn` | `/sheets/shtcnxxxxxx` |
| 演示文稿 | `sldcn` | `/slides/sldcnxxxxxx` |
| 思维笔记 | `bmncn` | `/mindnotes/bmncnxxxxxx` |
| 多维表格 | `bascn` | `/base/bascnxxxxxx` |
| 知识库 | `wikicn` | `/wiki/wikicnxxxxxx` |
| 文件夹 | `fldcn` | `/folder/fldcnxxxxxx` |

### **2. 前端docToken显示**

#### **文档列表页面 (Feishu.vue)**
- ✅ **新增Token列**: 在文档URL列前显示docToken
- ✅ **Tag样式显示**: 使用Element Plus Tag组件，等宽字体
- ✅ **复制功能**: 点击复制按钮复制token到剪贴板
- ✅ **自动提取**: URL输入时实时提取并显示token

#### **编辑表单增强**
```vue
<el-form-item label="文档URL">
  <el-input v-model="form.docUrl" @input="handleDocUrlChange" />
</el-form-item>

<el-form-item label="文档Token">
  <el-input v-model="form.docToken" readonly>
    <template #append>
      <el-button @click="copyToClipboard(form.docToken)" :icon="CopyDocument" />
    </template>
  </el-input>
</el-form-item>
```

#### **飞书API管理页面 (FeishuApiManager.vue)**
- ✅ **实时提取**: 从API返回的URL中实时提取docToken
- ✅ **一致UI**: 与文档管理页面保持相同的显示风格
- ✅ **复制功能**: 支持token复制操作

### **3. 根据docToken抓取文档内容**

#### **后端API实现**
```java
@RestController
public class FeishuApiController {
    
    @GetMapping("/content-by-token/{docToken}")
    public ResponseEntity<Map<String, Object>> getContentByDocToken(
            @PathVariable String docToken) {
        String content = feishuService.getDocumentContentByToken(docToken);
        // 返回文档内容
    }
}
```

#### **智能API端点选择**
```java
private String getApiEndpointByToken(String docToken) {
    if (docToken.startsWith("doccn")) {
        return "/open-apis/docx/v1/documents/{document_id}/raw_content";
    } else if (docToken.startsWith("shtcn")) {
        return "/open-apis/sheets/v1/spreadsheets/{document_id}/values_batch_get";
    } else if (docToken.startsWith("sldcn")) {
        return "/open-apis/slides/v1/presentations/{document_id}";
    }
    // ... 其他类型
}
```

#### **前端文档查看器增强**
```javascript
const loadContent = async () => {
  // 优先使用docToken获取内容
  if (props.document.docToken) {
    response = await getContentByDocToken(props.document.docToken)
  } else {
    // 降级使用文档ID
    response = await getFeishuDocumentContent(props.document.id)
  }
}
```

## 🔧 **技术特性**

### **1. 自动化处理**
- **实时提取**: URL输入时自动提取token
- **格式验证**: 验证token格式的有效性
- **错误处理**: 提取失败时的优雅降级

### **2. 用户体验优化**
- **可视化反馈**: Token以Tag形式显示，易于识别
- **一键复制**: 所有token都支持一键复制
- **只读显示**: Token字段为只读，防止误修改

### **3. 兼容性保障**
- **向后兼容**: 支持现有的文档ID获取方式
- **降级处理**: docToken失败时自动使用文档ID
- **错误恢复**: 多层错误处理确保功能稳定

## 📋 **界面展示**

### **文档列表表格**
```
| 文档标题 | Token | URL | 学科 | 创建者 | 操作 |
| 项目文档 | [doccnxxxxx] [📋] | https://feishu.cn/docs/... | 数学 | 张三 | [查看][编辑] |
```

### **编辑表单**
```
文档URL: [https://bytedance.feishu.cn/docs/doccnxxxxxx]
         例如: https://bytedance.feishu.cn/docs/doccnxxxxxx

文档Token: [doccnxxxxxx] [📋]
          Token会从URL自动提取，用于API调用
```

### **API管理页面**
```
| 文档名称 | Token | URL | 创建者 | 操作 |
| 需求文档 | [doccnxxxxx] [📋] | https://feishu.cn/... | 李四 | [预览][同步] |
```

## 🚀 **使用流程**

### **1. 创建/编辑文档**
1. 输入飞书文档URL
2. 系统自动提取并显示docToken
3. 保存时token一并存储到数据库

### **2. 查看文档内容**
1. 点击文档列表中的"查看"按钮
2. 系统优先使用docToken调用飞书API
3. 获取并渲染文档内容

### **3. API管理**
1. 从飞书API获取文档列表
2. 实时从URL提取docToken
3. 支持token复制和内容预览

## 🔍 **API接口**

### **新增接口**
```http
GET /feishu-api/content-by-token/{docToken}
```

**响应示例:**
```json
{
  "success": true,
  "content": "# 文档标题\n\n文档内容...",
  "docToken": "doccnxxxxxx",
  "timestamp": "2024-01-01T12:00:00"
}
```

### **支持的文档类型API**
- **doccn**: 飞书文档 → `/open-apis/docx/v1/documents/{id}/raw_content`
- **shtcn**: 飞书表格 → `/open-apis/sheets/v1/spreadsheets/{id}/values_batch_get`
- **sldcn**: 演示文稿 → `/open-apis/slides/v1/presentations/{id}`
- **bmncn**: 思维笔记 → `/open-apis/mindnote/v1/spaces/{id}/nodes`
- **bascn**: 多维表格 → `/open-apis/bitable/v1/apps/{id}/tables`

## 🛡️ **错误处理**

### **1. Token提取失败**
- 显示"未提取"状态
- 不影响其他功能正常使用
- 可手动输入或重新设置URL

### **2. API调用失败**
- 自动降级到文档ID方式
- 返回模拟内容作为备选
- 详细的错误日志记录

### **3. 格式验证**
- 验证token前缀格式
- 检查token长度要求
- 过滤无效的URL格式

## 📊 **数据流程**

### **Token提取流程**
```
用户输入URL → 自动提取token → 格式验证 → 存储到数据库
```

### **内容获取流程**
```
查看文档 → 检查docToken → 调用对应API → 解析内容 → 渲染显示
```

### **降级处理流程**
```
docToken失败 → 尝试文档ID → 仍失败 → 返回模拟内容
```

## 🎨 **样式设计**

### **Token标签样式**
```css
.doc-token-tag {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}
```

### **复制按钮样式**
```css
.copy-btn {
  flex-shrink: 0;
  padding: 4px;
  min-height: auto;
}
```

## 🔧 **配置说明**

### **环境变量**
```bash
# 飞书API配置（用于token内容获取）
FEISHU_APP_ID=cli_xxxxxx
FEISHU_APP_SECRET=xxxxxx
```

### **数据库字段**
```sql
-- 确保docToken字段存在
ALTER TABLE feishu_documents 
ADD COLUMN doc_token VARCHAR(100) NULL 
COMMENT '文档Token，从URL自动提取';
```

通过这些功能增强，飞书文档管理系统现在具备了完整的Token管理能力，提供了更高效、更直观的文档操作体验。用户可以通过Token快速识别文档类型，复制Token用于API调用，并享受基于Token的快速内容获取功能。
