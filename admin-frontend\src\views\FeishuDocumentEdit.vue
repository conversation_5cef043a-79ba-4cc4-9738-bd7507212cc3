<template>
  <div class="document-edit-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button
          :icon="ArrowLeft"
          @click="goBack"
          type="text"
          size="large"
        >
          返回
        </el-button>
        <el-divider direction="vertical" />
        <h1 v-if="document">编辑文档: {{ document.title }}</h1>
        <el-skeleton v-else animated>
          <template #template>
            <el-skeleton-item variant="h1" style="width: 300px" />
          </template>
        </el-skeleton>
      </div>
      <div class="header-right">
        <el-button
          type="success"
          :icon="Check"
          @click="saveDocument"
          :loading="saving"
        >
          保存文档
        </el-button>
        <el-button
          v-if="document"
          type="primary"
          :icon="View"
          @click="previewDocument"
        >
          预览
        </el-button>
        <el-button
          v-if="document"
          type="success"
          :icon="Download"
          @click="exportToPdf"
          :loading="exportingPdf"
        >
          导出PDF
        </el-button>
      </div>
    </div>

    <!-- 编辑器容器 -->
    <div class="editor-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton animated>
          <template #template>
            <el-skeleton-item variant="rect" style="height: 400px" />
          </template>
        </el-skeleton>
      </div>

      <div v-else-if="error" class="error-container">
        <el-result
          icon="error"
          :title="error"
          sub-title="请检查文档ID是否正确，或稍后重试"
        >
          <template #extra>
            <el-button type="primary" @click="loadDocument">重新加载</el-button>
            <el-button @click="goBack">返回</el-button>
          </template>
        </el-result>
      </div>

      <div v-else-if="document" class="editor-wrapper">
        <!-- 编辑模式选择 -->
        <div class="editor-toolbar">
          <el-radio-group v-model="editMode" @change="handleModeChange">
            <el-radio-button label="markdown">Markdown编辑器</el-radio-button>
            <el-radio-button label="preview">预览</el-radio-button>
          </el-radio-group>

          <div class="toolbar-actions">
            <el-button
              size="small"
              :icon="Refresh"
              @click="resetContent"
              title="重置内容"
            >
              重置
            </el-button>
            <el-button
              size="small"
              :icon="DocumentCopy"
              @click="copyContent"
              title="复制内容"
            >
              复制
            </el-button>
          </div>
        </div>

        <!-- 编辑器主体 -->
        <div class="editor-main">
          <!-- Markdown编辑器 -->
          <div v-if="editMode === 'markdown'" class="editor-panel">
            <div class="bytemd-wrapper">
              <Editor
                :value="editContent"
                :plugins="markdownPlugins"
                @change="handleMarkdownChange"
                :locale="locale"
                class="markdown-editor-component"
              />
            </div>
          </div>

          <!-- 预览面板 -->
          <div v-else-if="editMode === 'preview'" class="preview-panel">
            <!-- 预览工具栏 -->
            <div class="preview-toolbar">
              <div class="preview-mode-switch">
                <el-radio-group v-model="pdfPreviewMode" size="small">
                  <el-radio-button :label="false">普通预览</el-radio-button>
                  <el-radio-button :label="true">PDF分页预览</el-radio-button>
                </el-radio-group>
              </div>

              <!-- PDF分页控制 -->
              <div v-if="pdfPreviewMode" class="pdf-pagination">
                <el-button
                  size="small"
                  :disabled="currentPage <= 1"
                  @click="currentPage--"
                >
                  上一页
                </el-button>
                <span class="page-info">
                  {{ currentPage }} / {{ totalPages }}
                </span>
                <el-button
                  size="small"
                  :disabled="currentPage >= totalPages"
                  @click="currentPage++"
                >
                  下一页
                </el-button>
              </div>

              <div class="preview-actions">
                <el-button
                  size="small"
                  type="info"
                  @click="createTestContent"
                >
                  测试内容
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  @click="generatePdfPages"
                >
                  重新分页
                </el-button>
                <el-button
                  size="small"
                  type="warning"
                  :icon="Printer"
                  @click="openPrintPreview"
                >
                  打印预览
                </el-button>
                <el-button
                  size="small"
                  type="success"
                  :icon="Download"
                  @click="exportToPdf"
                  :loading="exportingPdf"
                >
                  导出PDF
                </el-button>
              </div>
            </div>

            <!-- 预览内容 -->
            <div class="preview-content-wrapper">
              <!-- 普通预览 -->
              <div v-if="!pdfPreviewMode" class="preview-content" v-html="previewContent"></div>

              <!-- PDF分页预览 -->
              <div v-else class="pdf-preview-container">
                <!-- 调试信息 -->
                <div class="debug-info" style="margin-bottom: 10px; padding: 10px; background: #f0f0f0; border-radius: 4px; font-size: 12px;">
                  <div>总页数: {{ totalPages }}</div>
                  <div>当前页: {{ currentPage }}</div>
                  <div>页面数组长度: {{ pdfPages.length }}</div>
                  <div>页面尺寸: {{ pageWidth }}×{{ pageHeight }}</div>
                </div>

                <div class="pdf-page-container">
                  <div
                    v-for="(page, index) in pdfPages"
                    :key="index"
                    v-show="index + 1 === currentPage"
                    class="pdf-page a4-page"
                    :style="{
                      width: pageWidth + 'px',
                      height: pageHeight + 'px'
                    }"
                  >
                    <div class="page-content" v-html="page.content"></div>
                    <div class="page-number">
                      第 {{ currentPage }} 页 / 共 {{ pdfPages.length }} 页
                    </div>
                  </div>

                  <!-- 如果没有页面数据，显示提示 -->
                  <div v-if="pdfPages.length === 0" class="no-pages">
                    <el-empty description="暂无分页内容，请点击重新分页"></el-empty>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文档预览"
      width="80%"
      :before-close="closePreviewDialog"
    >
      <div class="preview-dialog-content">
        <div class="preview-header">
          <h2>{{ document?.title }}</h2>
          <div class="preview-meta">
            <el-tag type="primary" size="small">{{ getDocTypeText(document?.docType) }}</el-tag>
            <span class="meta-item">最后更新: {{ formatDateTime(new Date()) }}</span>
          </div>
        </div>
        <el-divider />
        <div class="preview-body" v-html="previewContent"></div>
      </div>
      <template #footer>
        <el-button @click="closePreviewDialog">关闭</el-button>
        <el-button type="primary" @click="saveDocument" :loading="saving">
          保存并关闭
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Check,
  View,
  Refresh,
  DocumentCopy,
  Download,
  Printer
} from '@element-plus/icons-vue'
import {
  getFeishuDocument,
  updateFeishuDocumentContent
} from '@/api/feishu'
import { marked } from 'marked'

// 编辑器相关导入
import { Editor } from '@bytemd/vue-next'
import gfm from '@bytemd/plugin-gfm'
import highlight from '@bytemd/plugin-highlight'

// PDF相关导入
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

// 样式导入
import 'bytemd/dist/index.css'
import 'highlight.js/styles/default.css'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const document = ref(null)
const error = ref('')
const editMode = ref('markdown')
const editContent = ref('')
const originalContent = ref('')
const hasUnsavedChanges = ref(false)
const previewDialogVisible = ref(false)
const exportingPdf = ref(false)

// PDF预览相关
const pdfPreviewMode = ref(false)
const currentPage = ref(1)
const totalPages = ref(1)
const pdfPages = ref([])
const pageHeight = ref(842) // A4页面高度 (px)
const pageWidth = ref(595) // A4页面宽度 (px)

// 编辑器相关 - 只保留Markdown编辑器

// Markdown编辑器配置
const markdownPlugins = [
  gfm(),
  highlight()
]

const locale = {
  // 中文本地化配置
  toolbar: {
    bold: '粗体',
    italic: '斜体',
    strikethrough: '删除线',
    heading: '标题',
    code: '代码',
    quote: '引用',
    link: '链接',
    image: '图片',
    table: '表格',
    list: '列表',
    ordered_list: '有序列表',
    unordered_list: '无序列表',
    task_list: '任务列表',
    fullscreen: '全屏',
    preview: '预览',
    write: '编辑',
    toc: '目录',
    help: '帮助'
  }
}

// 计算属性
const previewContent = computed(() => {
  if (!editContent.value) return ''

  try {
    if (editMode.value === 'html') {
      return editContent.value
    } else {
      // Markdown模式
      marked.setOptions({
        breaks: true,
        gfm: true,
        headerIds: false,
        mangle: false
      })
      return marked(editContent.value)
    }
  } catch (err) {
    console.error('内容渲染失败:', err)
    return `<pre>${editContent.value}</pre>`
  }
})

// 方法
const loadDocument = async () => {
  const documentId = route.params.id
  if (!documentId) {
    error.value = '文档ID参数缺失'
    return
  }

  loading.value = true
  error.value = ''

  try {
    console.log('加载文档信息:', documentId)
    const response = await getFeishuDocument(documentId)

    if (response) {
      document.value = response
      editContent.value = response.content || ''
      originalContent.value = response.content || ''
      console.log('文档信息加载成功:', document.value)
    } else {
      error.value = '文档数据格式异常'
    }
  } catch (err) {
    console.error('加载文档信息失败:', err)
    error.value = err.response?.data?.message || err.message || '加载文档信息失败'
  } finally {
    loading.value = false
  }
}

const saveDocument = async () => {
  if (!document.value) {
    ElMessage.warning('文档信息不存在')
    return
  }

  if (!editContent.value.trim()) {
    ElMessage.warning('文档内容不能为空')
    return
  }

  saving.value = true

  try {
    await updateFeishuDocumentContent(document.value.id, editContent.value)
    originalContent.value = editContent.value
    hasUnsavedChanges.value = false
    ElMessage.success('文档保存成功')

    if (previewDialogVisible.value) {
      closePreviewDialog()
    }
  } catch (error) {
    console.error('保存文档失败:', error)
    ElMessage.error('保存文档失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

const goBack = async () => {
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要离开吗？',
        '确认离开',
        {
          confirmButtonText: '离开',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return
    }
  }

  router.push('/feishu')
}

const previewDocument = () => {
  previewDialogVisible.value = true
}

const closePreviewDialog = () => {
  previewDialogVisible.value = false
}

const handleModeChange = (mode) => {
  console.log('编辑模式切换:', mode, '当前模式:', editMode.value)
  // 简化的模式切换，只在Markdown和预览之间切换
  console.log('编辑模式切换完成:', mode)
}

// Markdown编辑器变化处理
const handleMarkdownChange = (value) => {
  editContent.value = value
  handleContentChange()
}

// Monaco编辑器相关方法已移除，只保留Markdown编辑器

// A4分页预览处理
const generatePdfPages = async () => {
  console.log('开始生成A4分页预览...')

  if (!previewContent.value) {
    console.log('预览内容为空')
    pdfPages.value = [{
      content: '<p>暂无内容</p>',
      pageNumber: 1
    }]
    currentPage.value = 1
    return
  }

  try {
    // A4纸张尺寸 (以像素为单位，96 DPI)
    const A4_WIDTH = 794  // 210mm ≈ 794px
    const A4_HEIGHT = 1123 // 297mm ≈ 1123px
    const PAGE_MARGIN = 60 // 页边距
    const CONTENT_HEIGHT = A4_HEIGHT - (PAGE_MARGIN * 2) // 可用内容高度

    // 更新页面尺寸
    pageWidth.value = A4_WIDTH
    pageHeight.value = A4_HEIGHT

    // 创建临时容器用于测量内容高度
    const measureContainer = document.createElement('div')
    measureContainer.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: ${A4_WIDTH - (PAGE_MARGIN * 2)}px;
      padding: ${PAGE_MARGIN}px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      font-size: 14px;
      line-height: 1.6;
      color: #333;
      background: white;
      box-sizing: border-box;
      overflow: visible;
    `

    // 添加文档头部
    const headerHtml = `
      <div style="text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #3498db;">
        <h1 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 24px;">
          ${document.value?.title || '飞书文档'}
        </h1>
        <div style="font-size: 12px; color: #666;">
          <div style="margin: 3px 0;">类型: ${getDocTypeText(document.value?.docType)}</div>
          <div style="margin: 3px 0;">创建者: ${document.value?.creatorName || '未知'}</div>
          <div style="margin: 3px 0;">生成时间: ${new Date().toLocaleString('zh-CN')}</div>
        </div>
      </div>
    `

    // 设置完整内容
    measureContainer.innerHTML = headerHtml + previewContent.value
    document.body.appendChild(measureContainer)

    // 等待DOM渲染
    await new Promise(resolve => setTimeout(resolve, 100))

    // 获取总内容高度
    const totalHeight = measureContainer.scrollHeight
    console.log('总内容高度:', totalHeight, 'px')
    console.log('单页可用高度:', CONTENT_HEIGHT, 'px')

    // 计算需要的页数
    const totalPages = Math.max(1, Math.ceil(totalHeight / CONTENT_HEIGHT))
    console.log('预计页数:', totalPages)

    const pages = []

    if (totalPages === 1) {
      // 单页内容
      pages.push({
        content: measureContainer.innerHTML,
        pageNumber: 1
      })
    } else {
      // 多页内容 - 简化分页策略
      const contentPerPage = Math.ceil(previewContent.value.length / totalPages)

      for (let i = 0; i < totalPages; i++) {
        const start = i * contentPerPage
        const end = Math.min(start + contentPerPage, previewContent.value.length)
        let pageContent = previewContent.value.slice(start, end)

        // 为第一页添加头部
        if (i === 0) {
          pageContent = headerHtml + pageContent
        }

        // 确保页面内容不为空
        if (pageContent.trim()) {
          pages.push({
            content: pageContent,
            pageNumber: i + 1
          })
        }
      }
    }

    // 清理临时容器
    document.body.removeChild(measureContainer)

    // 如果没有生成任何页面，创建一个默认页面
    if (pages.length === 0) {
      pages.push({
        content: headerHtml + (previewContent.value || '<p>暂无内容</p>'),
        pageNumber: 1
      })
    }

    pdfPages.value = pages
    currentPage.value = 1

    console.log(`A4分页完成，共 ${pages.length} 页`)
    ElMessage.success(`A4分页完成，共生成 ${pages.length} 页`)
  } catch (error) {
    console.error('A4分页处理失败:', error)

    // 降级处理
    pdfPages.value = [{
      content: previewContent.value || '<p>内容加载失败</p>',
      pageNumber: 1
    }]
    currentPage.value = 1

    ElMessage.error('分页处理失败，已降级为单页显示')
  }
}

// PDF导出功能
const exportToPdf = async () => {
  if (!editContent.value) {
    ElMessage.warning('文档内容为空，无法导出PDF')
    return
  }

  exportingPdf.value = true

  try {
    // 检查浏览器环境
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      throw new Error('当前环境不支持浏览器API，无法导出PDF')
    }

    // 检查DOM API
    if (!document.createElement || !document.body) {
      throw new Error('DOM API不完整，无法导出PDF')
    }

    // 检查必要的库是否加载
    if (!window.jsPDF) {
      throw new Error('jsPDF库未正确加载，请刷新页面重试')
    }

    if (!window.html2canvas) {
      throw new Error('html2canvas库未正确加载，请刷新页面重试')
    }

    console.log('开始PDF导出...')

    // 创建PDF文档
    const pdf = new window.jsPDF({
      orientation: 'portrait',
      unit: 'pt',
      format: 'a4',
      compress: true
    })

    console.log('PDF文档创建成功')

    // 创建用于PDF生成的临时容器
    let pdfContainer = null
    try {
      pdfContainer = document.createElement('div')
      console.log('临时容器创建成功')
    } catch (error) {
      throw new Error('无法创建临时DOM容器: ' + error.message)
    }

    // 设置容器样式
    pdfContainer.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: ${pageWidth.value}px;
      background: white;
      padding: 40px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      box-sizing: border-box;
    `

    // 添加文档头部信息
    const headerHtml = `
      <div style="text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #3498db;">
        <h1 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 24px;">
          ${document.value?.title || '飞书文档'}
        </h1>
        <div style="font-size: 14px; color: #666;">
          <div style="margin: 5px 0;">类型: ${getDocTypeText(document.value?.docType)}</div>
          <div style="margin: 5px 0;">创建者: ${document.value?.creatorName || '未知'}</div>
          <div style="margin: 5px 0;">导出时间: ${new Date().toLocaleString('zh-CN')}</div>
        </div>
      </div>
    `

    // 设置容器内容
    try {
      pdfContainer.innerHTML = headerHtml + (previewContent.value || '')
      console.log('容器内容设置成功')
    } catch (error) {
      throw new Error('设置容器内容失败: ' + error.message)
    }

    // 添加到DOM
    try {
      document.body.appendChild(pdfContainer)
      console.log('容器添加到DOM成功')
    } catch (error) {
      throw new Error('添加容器到DOM失败: ' + error.message)
    }

    // 等待DOM更新
    await new Promise(resolve => setTimeout(resolve, 100))

    // 使用html2canvas生成图片
    let canvas = null
    try {
      console.log('开始生成canvas...')
      canvas = await window.html2canvas(pdfContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: pageWidth.value,
        height: pdfContainer.scrollHeight,
        logging: false,
        onclone: () => {
          console.log('Canvas克隆文档成功')
        }
      })
      console.log('Canvas生成成功，尺寸:', canvas.width, 'x', canvas.height)
    } catch (error) {
      throw new Error('生成canvas失败: ' + error.message)
    }

    // 计算分页
    const imgWidth = pageWidth.value
    const imgHeight = canvas.height * (pageWidth.value / canvas.width)
    const pageHeight = 842 // A4高度
    const totalPages = Math.ceil(imgHeight / pageHeight)

    console.log('PDF分页信息:', { imgWidth, imgHeight, pageHeight, totalPages })

    // 添加页面到PDF
    try {
      for (let i = 0; i < totalPages; i++) {
        if (i > 0) {
          pdf.addPage()
        }

        const yOffset = -(i * pageHeight)
        const imageData = canvas.toDataURL('image/jpeg', 0.95)

        pdf.addImage(
          imageData,
          'JPEG',
          0,
          yOffset,
          imgWidth,
          imgHeight
        )

        // 添加页码
        pdf.setFontSize(10)
        pdf.setTextColor(128, 128, 128)
        pdf.text(
          `第 ${i + 1} 页 / 共 ${totalPages} 页`,
          imgWidth - 100,
          pageHeight - 20
        )
      }
      console.log('PDF页面添加成功')
    } catch (error) {
      throw new Error('添加PDF页面失败: ' + error.message)
    }

    // 清理临时容器
    try {
      if (pdfContainer && pdfContainer.parentNode) {
        document.body.removeChild(pdfContainer)
        console.log('临时容器清理成功')
      }
    } catch (error) {
      console.warn('清理临时容器失败:', error)
    }

    // 保存PDF
    try {
      const fileName = `${document.value?.title || '飞书文档'}_${new Date().toISOString().slice(0, 10)}.pdf`
      pdf.save(fileName)
      console.log('PDF保存成功:', fileName)
    } catch (error) {
      throw new Error('保存PDF失败: ' + error.message)
    }

    ElMessage.success('PDF导出成功')
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败: ' + error.message)
  } finally {
    exportingPdf.value = false
  }
}

const handleContentChange = () => {
  hasUnsavedChanges.value = editContent.value !== originalContent.value
}

const resetContent = async () => {
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '确定要重置内容吗？这将丢失所有未保存的更改。',
        '确认重置',
        {
          confirmButtonText: '重置',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return
    }
  }

  editContent.value = originalContent.value
  hasUnsavedChanges.value = false
  ElMessage.success('内容已重置')
}

const copyContent = async () => {
  try {
    await navigator.clipboard.writeText(editContent.value)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 创建测试内容
const createTestContent = () => {
  const testContent = `# 测试文档标题

这是一个用于测试PDF分页功能的文档。

## 第一章 介绍

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

### 1.1 背景

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

### 1.2 目标

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.

## 第二章 详细内容

Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.

### 2.1 技术实现

- 第一个要点：使用Vue 3 Composition API
- 第二个要点：集成ByteMD编辑器
- 第三个要点：实现PDF分页预览
- 第四个要点：支持高质量PDF导出

### 2.2 功能特性

1. **Markdown编辑器**: 专业的Markdown编辑环境
2. **实时预览**: 所见即所得的预览效果
3. **PDF分页**: 智能的分页算法
4. **高质量导出**: 生成专业的PDF文档
5. **打印预览**: 专业的打印预览功能

## 第三章 使用说明

### 3.1 基本操作

要使用PDF分页预览功能，请按照以下步骤操作：

1. 切换到预览模式
2. 选择"PDF分页预览"
3. 使用导航按钮浏览各页
4. 点击"打印预览"查看打印效果
5. 点击"导出PDF"生成文档

### 3.2 高级功能

#### 3.2.1 编辑器功能

系统支持专业的Markdown编辑：
- Markdown编辑器模式
- 实时预览模式
- PDF分页预览
- 打印预览功能

#### 3.2.2 内容格式化

支持丰富的Markdown语法：

\`\`\`javascript
// 代码块示例
function generatePdfPages() {
  console.log('开始生成PDF分页...');
  // 分页逻辑
}
\`\`\`

#### 3.2.3 表格支持

| 功能 | 状态 | 说明 |
|------|------|------|
| Markdown编辑 | ✅ | 完全支持 |
| 实时预览 | ✅ | 完全支持 |
| PDF预览 | ✅ | 分页显示 |
| 打印预览 | ✅ | 新增功能 |
| PDF导出 | ✅ | 高质量输出 |

## 第四章 总结

本文档演示了完整的文档编辑和预览功能。通过智能的分页算法和打印预览，用户可以：

- 实时预览文档的分页效果
- 查看专业的打印预览
- 精确控制内容的页面布局
- 生成高质量的PDF文档
- 享受专业的编辑体验

### 4.1 技术优势

1. **性能优化**: 高效的分页算法
2. **用户体验**: 直观的操作界面
3. **功能完整**: 从编辑到导出的完整流程
4. **质量保证**: 专业级的PDF输出
5. **打印支持**: 完整的打印预览功能

### 4.2 未来展望

后续版本将继续优化：
- 更智能的分页算法
- 更丰富的编辑功能
- 更好的用户体验
- 更多的导出格式
- 增强的打印功能

---

**注意**: 这是一个测试文档，用于验证PDF分页和打印预览功能的正确性。实际使用时，请根据具体需求调整内容和格式。

© 2024 飞书文档管理系统`

  editContent.value = testContent
  ElMessage.success('已创建测试内容，可以测试分页和打印预览功能了')
}

// 打印预览功能
const openPrintPreview = () => {
  if (!previewContent.value) {
    ElMessage.warning('预览内容为空，无法打印')
    return
  }

  try {
    // 创建打印预览窗口
    const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes')

    if (!printWindow) {
      ElMessage.error('无法打开打印预览窗口，请检查浏览器弹窗设置')
      return
    }

    // 构建打印页面HTML
    const printHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印预览 - ${document.value?.title || '飞书文档'}</title>
    <style>
        /* 打印样式 */
        @media print {
            @page {
                margin: 2cm;
                size: A4;
            }

            body {
                margin: 0;
                padding: 0;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                line-height: 1.6;
                color: #000;
                background: white;
            }

            .no-print {
                display: none !important;
            }

            .page-break {
                page-break-before: always;
            }
        }

        /* 屏幕样式 */
        @media screen {
            body {
                margin: 0;
                padding: 20px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background: #f5f5f5;
            }

            .print-container {
                max-width: 210mm;
                margin: 0 auto;
                background: white;
                padding: 20mm;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                border-radius: 4px;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #3498db;
            }

            .print-header h1 {
                margin: 0 0 15px 0;
                color: #2c3e50;
                font-size: 24px;
            }

            .print-meta {
                font-size: 14px;
                color: #666;
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                gap: 10px;
            }

            .print-actions {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }

            .print-btn {
                background: #409eff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 10px;
                font-size: 14px;
            }

            .print-btn:hover {
                background: #337ecc;
            }

            .close-btn {
                background: #909399;
            }

            .close-btn:hover {
                background: #767a82;
            }
        }

        /* 内容样式 */
        .print-content h1 {
            font-size: 28px;
            font-weight: 600;
            margin: 30px 0 20px 0;
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .print-content h2 {
            font-size: 24px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            color: #34495e;
        }

        .print-content h3 {
            font-size: 20px;
            font-weight: 600;
            margin: 20px 0 12px 0;
            color: #34495e;
        }

        .print-content p {
            margin: 0 0 15px 0;
            text-align: justify;
        }

        .print-content ul, .print-content ol {
            margin: 0 0 15px 0;
            padding-left: 25px;
        }

        .print-content li {
            margin: 5px 0;
        }

        .print-content blockquote {
            margin: 15px 0;
            padding: 10px 15px;
            background: #f8f9fa;
            border-left: 4px solid #409eff;
            color: #666;
        }

        .print-content code {
            background: #f1f2f3;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }

        .print-content pre {
            background: #f6f8fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 15px 0;
            border: 1px solid #e1e4e8;
        }

        .print-content pre code {
            background: none;
            padding: 0;
            font-size: 14px;
            line-height: 1.45;
        }

        .print-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        .print-content th, .print-content td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }

        .print-content th {
            background: #f5f5f5;
            font-weight: 600;
        }

        .print-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 12px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="print-actions no-print">
        <button class="print-btn" onclick="window.print()">打印文档</button>
        <button class="print-btn close-btn" onclick="window.close()">关闭预览</button>
    </div>

    <div class="print-container">
        <div class="print-header">
            <h1>${document.value?.title || '飞书文档'}</h1>
            <div class="print-meta">
                <span>文档类型: ${getDocTypeText(document.value?.docType)}</span>
                <span>创建者: ${document.value?.creatorName || '未知'}</span>
                <span>打印时间: ${new Date().toLocaleString('zh-CN')}</span>
            </div>
        </div>

        <div class="print-content">
            ${previewContent.value}
        </div>

        <div class="print-footer">
            <p>本文档由飞书文档管理系统生成 © 2024</p>
        </div>
    </div>
</body>
</html>`

    // 写入HTML内容
    printWindow.document.write(printHtml)
    printWindow.document.close()

    // 等待内容加载完成后聚焦窗口
    printWindow.onload = () => {
      printWindow.focus()
    }

    ElMessage.success('打印预览已打开')
  } catch (error) {
    console.error('打印预览失败:', error)
    ElMessage.error('打印预览失败: ' + error.message)
  }
}

// 工具方法
const getDocTypeText = (type) => {
  const texts = {
    DOC: '文档',
    SHEET: '表格',
    SLIDE: '演示文稿',
    MINDMAP: '思维导图',
    BITABLE: '多维表格'
  }
  return texts[type] || type
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 监听内容变化
watch(editContent, () => {
  handleContentChange()
})

// 监听PDF预览模式变化
watch(pdfPreviewMode, (newValue) => {
  if (newValue && previewContent.value) {
    nextTick(() => {
      generatePdfPages()
    })
  }
})

// 监听预览内容变化
watch(previewContent, () => {
  if (pdfPreviewMode.value) {
    nextTick(() => {
      generatePdfPages()
    })
  }
})

// 页面离开前确认
window.addEventListener('beforeunload', (e) => {
  if (hasUnsavedChanges.value) {
    e.preventDefault()
    e.returnValue = ''
  }
})

// 生命周期
onMounted(() => {
  loadDocument()
})

// Monaco编辑器清理方法已移除

onUnmounted(() => {
  console.log('组件卸载，清理资源...')
  // 不再需要清理Monaco编辑器
})
</script>

<style scoped>
.document-edit-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  max-width: 500px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-right {
  display: flex;
  gap: 10px;
}

.editor-container {
  flex: 1;
  overflow: hidden;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-container {
  padding: 20px;
}

.error-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.editor-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}

.editor-main {
  flex: 1;
  overflow: hidden;
  display: flex;
}

.editor-panel {
  flex: 1;
  padding: 20px;
}

.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
  flex-shrink: 0;
}

.preview-mode-switch {
  display: flex;
  align-items: center;
}

.pdf-pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-info {
  font-size: 14px;
  color: #606266;
  margin: 0 10px;
  min-width: 60px;
  text-align: center;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.preview-content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.preview-content {
  line-height: 1.6;
  color: #333;
}

/* PDF预览样式 */
.pdf-preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.pdf-page-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 40px 20px;
  overflow-y: auto;
}

/* A4页面样式 */
.pdf-page.a4-page {
  position: relative;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 0 auto;
  box-sizing: border-box;

  /* A4纸张尺寸 (794px × 1123px) */
  width: 794px !important;
  height: 1123px !important;
}

.pdf-page.a4-page .page-content {
  padding: 60px;
  height: calc(100% - 40px); /* 为页码留出空间 */
  overflow: hidden;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  box-sizing: border-box;

  /* 文本处理 */
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* A4页面内容样式 */
.pdf-page.a4-page .page-content h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #2c3e50;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
}

.pdf-page.a4-page .page-content h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 20px 0 15px 0;
  color: #34495e;
}

.pdf-page.a4-page .page-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 15px 0 10px 0;
  color: #34495e;
}

.pdf-page.a4-page .page-content p {
  margin: 0 0 12px 0;
  text-align: justify;
}

.pdf-page.a4-page .page-content ul,
.pdf-page.a4-page .page-content ol {
  margin: 0 0 12px 0;
  padding-left: 20px;
}

.pdf-page.a4-page .page-content li {
  margin: 4px 0;
}

.pdf-page.a4-page .page-content code {
  background: #f1f2f3;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.pdf-page.a4-page .page-content pre {
  background: #f6f8fa;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
  border: 1px solid #e1e4e8;
}

.pdf-page.a4-page .page-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  font-size: 13px;
}

.pdf-page.a4-page .page-content th,
.pdf-page.a4-page .page-content td {
  border: 1px solid #ddd;
  padding: 6px 10px;
  text-align: left;
}

.pdf-page.a4-page .page-content th {
  background: #f5f5f5;
  font-weight: 600;
}

.pdf-page.a4-page .page-number {
  position: absolute;
  bottom: 20px;
  right: 60px;
  font-size: 12px;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #eee;
}

.no-pages {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #999;
}

.split-view .editor-panel {
  flex: 1;
  border-right: 1px solid #ebeef5;
}

.split-view .preview-panel {
  flex: 1;
}

/* 编辑器样式 */
.bytemd-wrapper {
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.markdown-editor-component {
  height: 100%;
}

/* Monaco编辑器样式已移除 */

/* 分屏编辑样式已移除 */

/* 传统编辑器样式已移除，只使用ByteMD编辑器 */

.preview-content {
  line-height: 1.6;
  color: #333;
}

/* Markdown预览样式 */
.preview-content :deep(h1) {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee;
  color: #2c3e50;
}

.preview-content :deep(h2) {
  font-size: 24px;
  font-weight: 600;
  margin: 30px 0 15px 0;
  color: #34495e;
}

.preview-content :deep(h3) {
  font-size: 20px;
  font-weight: 600;
  margin: 25px 0 12px 0;
  color: #34495e;
}

.preview-content :deep(p) {
  margin: 0 0 15px 0;
  text-align: justify;
}

.preview-content :deep(ul),
.preview-content :deep(ol) {
  margin: 0 0 15px 0;
  padding-left: 25px;
}

.preview-content :deep(li) {
  margin: 5px 0;
}

.preview-content :deep(blockquote) {
  margin: 15px 0;
  padding: 10px 15px;
  background: #f8f9fa;
  border-left: 4px solid #409eff;
  color: #666;
}

.preview-content :deep(code) {
  background: #f1f2f3;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.preview-content :deep(pre) {
  background: #f6f8fa;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 15px 0;
  border: 1px solid #e1e4e8;
}

.preview-content :deep(pre code) {
  background: none;
  padding: 0;
  font-size: 14px;
  line-height: 1.45;
}

.preview-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.preview-content :deep(th),
.preview-content :deep(td) {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.preview-content :deep(th) {
  background: #f5f5f5;
  font-weight: 600;
}

/* 预览对话框样式 */
.preview-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.preview-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.meta-item {
  color: #909399;
  font-size: 14px;
}

.preview-body {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .header-left {
    width: 100%;
  }

  .header-left h1 {
    max-width: none;
    font-size: 18px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .editor-container {
    margin: 10px;
  }

  .editor-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .toolbar-actions {
    width: 100%;
    justify-content: flex-end;
  }

  /* 分屏编辑响应式样式已移除 */

  /* PDF预览响应式 */
  .preview-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .pdf-pagination {
    width: 100%;
    justify-content: center;
  }

  .preview-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .pdf-page {
    width: 100% !important;
    max-width: 350px;
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
  }

  .pdf-page-content {
    height: auto;
    min-height: 400px;
  }

  .pdf-preview-container {
    padding: 10px;
  }
}
</style>

<style>
/* ByteMD编辑器全局样式覆盖 */
.bytemd {
  height: 100% !important;
}

.bytemd-toolbar {
  border-bottom: 1px solid #dcdfe6 !important;
  background: #fafafa !important;
}

.bytemd-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
}

.bytemd-preview {
  padding: 15px !important;
}

.bytemd-split {
  border: none !important;
}

.bytemd-toolbar-icon {
  color: #606266 !important;
}

.bytemd-toolbar-icon:hover {
  color: #409eff !important;
  background: #ecf5ff !important;
}

/* Monaco编辑器主题样式已移除 */
</style>
