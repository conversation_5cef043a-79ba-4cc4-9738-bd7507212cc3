# 飞书文档查看功能使用指南

## 功能概述

飞书文档查看功能允许用户在系统内直接查看飞书文档的内容，支持Markdown格式渲染，提供良好的阅读体验。

## 主要特性

### 1. 文档内容展示
- **Markdown渲染**: 支持完整的Markdown语法，包括标题、列表、代码块、表格等
- **响应式设计**: 适配不同屏幕尺寸，支持移动端查看
- **全屏模式**: 提供全屏查看体验，专注阅读内容

### 2. 文档信息展示
- **基本信息**: 显示文档标题、类型、状态、创建者等
- **统计信息**: 显示查看次数、最后更新时间等
- **标签分类**: 显示文档类型和状态标签

### 3. 交互功能
- **内容刷新**: 手动刷新文档内容，获取最新版本
- **原文档链接**: 直接跳转到飞书原文档
- **查看统计**: 自动记录文档查看次数

## 使用方法

### 1. 从文档列表进入
1. 在飞书文档管理页面，找到要查看的文档
2. 点击操作列中的"查看"按钮
3. 系统会跳转到文档查看页面

### 2. 直接访问
- 通过URL直接访问：`/feishu/view/{documentId}`
- 其中 `{documentId}` 是文档的数据库ID

### 3. 查看操作
- **阅读内容**: 在内容区域滚动查看文档内容
- **全屏查看**: 点击"全屏查看"按钮进入全屏模式
- **刷新内容**: 点击"刷新内容"按钮获取最新内容
- **打开原文档**: 点击"打开原文档"按钮在新窗口打开飞书文档

## 技术实现

### 前端组件

#### 1. FeishuDocumentViewer.vue
- **功能**: 核心文档查看组件
- **特性**: 
  - Markdown内容渲染
  - 全屏查看支持
  - 错误处理和重试
  - 响应式布局

#### 2. FeishuDocumentView.vue
- **功能**: 文档查看页面
- **特性**:
  - 页面导航
  - 文档信息加载
  - 错误状态处理

### 后端API

#### 1. 获取文档内容
```
GET /admin/feishu/documents/{id}/content
```
- **功能**: 获取指定文档的内容
- **返回**: 文档内容（Markdown格式）

#### 2. 增加查看次数
```
POST /admin/feishu/documents/{id}/view
```
- **功能**: 增加文档的查看次数
- **返回**: 更新后的查看次数

#### 3. 获取文档信息
```
GET /admin/feishu/documents/{id}
```
- **功能**: 获取文档的基本信息
- **返回**: 文档详细信息

### 数据流程

1. **页面加载**: 
   - 获取文档基本信息
   - 加载文档内容
   - 渲染Markdown内容

2. **内容获取**:
   - 优先从数据库缓存获取
   - 缓存不存在时模拟从飞书API获取
   - 更新数据库缓存

3. **查看统计**:
   - 每次查看自动增加计数
   - 实时更新查看次数

## 样式特性

### 1. Markdown样式
- **标题**: 分级标题样式，清晰的层次结构
- **段落**: 合适的行间距和段落间距
- **列表**: 有序和无序列表的美化样式
- **代码**: 代码块和行内代码的高亮显示
- **引用**: 引用块的特殊样式
- **表格**: 表格的边框和斑马纹样式

### 2. 响应式设计
- **桌面端**: 宽屏布局，充分利用屏幕空间
- **平板端**: 适中的布局，保持可读性
- **移动端**: 紧凑布局，优化触摸操作

### 3. 交互反馈
- **加载状态**: 骨架屏和加载动画
- **错误状态**: 友好的错误提示和重试按钮
- **成功反馈**: 操作成功的提示信息

## 配置说明

### 1. 依赖安装
确保前端项目已安装必要依赖：
```bash
npm install marked@^5.1.1
```

### 2. 路由配置
在 `router/index.js` 中已配置文档查看路由：
```javascript
{
  path: 'feishu/view/:id',
  name: 'FeishuDocumentView',
  component: () => import('@/views/FeishuDocumentView.vue'),
  meta: { title: '查看文档', hidden: true }
}
```

### 3. API配置
在 `api/feishu.js` 中已配置相关API方法：
- `getFeishuDocument(id)`: 获取文档信息
- `getFeishuDocumentContent(id)`: 获取文档内容
- `incrementFeishuDocumentViewCount(id)`: 增加查看次数

## 测试数据

系统提供了测试数据脚本 `feishu-test-data.sql`，包含：
- 数学基础知识总结（包含丰富的Markdown内容）
- 语文阅读理解技巧
- 英语单词记忆法

可以使用这些测试数据来验证文档查看功能。

## 注意事项

### 1. 权限控制
- 确保用户有查看文档的权限
- 检查文档的访问级别设置

### 2. 内容安全
- Markdown内容经过安全渲染
- 防止XSS攻击

### 3. 性能优化
- 内容缓存机制减少API调用
- 懒加载和按需渲染

### 4. 错误处理
- 网络错误的重试机制
- 友好的错误提示信息
- 降级处理方案

## 扩展功能

### 1. 实际飞书API集成
当前使用模拟数据，实际部署时需要：
- 配置飞书API密钥
- 实现真实的API调用
- 处理API限制和错误

### 2. 高级功能
- 文档评论功能
- 文档收藏功能
- 文档分享功能
- 文档版本历史

### 3. 搜索和导航
- 文档内容搜索
- 目录导航
- 相关文档推荐

## 故障排除

### 1. 文档加载失败
- 检查文档ID是否正确
- 确认后端服务正常运行
- 查看浏览器控制台错误信息

### 2. Markdown渲染异常
- 检查marked库是否正确安装
- 确认内容格式是否正确
- 查看渲染错误信息

### 3. 样式显示问题
- 检查CSS样式是否正确加载
- 确认响应式断点设置
- 验证浏览器兼容性

通过以上指南，您可以完整地使用和维护飞书文档查看功能。
