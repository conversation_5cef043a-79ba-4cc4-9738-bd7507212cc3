# 飞书OpenAPI集成指南

## 概述

本指南详细介绍如何配置和使用飞书OpenAPI集成功能，实现获取个人空间文档列表、同步文档到本地数据库等功能。

## 功能特性

### 🔗 **API集成功能**
- **访问令牌管理**: 自动获取和缓存飞书租户访问令牌
- **文档列表获取**: 获取个人空间的文档列表
- **文档内容获取**: 支持多种文档类型的内容获取
- **文档同步**: 将飞书文档同步到本地数据库
- **连接测试**: 测试API连接状态和配置

### 📄 **支持的文档类型**
- **doc/docx**: 飞书文档
- **sheet**: 飞书表格  
- **slides**: 飞书演示文稿
- **bitable**: 多维表格
- **mindnote**: 思维笔记

### 🎯 **管理功能**
- **文档预览**: 在线预览文档内容
- **批量同步**: 批量同步文档到本地
- **搜索筛选**: 按名称和类型筛选文档
- **分页浏览**: 支持大量文档的分页显示

## 配置步骤

### 1. 创建飞书应用

#### A. 登录飞书开放平台
1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 使用飞书账号登录
3. 进入"开发者后台"

#### B. 创建企业自建应用
1. 点击"创建应用"
2. 选择"企业自建应用"
3. 填写应用信息：
   - **应用名称**: LAIT文档管理系统
   - **应用描述**: 用于管理和同步飞书文档的系统
   - **应用图标**: 上传应用图标

#### C. 获取应用凭证
1. 在应用详情页面，找到"凭证与基础信息"
2. 记录以下信息：
   - **App ID**: 应用ID
   - **App Secret**: 应用密钥

### 2. 配置应用权限

#### A. 添加API权限
在"权限管理"页面，添加以下权限：

**云文档权限**:
- `drive:drive`: 查看、编辑、创建和删除云空间中的文件
- `drive:drive.readonly`: 查看云空间中的文件
- `drive:drive.file`: 查看、编辑、创建和删除云空间中的文件

**用户信息权限**:
- `contact:user.id:readonly`: 获取用户ID
- `contact:user.base:readonly`: 获取用户基本信息

#### B. 设置重定向URL（如需要）
如果使用OAuth流程，需要设置重定向URL

### 3. 配置系统参数

#### A. 环境变量配置
在系统环境变量或启动参数中设置：

```bash
# 飞书应用配置
export FEISHU_APP_ID="your_app_id_here"
export FEISHU_APP_SECRET="your_app_secret_here"
```

#### B. application.yml配置
在 `backend/src/main/resources/application.yml` 中：

```yaml
feishu:
  api:
    # 飞书应用配置
    app-id: ${FEISHU_APP_ID:}
    app-secret: ${FEISHU_APP_SECRET:}
    
    # 启用API
    enabled: true  # 设置为true启用飞书API
    
    # 其他配置保持默认值
    page-size: 50
    connect-timeout: 10000
    read-timeout: 30000
```

### 4. 启动和测试

#### A. 启动后端服务
```bash
cd backend
mvn spring-boot:run
```

#### B. 启动前端服务
```bash
cd admin-frontend
npm run dev
```

#### C. 访问管理页面
1. 登录系统
2. 访问"飞书API管理"页面
3. 点击"测试连接"验证配置

## 使用方法

### 1. 测试API连接

#### A. 访问管理页面
- 路径: `/feishu-api`
- 菜单: "飞书API管理"

#### B. 测试连接
1. 点击"测试连接"按钮
2. 查看连接状态：
   - ✅ **已连接**: 配置正确，可以正常使用
   - ❌ **未连接**: 检查配置或网络连接

#### C. 查看连接信息
- 用户信息显示
- 错误信息提示
- 连接状态实时更新

### 2. 获取文档列表

#### A. 获取个人空间文档
1. 点击"获取文档列表"按钮
2. 系统会调用飞书API获取文档
3. 文档列表显示在表格中

#### B. 搜索和筛选
- **按名称搜索**: 在搜索框输入关键词
- **按类型筛选**: 选择文档类型进行筛选
- **调整页面大小**: 设置每页显示的文档数量

#### C. 文档信息显示
- 文档名称和图标
- 文件类型标签
- 文件大小
- 修改时间
- 创建者信息

### 3. 文档操作

#### A. 预览文档
1. 点击文档行的"预览"按钮
2. 系统获取文档内容
3. 在弹窗中显示Markdown格式的内容

#### B. 同步文档
1. **单个同步**: 点击"同步"按钮同步单个文档
2. **批量同步**: 点击"同步到本地"批量同步所有文档
3. 同步结果会显示成功数量和错误信息

#### C. 打开原文档
- 点击"打开"按钮在新窗口打开飞书原文档

### 4. 查看同步结果

#### A. 同步状态
- 实时显示同步进度
- 成功/失败状态提示
- 详细的错误信息

#### B. 本地文档管理
- 同步后的文档会出现在"飞书文档"列表中
- 可以在本地进行查看和管理

## API接口说明

### 后端API接口

#### 1. 测试连接
```
GET /api/admin/feishu/api/test-connection
```
- **功能**: 测试飞书API连接状态
- **返回**: 连接状态、用户信息、错误信息

#### 2. 获取文档列表
```
GET /api/admin/feishu/api/personal-documents
```
- **参数**: 
  - `folderId`: 文件夹ID（可选）
  - `pageToken`: 分页标记（可选）
  - `pageSize`: 每页大小（默认50）
- **返回**: 文档列表、统计信息

#### 3. 同步文档
```
POST /api/admin/feishu/api/sync-documents
```
- **参数**: 
  - `folderId`: 文件夹ID（可选）
  - `pageSize`: 每页大小（可选）
- **返回**: 同步结果、成功数量

#### 4. 获取文档内容
```
GET /api/admin/feishu/api/content/{fileToken}
```
- **参数**: 
  - `fileToken`: 文件Token
  - `fileType`: 文件类型（可选）
- **返回**: 文档内容（Markdown格式）

### 前端API调用

#### 1. 测试连接
```javascript
import { testFeishuApiConnection } from '@/api/feishu'

const testConnection = async () => {
  try {
    const response = await testFeishuApiConnection()
    console.log('连接状态:', response.data.connected)
  } catch (error) {
    console.error('测试失败:', error)
  }
}
```

#### 2. 获取文档列表
```javascript
import { getFeishuPersonalDocuments } from '@/api/feishu'

const loadDocuments = async () => {
  try {
    const response = await getFeishuPersonalDocuments({
      pageSize: 50
    })
    console.log('文档列表:', response.data.files)
  } catch (error) {
    console.error('获取失败:', error)
  }
}
```

## 故障排除

### 1. 连接失败

#### A. 检查配置
- 确认App ID和App Secret正确
- 检查环境变量是否设置
- 验证application.yml配置

#### B. 检查权限
- 确认应用已获得必要的API权限
- 检查权限是否已生效
- 验证应用状态是否正常

#### C. 网络问题
- 检查服务器网络连接
- 确认可以访问飞书API域名
- 检查防火墙设置

### 2. 获取文档失败

#### A. 权限问题
- 确认有访问个人空间的权限
- 检查文档访问权限
- 验证用户身份

#### B. API限制
- 检查API调用频率限制
- 确认请求参数正确
- 验证Token有效性

### 3. 同步失败

#### A. 数据库问题
- 检查数据库连接
- 确认表结构正确
- 验证数据完整性

#### B. 内容解析问题
- 检查文档格式支持
- 确认内容编码正确
- 验证Markdown转换

## 最佳实践

### 1. 安全配置
- 使用环境变量存储敏感信息
- 定期更新App Secret
- 限制API访问权限

### 2. 性能优化
- 合理设置页面大小
- 使用缓存减少API调用
- 异步处理大量数据

### 3. 错误处理
- 实现重试机制
- 记录详细错误日志
- 提供用户友好的错误提示

### 4. 监控和维护
- 监控API调用状态
- 定期检查Token有效性
- 及时处理同步异常

通过以上配置和使用方法，您可以成功集成飞书OpenAPI，实现文档的获取、预览和同步功能。
