package com.lait.repository;

import com.lait.entity.WrongQuestion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 错题数据访问层
 */
@Repository
public interface WrongQuestionRepository extends JpaRepository<WrongQuestion, Long> {

    /**
     * 根据学生ID查找错题
     */
    List<WrongQuestion> findByStudentId(Long studentId);

    /**
     * 根据题目ID查找错题
     */
    List<WrongQuestion> findByQuestionId(Long questionId);

    /**
     * 根据学生和题目查找错题
     */
    Optional<WrongQuestion> findByStudentIdAndQuestionId(Long studentId, Long questionId);

    /**
     * 根据复习状态查找错题
     */
    List<WrongQuestion> findByReviewStatus(WrongQuestion.ReviewStatus reviewStatus);

    /**
     * 根据学生和复习状态查找错题
     */
    List<WrongQuestion> findByStudentIdAndReviewStatus(Long studentId, WrongQuestion.ReviewStatus reviewStatus);

    /**
     * 分页查询错题（排除已删除）
     */
    @Query("SELECT wq FROM WrongQuestion wq WHERE wq.isDeleted = false")
    Page<WrongQuestion> findAllActive(Pageable pageable);

    /**
     * 根据学生分页查询错题
     */
    @Query("SELECT wq FROM WrongQuestion wq WHERE wq.isDeleted = false AND wq.studentId = :studentId")
    Page<WrongQuestion> findByStudentIdAndNotDeleted(@Param("studentId") Long studentId, Pageable pageable);

    /**
     * 统计学生错题数量
     */
    @Query("SELECT COUNT(wq) FROM WrongQuestion wq WHERE wq.isDeleted = false AND wq.studentId = :studentId")
    Long countByStudentId(@Param("studentId") Long studentId);

    /**
     * 统计学生待复习错题数量
     */
    @Query("SELECT COUNT(wq) FROM WrongQuestion wq WHERE wq.isDeleted = false AND wq.studentId = :studentId AND wq.reviewStatus = 'PENDING'")
    Long countPendingByStudentId(@Param("studentId") Long studentId);

    /**
     * 统计学生已掌握错题数量
     */
    @Query("SELECT COUNT(wq) FROM WrongQuestion wq WHERE wq.isDeleted = false AND wq.studentId = :studentId AND wq.isMastered = true")
    Long countMasteredByStudentId(@Param("studentId") Long studentId);
}
