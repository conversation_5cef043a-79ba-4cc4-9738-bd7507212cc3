<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入工作流"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-section">
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="文件导入" name="file">
          <div class="file-upload">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="true"
              :limit="1"
              accept=".json"
              @change="handleFileChange"
              @remove="handleFileRemove"
            >
              <template #trigger>
                <el-button type="primary">
                  <el-icon><Upload /></el-icon>
                  选择配置文件
                </el-button>
              </template>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传JSON格式的配置文件，且不超过1MB
                </div>
              </template>
            </el-upload>
          </div>
        </el-tab-pane>

        <el-tab-pane label="JSON导入" name="json">
          <div class="json-input">
            <el-input
              v-model="jsonConfig"
              type="textarea"
              :rows="12"
              placeholder="请粘贴工作流配置的JSON内容"
              @blur="validateJsonConfig"
            />
            <div v-if="jsonValidation" class="validation-message">
              <el-text :type="jsonValidation.valid ? 'success' : 'danger'">
                {{ jsonValidation.message }}
              </el-text>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="模板导入" name="template">
          <div class="template-section">
            <div class="template-list">
              <el-row :gutter="20">
                <el-col
                  v-for="template in templates"
                  :key="template.name"
                  :span="12"
                >
                  <el-card
                    class="template-card"
                    :class="{ 'selected': selectedTemplate === template }"
                    @click="selectTemplate(template)"
                  >
                    <div class="template-content">
                      <h4>{{ template.name }}</h4>
                      <p>{{ template.description }}</p>
                      <el-tag :type="getTypeTagType(template.type)">
                        {{ getTypeLabel(template.type) }}
                      </el-tag>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
            <div v-if="selectedTemplate" class="template-preview">
              <el-divider>模板预览</el-divider>
              <el-input
                :model-value="formatJson(selectedTemplate.config)"
                type="textarea"
                :rows="8"
                readonly
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div v-if="previewData" class="preview-section">
      <el-divider>导入预览</el-divider>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="工作流名称">
          {{ previewData.name || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="类型">
          {{ getTypeLabel(previewData.type) || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ previewData.description || '无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleImport"
          :loading="importing"
          :disabled="!canImport"
        >
          导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { importWorkflow, getWorkflowTemplates, workflowTypes } from '@/api/cozeWorkflow'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const uploadRef = ref()
const importing = ref(false)
const activeTab = ref('file')
const jsonConfig = ref('')
const jsonValidation = ref(null)
const previewData = ref(null)
const selectedTemplate = ref(null)
const templates = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const canImport = computed(() => {
  switch (activeTab.value) {
    case 'file':
      return previewData.value !== null
    case 'json':
      return jsonValidation.value && jsonValidation.value.valid
    case 'template':
      return selectedTemplate.value !== null
    default:
      return false
  }
})

// 方法
const getTypeLabel = (type) => {
  const typeObj = workflowTypes.find(t => t.value === type)
  return typeObj ? typeObj.label : type
}

const getTypeTagType = (type) => {
  const typeMap = {
    'QUESTION_GENERATION': 'primary',
    'CONTENT_ANALYSIS': 'success',
    'STUDY_GUIDANCE': 'warning',
    'ANSWER_EXPLANATION': 'info',
    'PERFORMANCE_ANALYSIS': 'danger',
    'CUSTOM': ''
  }
  return typeMap[type] || ''
}

const loadTemplates = async () => {
  try {
    const response = await getWorkflowTemplates()
    if (response.data) {
      templates.value = response.data
    }
  } catch (error) {
    console.error('加载模板失败:', error)
  }
}

const handleFileChange = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target.result)
      previewData.value = config
      jsonConfig.value = e.target.result
    } catch (error) {
      ElMessage.error('文件格式错误，请上传有效的JSON文件')
      uploadRef.value.clearFiles()
      previewData.value = null
    }
  }
  reader.readAsText(file.raw)
}

const handleFileRemove = () => {
  previewData.value = null
  jsonConfig.value = ''
}

const validateJsonConfig = () => {
  if (!jsonConfig.value || jsonConfig.value.trim() === '') {
    jsonValidation.value = null
    previewData.value = null
    return
  }

  try {
    const config = JSON.parse(jsonConfig.value)
    jsonValidation.value = {
      valid: true,
      message: 'JSON格式正确'
    }
    previewData.value = config
  } catch (error) {
    jsonValidation.value = {
      valid: false,
      message: 'JSON格式错误: ' + error.message
    }
    previewData.value = null
  }
}

const selectTemplate = (template) => {
  selectedTemplate.value = template
  previewData.value = {
    name: template.name,
    description: template.description,
    type: template.type
  }
}

const getImportConfig = () => {
  switch (activeTab.value) {
    case 'file':
    case 'json':
      return jsonConfig.value
    case 'template':
      return JSON.stringify({
        name: selectedTemplate.value.name,
        description: selectedTemplate.value.description,
        type: selectedTemplate.value.type,
        config: selectedTemplate.value.config
      })
    default:
      return null
  }
}

const handleImport = async () => {
  try {
    const config = getImportConfig()
    if (!config) {
      ElMessage.error('请选择要导入的配置')
      return
    }

    importing.value = true
    const response = await importWorkflow(config)
    
    if (response.data) {
      ElMessage.success('导入成功')
      emit('success')
      handleClose()
    }
  } catch (error) {
    ElMessage.error('导入失败: ' + (error.message || '未知错误'))
  } finally {
    importing.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  activeTab.value = 'file'
  jsonConfig.value = ''
  jsonValidation.value = null
  previewData.value = null
  selectedTemplate.value = null
  uploadRef.value?.clearFiles()
}

const formatJson = (jsonStr) => {
  try {
    const obj = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return jsonStr
  }
}

// 生命周期
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.import-section {
  margin-bottom: 20px;
}

.file-upload {
  padding: 20px 0;
  text-align: center;
}

.json-input {
  padding: 15px 0;
}

.validation-message {
  margin-top: 8px;
  font-size: 12px;
}

.template-section {
  padding: 15px 0;
}

.template-list {
  margin-bottom: 20px;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 15px;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.template-card.selected {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.template-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.template-content p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.template-preview {
  margin-top: 20px;
}

.preview-section {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

:deep(.el-upload__tip) {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}
</style>
