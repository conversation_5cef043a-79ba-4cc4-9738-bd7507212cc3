import request from './request'

// 获取题目列表
export function getQuestions(params) {
  return request({
    url: '/questions',
    method: 'get',
    params
  })
}

// 获取题目详情
export function getQuestion(id) {
  return request({
    url: `/questions/${id}`,
    method: 'get'
  })
}

// 根据学科获取题目
export function getQuestionsBySubject(subjectId, params) {
  return request({
    url: `/questions/subject/${subjectId}`,
    method: 'get',
    params
  })
}

// 提交答案
export function submitAnswer(data) {
  return request({
    url: '/student/questions/submit',
    method: 'post',
    data
  })
}

// 获取练习题目
export function getPracticeQuestions(params) {
  return request({
    url: '/student/questions/practice',
    method: 'get',
    params
  })
}

// 获取随机题目
export function getRandomQuestions(params) {
  return request({
    url: '/student/questions/random',
    method: 'get',
    params
  })
}

// ========== 飞书文档题目导入 ==========

// 从飞书文档导入题目
export function importQuestionsFromFeishu(data) {
  return request({
    url: '/student/questions/import-from-feishu',
    method: 'post',
    data
  })
}

// 预览飞书文档题目
export function previewFeishuQuestions(data) {
  return request({
    url: '/student/questions/preview-feishu',
    method: 'post',
    data
  })
}
