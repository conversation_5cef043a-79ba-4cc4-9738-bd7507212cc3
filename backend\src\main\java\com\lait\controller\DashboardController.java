package com.lait.controller;

import com.lait.service.DashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 仪表盘控制器
 */
@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
@Slf4j
public class DashboardController {

    private final DashboardService dashboardService;

    /**
     * 获取仪表盘统计数据
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getDashboardStats() {
        try {
            log.info("获取仪表盘统计数据");
            Map<String, Object> stats = dashboardService.getDashboardStats();
            log.info("成功获取仪表盘统计数据");
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取仪表盘统计数据失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取最近活动
     */
    @GetMapping("/activities")
    public ResponseEntity<Map<String, Object>> getRecentActivities() {
        try {
            log.info("获取最近活动");
            Map<String, Object> activities = dashboardService.getRecentActivities();
            log.info("成功获取最近活动");
            return ResponseEntity.ok(activities);
        } catch (Exception e) {
            log.error("获取最近活动失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取用户注册趋势
     */
    @GetMapping("/user-trend")
    public ResponseEntity<Map<String, Object>> getUserRegistrationTrend(
            @RequestParam(defaultValue = "30") Integer days) {
        try {
            log.info("获取用户注册趋势 - days: {}", days);
            Map<String, Object> trend = dashboardService.getUserRegistrationTrend(days);
            log.info("成功获取用户注册趋势");
            return ResponseEntity.ok(trend);
        } catch (Exception e) {
            log.error("获取用户注册趋势失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取学科分布
     */
    @GetMapping("/subject-distribution")
    public ResponseEntity<Map<String, Object>> getSubjectDistribution() {
        try {
            log.info("获取学科分布");
            Map<String, Object> distribution = dashboardService.getSubjectDistribution();
            log.info("成功获取学科分布");
            return ResponseEntity.ok(distribution);
        } catch (Exception e) {
            log.error("获取学科分布失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取题目统计
     */
    @GetMapping("/question-stats")
    public ResponseEntity<Map<String, Object>> getQuestionStats() {
        try {
            log.info("获取题目统计");
            Map<String, Object> stats = dashboardService.getQuestionStats();
            log.info("成功获取题目统计");
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取题目统计失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取笔记统计
     */
    @GetMapping("/note-stats")
    public ResponseEntity<Map<String, Object>> getNoteStats() {
        try {
            log.info("获取笔记统计");
            Map<String, Object> stats = dashboardService.getNoteStats();
            log.info("成功获取笔记统计");
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取笔记统计失败", e);
            return ResponseEntity.status(500).build();
        }
    }
}
