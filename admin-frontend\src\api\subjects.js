import request from './request'

// 获取学科列表
export function getSubjects(params) {
  return request({
    url: '/subjects',
    method: 'get',
    params
  })
}

// 获取学科详情
export function getSubject(id) {
  return request({
    url: `/subjects/${id}`,
    method: 'get'
  })
}

// 创建学科
export function createSubject(data) {
  return request({
    url: '/subjects',
    method: 'post',
    data
  })
}

// 更新学科
export function updateSubject(id, data) {
  return request({
    url: `/subjects/${id}`,
    method: 'put',
    data
  })
}

// 删除学科
export function deleteSubject(id) {
  return request({
    url: `/subjects/${id}`,
    method: 'delete'
  })
}

// 激活学科
export function activateSubject(id) {
  return request({
    url: `/subjects/${id}/activate`,
    method: 'put'
  })
}

// 停用学科
export function deactivateSubject(id) {
  return request({
    url: `/subjects/${id}/deactivate`,
    method: 'put'
  })
}

// 获取学科统计信息
export function getSubjectStats() {
  return request({
    url: '/subjects/stats',
    method: 'get'
  })
}

// 批量删除学科
export function batchDeleteSubjects(ids) {
  return request({
    url: '/subjects/batch',
    method: 'delete',
    data: { ids }
  })
}

// 获取活跃学科列表
export function getActiveSubjects() {
  return request({
    url: '/subjects/active',
    method: 'get'
  })
}
