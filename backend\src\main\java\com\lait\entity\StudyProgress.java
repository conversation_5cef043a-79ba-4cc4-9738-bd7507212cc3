package com.lait.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 学习进度实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "study_progress")
public class StudyProgress extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    @Column(name = "subject_id", nullable = false)
    private Long subjectId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subject_id", insertable = false, updatable = false)
    private Subject subject;

    @Column(name = "chapter")
    private String chapter;

    @Column(name = "total_questions")
    private Integer totalQuestions = 0;

    @Column(name = "completed_questions")
    private Integer completedQuestions = 0;

    @Column(name = "correct_questions")
    private Integer correctQuestions = 0;

    @Column(name = "total_time_spent")
    private Integer totalTimeSpent = 0; // 总学习时间（分钟）

    @Column(name = "last_study_time")
    private LocalDateTime lastStudyTime;

    @Column(name = "study_streak")
    private Integer studyStreak = 0; // 连续学习天数

    @Column(name = "mastery_level")
    @Enumerated(EnumType.STRING)
    private MasteryLevel masteryLevel = MasteryLevel.BEGINNER;

    @Column(name = "completion_percentage")
    private Double completionPercentage = 0.0;

    @Column(name = "accuracy_rate")
    private Double accuracyRate = 0.0;

    @Column(name = "average_time_per_question")
    private Double averageTimePerQuestion = 0.0;

    @Column(columnDefinition = "JSON")
    private String statistics; // 详细统计数据

    @Column(columnDefinition = "JSON")
    private String weakPoints; // 薄弱知识点

    @Column(columnDefinition = "JSON")
    private String strongPoints; // 强项知识点

    /**
     * 掌握程度枚举
     */
    public enum MasteryLevel {
        BEGINNER("初学者"),
        BASIC("基础"),
        INTERMEDIATE("中级"),
        ADVANCED("高级"),
        EXPERT("专家");

        private final String description;

        MasteryLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
