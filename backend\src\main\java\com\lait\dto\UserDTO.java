package com.lait.dto;

import com.lait.entity.User;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 用户数据传输对象
 */
@Data
public class UserDTO {

    private Long id;

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;

    @Email(message = "邮箱格式不正确")
    private String email;

    private String realName;

    private String phoneNumber;

    private User.UserRole role;

    private User.UserStatus status;

    private String avatarUrl;

    private LocalDateTime lastLoginTime;

    private Integer gradeLevel;

    private String className;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;

    /**
     * 创建用户请求DTO
     */
    @Data
    public static class CreateUserRequest {
        @NotBlank(message = "用户名不能为空")
        @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
        private String username;

        @NotBlank(message = "密码不能为空")
        @Size(min = 6, message = "密码长度不能少于6个字符")
        private String password;

        @Email(message = "邮箱格式不正确")
        private String email;

        private String realName;

        private String phoneNumber;

        private User.UserRole role;

        private Integer gradeLevel;

        private String className;
    }

    /**
     * 更新用户请求DTO
     */
    @Data
    public static class UpdateUserRequest {
        @Email(message = "邮箱格式不正确")
        private String email;

        private String realName;

        private String phoneNumber;

        private User.UserStatus status;

        private String avatarUrl;

        private Integer gradeLevel;

        private String className;
    }

    /**
     * 修改密码请求DTO
     */
    @Data
    public static class ChangePasswordRequest {
        @NotBlank(message = "原密码不能为空")
        private String oldPassword;

        @NotBlank(message = "新密码不能为空")
        @Size(min = 6, message = "新密码长度不能少于6个字符")
        private String newPassword;
    }
}
