package com.lait.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lait.entity.CozeWorkflow;
import com.lait.entity.CozeWorkflowExecution;
import com.lait.repository.CozeWorkflowRepository;
import com.lait.repository.CozeWorkflowExecutionRepository;
import com.lait.service.CozeWorkflowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Coze工作流服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CozeWorkflowServiceImpl implements CozeWorkflowService {

    private final CozeWorkflowRepository workflowRepository;
    private final CozeWorkflowExecutionRepository executionRepository;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public CozeWorkflow createWorkflow(CozeWorkflow workflow) {
        log.info("创建工作流: {}", workflow.getName());

        // 检查名称是否已存在
        if (workflowRepository.existsByName(workflow.getName())) {
            throw new RuntimeException("工作流名称已存在");
        }

        // 设置默认值
        if (workflow.getStatus() == null) {
            workflow.setStatus(CozeWorkflow.WorkflowStatus.DRAFT);
        }
        if (workflow.getExecutionCount() == null) {
            workflow.setExecutionCount(0L);
        }
        if (workflow.getSuccessCount() == null) {
            workflow.setSuccessCount(0L);
        }
        if (workflow.getFailureCount() == null) {
            workflow.setFailureCount(0L);
        }
        if (workflow.getIsPublic() == null) {
            workflow.setIsPublic(false);
        }
        if (workflow.getVersion() == null) {
            workflow.setVersion(1);
        }

        workflow.setCreatedTime(LocalDateTime.now());
        workflow.setUpdatedTime(LocalDateTime.now());

        return workflowRepository.save(workflow);
    }

    @Override
    @Transactional
    public CozeWorkflow updateWorkflow(Long id, CozeWorkflow workflow) {
        log.info("更新工作流: {}", id);

        CozeWorkflow existingWorkflow = getWorkflowById(id);

        // 检查名称是否与其他工作流冲突
        if (!existingWorkflow.getName().equals(workflow.getName()) &&
            workflowRepository.existsByName(workflow.getName())) {
            throw new RuntimeException("工作流名称已存在");
        }

        // 更新字段
        existingWorkflow.setName(workflow.getName());
        existingWorkflow.setDescription(workflow.getDescription());
        existingWorkflow.setType(workflow.getType());
        existingWorkflow.setConfig(workflow.getConfig());
        existingWorkflow.setInputConfig(workflow.getInputConfig());
        existingWorkflow.setOutputConfig(workflow.getOutputConfig());
        existingWorkflow.setCategory(workflow.getCategory());
        existingWorkflow.setTags(workflow.getTags());
        existingWorkflow.setIsPublic(workflow.getIsPublic());
        existingWorkflow.setUpdatedTime(LocalDateTime.now());

        // 如果配置有变化，增加版本号
        if (!Objects.equals(existingWorkflow.getConfig(), workflow.getConfig())) {
            existingWorkflow.setVersion(existingWorkflow.getVersion() + 1);
        }

        return workflowRepository.save(existingWorkflow);
    }

    @Override
    @Transactional
    public void deleteWorkflow(Long id) {
        log.info("删除工作流: {}", id);

        CozeWorkflow workflow = getWorkflowById(id);

        // 检查是否有正在执行的任务
        List<CozeWorkflowExecution> runningExecutions = executionRepository.findRunningExecutions();
        boolean hasRunningExecution = runningExecutions.stream()
                .anyMatch(execution -> execution.getWorkflowId().equals(id));

        if (hasRunningExecution) {
            throw new RuntimeException("工作流正在执行中，无法删除");
        }

        // 删除相关的执行记录
        List<CozeWorkflowExecution> executions = executionRepository.findByWorkflowId(id);
        executionRepository.deleteAll(executions);

        // 删除工作流
        workflowRepository.delete(workflow);
    }

    @Override
    public CozeWorkflow getWorkflowById(Long id) {
        return workflowRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("工作流不存在: " + id));
    }

    @Override
    public Page<CozeWorkflow> getWorkflows(String name, CozeWorkflow.WorkflowType type,
                                         CozeWorkflow.WorkflowStatus status, String category,
                                         Long creatorId, Pageable pageable) {
        return workflowRepository.findWorkflows(name, type, status, category, creatorId, pageable);
    }

    @Override
    public List<CozeWorkflow> getAllWorkflows() {
        return workflowRepository.findAll(Sort.by(Sort.Direction.DESC, "createdTime"));
    }

    @Override
    public List<CozeWorkflow> getWorkflowsByStatus(CozeWorkflow.WorkflowStatus status) {
        return workflowRepository.findByStatus(status);
    }

    @Override
    public List<CozeWorkflow> getWorkflowsByType(CozeWorkflow.WorkflowType type) {
        return workflowRepository.findByType(type);
    }

    @Override
    public List<CozeWorkflow> getPublicWorkflows() {
        return workflowRepository.findByIsPublicTrue();
    }

    @Override
    @Transactional
    public CozeWorkflowExecution executeWorkflow(Long workflowId, Map<String, Object> inputData, Long executorId) {
        log.info("执行工作流: {}", workflowId);

        CozeWorkflow workflow = getWorkflowById(workflowId);

        if (workflow.getStatus() != CozeWorkflow.WorkflowStatus.ACTIVE) {
            throw new RuntimeException("工作流未激活，无法执行");
        }

        // 创建执行记录
        CozeWorkflowExecution execution = new CozeWorkflowExecution();
        execution.setWorkflowId(workflowId);
        execution.setStatus(CozeWorkflowExecution.ExecutionStatus.PENDING);
        execution.setExecutorId(executorId);
        execution.setExecutionType(CozeWorkflowExecution.ExecutionType.MANUAL);
        execution.setTriggerType(CozeWorkflowExecution.TriggerType.USER);
        execution.setStartTime(LocalDateTime.now());

        try {
            execution.setInputData(objectMapper.writeValueAsString(inputData));
        } catch (Exception e) {
            log.warn("序列化输入数据失败", e);
        }

        execution = executionRepository.save(execution);

        // 模拟执行工作流
        try {
            execution.setStatus(CozeWorkflowExecution.ExecutionStatus.RUNNING);
            executionRepository.save(execution);

            // 这里应该调用实际的Coze API执行工作流
            Map<String, Object> result = executeWorkflowInternal(workflow, inputData);

            execution.setStatus(CozeWorkflowExecution.ExecutionStatus.SUCCESS);
            execution.setOutputData(objectMapper.writeValueAsString(result));
            execution.setEndTime(LocalDateTime.now());
            execution.setDuration(java.time.Duration.between(execution.getStartTime(), execution.getEndTime()).toMillis());

            // 更新工作流统计
            updateWorkflowStatistics(workflow, true, execution.getDuration());

        } catch (Exception e) {
            log.error("工作流执行失败", e);
            execution.setStatus(CozeWorkflowExecution.ExecutionStatus.FAILED);
            execution.setErrorMessage(e.getMessage());
            execution.setEndTime(LocalDateTime.now());
            execution.setDuration(java.time.Duration.between(execution.getStartTime(), execution.getEndTime()).toMillis());

            // 更新工作流统计
            updateWorkflowStatistics(workflow, false, execution.getDuration());
        }

        return executionRepository.save(execution);
    }

    @Override
    public String executeWorkflowAsync(Long workflowId, Map<String, Object> inputData, Long executorId) {
        // 异步执行的实现
        // 这里可以使用Spring的@Async或者消息队列
        String executionId = UUID.randomUUID().toString();

        // 创建异步任务
        // CompletableFuture.runAsync(() -> {
        //     executeWorkflow(workflowId, inputData, executorId);
        // });

        return executionId;
    }

    @Override
    public Map<String, Object> testWorkflow(Long workflowId, Map<String, Object> inputData) {
        log.info("测试工作流: {}", workflowId);

        CozeWorkflow workflow = getWorkflowById(workflowId);

        try {
            return executeWorkflowInternal(workflow, inputData);
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }

    /**
     * 内部执行工作流逻辑
     */
    private Map<String, Object> executeWorkflowInternal(CozeWorkflow workflow, Map<String, Object> inputData) {
        // 这里应该实现实际的Coze API调用逻辑
        // 目前返回模拟结果
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("workflowId", workflow.getId());
        result.put("workflowName", workflow.getName());
        result.put("executionTime", LocalDateTime.now());
        result.put("inputData", inputData);
        result.put("output", "模拟执行结果");

        return result;
    }

    /**
     * 更新工作流统计信息
     */
    private void updateWorkflowStatistics(CozeWorkflow workflow, boolean success, Long duration) {
        workflow.setExecutionCount(workflow.getExecutionCount() + 1);
        workflow.setLastExecutedAt(LocalDateTime.now());

        if (success) {
            workflow.setSuccessCount(workflow.getSuccessCount() + 1);
        } else {
            workflow.setFailureCount(workflow.getFailureCount() + 1);
        }

        // 更新平均执行时间
        if (duration != null) {
            if (workflow.getAvgExecutionTime() == null) {
                workflow.setAvgExecutionTime(duration);
            } else {
                long totalTime = workflow.getAvgExecutionTime() * (workflow.getExecutionCount() - 1) + duration;
                workflow.setAvgExecutionTime(totalTime / workflow.getExecutionCount());
            }
        }

        workflowRepository.save(workflow);
    }

    @Override
    public Page<CozeWorkflowExecution> getWorkflowExecutions(Long workflowId, CozeWorkflowExecution.ExecutionStatus status,
                                                           Long executorId, LocalDateTime startTime, LocalDateTime endTime,
                                                           Pageable pageable) {
        return executionRepository.findExecutions(workflowId, status, executorId, startTime, endTime, pageable);
    }

    @Override
    public CozeWorkflowExecution getExecutionById(Long executionId) {
        return executionRepository.findById(executionId)
                .orElseThrow(() -> new RuntimeException("执行记录不存在: " + executionId));
    }

    @Override
    @Transactional
    public void cancelExecution(Long executionId) {
        log.info("取消执行: {}", executionId);

        CozeWorkflowExecution execution = getExecutionById(executionId);

        if (execution.getStatus() == CozeWorkflowExecution.ExecutionStatus.RUNNING ||
            execution.getStatus() == CozeWorkflowExecution.ExecutionStatus.PENDING) {
            execution.setStatus(CozeWorkflowExecution.ExecutionStatus.CANCELLED);
            execution.setEndTime(LocalDateTime.now());
            execution.setDuration(java.time.Duration.between(execution.getStartTime(), execution.getEndTime()).toMillis());
            executionRepository.save(execution);
        } else {
            throw new RuntimeException("执行记录状态不允许取消");
        }
    }

    @Override
    @Transactional
    public CozeWorkflowExecution retryExecution(Long executionId) {
        log.info("重新执行: {}", executionId);

        CozeWorkflowExecution originalExecution = getExecutionById(executionId);

        // 解析原始输入数据
        Map<String, Object> inputData = new HashMap<>();
        try {
            if (originalExecution.getInputData() != null) {
                inputData = objectMapper.readValue(originalExecution.getInputData(), Map.class);
            }
        } catch (Exception e) {
            log.warn("解析输入数据失败", e);
        }

        return executeWorkflow(originalExecution.getWorkflowId(), inputData, originalExecution.getExecutorId());
    }

    @Override
    @Transactional
    public CozeWorkflow duplicateWorkflow(Long workflowId, String newName) {
        log.info("复制工作流: {} -> {}", workflowId, newName);

        CozeWorkflow originalWorkflow = getWorkflowById(workflowId);

        // 检查新名称是否已存在
        if (workflowRepository.existsByName(newName)) {
            throw new RuntimeException("工作流名称已存在");
        }

        CozeWorkflow newWorkflow = new CozeWorkflow();
        newWorkflow.setName(newName);
        newWorkflow.setDescription(originalWorkflow.getDescription() + " (副本)");
        newWorkflow.setType(originalWorkflow.getType());
        newWorkflow.setStatus(CozeWorkflow.WorkflowStatus.DRAFT);
        newWorkflow.setConfig(originalWorkflow.getConfig());
        newWorkflow.setInputConfig(originalWorkflow.getInputConfig());
        newWorkflow.setOutputConfig(originalWorkflow.getOutputConfig());
        newWorkflow.setCategory(originalWorkflow.getCategory());
        newWorkflow.setTags(originalWorkflow.getTags());
        newWorkflow.setCreatorId(originalWorkflow.getCreatorId());
        newWorkflow.setCreatorName(originalWorkflow.getCreatorName());
        newWorkflow.setIsPublic(false); // 副本默认为私有

        return createWorkflow(newWorkflow);
    }

    @Override
    @Transactional
    public CozeWorkflow importWorkflow(String workflowConfig) {
        log.info("导入工作流配置");

        try {
            Map<String, Object> config = objectMapper.readValue(workflowConfig, Map.class);

            CozeWorkflow workflow = new CozeWorkflow();
            workflow.setName((String) config.get("name"));
            workflow.setDescription((String) config.get("description"));
            workflow.setType(CozeWorkflow.WorkflowType.valueOf((String) config.get("type")));
            workflow.setConfig(workflowConfig);
            workflow.setStatus(CozeWorkflow.WorkflowStatus.DRAFT);

            return createWorkflow(workflow);
        } catch (Exception e) {
            log.error("导入工作流失败", e);
            throw new RuntimeException("导入工作流失败: " + e.getMessage());
        }
    }

    @Override
    public String exportWorkflow(Long workflowId) {
        log.info("导出工作流: {}", workflowId);

        CozeWorkflow workflow = getWorkflowById(workflowId);

        try {
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("name", workflow.getName());
            exportData.put("description", workflow.getDescription());
            exportData.put("type", workflow.getType().name());
            exportData.put("config", workflow.getConfig());
            exportData.put("inputConfig", workflow.getInputConfig());
            exportData.put("outputConfig", workflow.getOutputConfig());
            exportData.put("category", workflow.getCategory());
            exportData.put("tags", workflow.getTags());
            exportData.put("version", workflow.getVersion());
            exportData.put("exportTime", LocalDateTime.now());

            return objectMapper.writeValueAsString(exportData);
        } catch (Exception e) {
            log.error("导出工作流失败", e);
            throw new RuntimeException("导出工作流失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void activateWorkflow(Long workflowId) {
        log.info("激活工作流: {}", workflowId);

        CozeWorkflow workflow = getWorkflowById(workflowId);
        workflow.setStatus(CozeWorkflow.WorkflowStatus.ACTIVE);
        workflow.setUpdatedTime(LocalDateTime.now());
        workflowRepository.save(workflow);
    }

    @Override
    @Transactional
    public void deactivateWorkflow(Long workflowId) {
        log.info("停用工作流: {}", workflowId);

        CozeWorkflow workflow = getWorkflowById(workflowId);
        workflow.setStatus(CozeWorkflow.WorkflowStatus.INACTIVE);
        workflow.setUpdatedTime(LocalDateTime.now());
        workflowRepository.save(workflow);
    }

    @Override
    @Transactional
    public void archiveWorkflow(Long workflowId) {
        log.info("归档工作流: {}", workflowId);

        CozeWorkflow workflow = getWorkflowById(workflowId);
        workflow.setStatus(CozeWorkflow.WorkflowStatus.ARCHIVED);
        workflow.setUpdatedTime(LocalDateTime.now());
        workflowRepository.save(workflow);
    }

    @Override
    public Map<String, Object> getWorkflowStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总数统计
        long totalCount = workflowRepository.count();
        long activeCount = workflowRepository.countByStatus(CozeWorkflow.WorkflowStatus.ACTIVE);
        long draftCount = workflowRepository.countByStatus(CozeWorkflow.WorkflowStatus.DRAFT);
        long inactiveCount = workflowRepository.countByStatus(CozeWorkflow.WorkflowStatus.INACTIVE);

        stats.put("totalCount", totalCount);
        stats.put("activeCount", activeCount);
        stats.put("draftCount", draftCount);
        stats.put("inactiveCount", inactiveCount);

        // 类型统计
        List<Object[]> typeStats = workflowRepository.countByType();
        Map<String, Long> typeCountMap = new HashMap<>();
        for (Object[] stat : typeStats) {
            typeCountMap.put(((CozeWorkflow.WorkflowType) stat[0]).name(), (Long) stat[1]);
        }
        stats.put("typeStats", typeCountMap);

        // 执行统计
        long totalExecutions = executionRepository.count();
        stats.put("totalExecutions", totalExecutions);

        return stats;
    }

    @Override
    public Map<String, Object> getExecutionStatistics(Long workflowId) {
        Map<String, Object> stats = new HashMap<>();

        long totalExecutions = executionRepository.countByWorkflowId(workflowId);
        long successCount = executionRepository.countSuccessByWorkflowId(workflowId);
        long failureCount = executionRepository.countFailureByWorkflowId(workflowId);
        Double avgDuration = executionRepository.avgDurationByWorkflowId(workflowId);

        stats.put("totalExecutions", totalExecutions);
        stats.put("successCount", successCount);
        stats.put("failureCount", failureCount);
        stats.put("successRate", totalExecutions > 0 ? (double) successCount / totalExecutions * 100 : 0);
        stats.put("avgDuration", avgDuration != null ? avgDuration : 0);

        return stats;
    }

    @Override
    public List<CozeWorkflow> getPopularWorkflows(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return workflowRepository.findPopularWorkflows(pageable);
    }

    @Override
    public List<CozeWorkflow> getRecentlyExecutedWorkflows(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return workflowRepository.findRecentlyExecuted(pageable);
    }

    @Override
    @Transactional
    public void cleanupOldExecutions(int daysToKeep) {
        log.info("清理{}天前的执行记录", daysToKeep);

        LocalDateTime threshold = LocalDateTime.now().minusDays(daysToKeep);
        executionRepository.deleteOldExecutions(threshold);
    }

    @Override
    public Map<String, Object> validateWorkflowConfig(String config) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证JSON格式
            objectMapper.readValue(config, Map.class);
            result.put("valid", true);
            result.put("message", "配置格式正确");
        } catch (Exception e) {
            result.put("valid", false);
            result.put("message", "配置格式错误: " + e.getMessage());
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getWorkflowTemplates() {
        List<Map<String, Object>> templates = new ArrayList<>();

        // 题目生成模板
        Map<String, Object> questionTemplate = new HashMap<>();
        questionTemplate.put("name", "题目生成工作流");
        questionTemplate.put("type", CozeWorkflow.WorkflowType.QUESTION_GENERATION);
        questionTemplate.put("description", "基于学科和难度生成题目");
        questionTemplate.put("config", "{}");
        templates.add(questionTemplate);

        // 内容分析模板
        Map<String, Object> analysisTemplate = new HashMap<>();
        analysisTemplate.put("name", "内容分析工作流");
        analysisTemplate.put("type", CozeWorkflow.WorkflowType.CONTENT_ANALYSIS);
        analysisTemplate.put("description", "分析学习内容和学生表现");
        analysisTemplate.put("config", "{}");
        templates.add(analysisTemplate);

        return templates;
    }
}
