package com.lait.service;

import com.lait.dto.WrongQuestionDTO;
import com.lait.entity.WrongQuestion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 错题服务接口
 */
public interface WrongQuestionService {

    /**
     * 创建错题记录
     */
    WrongQuestionDTO createWrongQuestion(WrongQuestionDTO.CreateWrongQuestionRequest request);

    /**
     * 根据ID获取错题
     */
    WrongQuestionDTO getWrongQuestionById(Long id);

    /**
     * 更新错题信息
     */
    WrongQuestionDTO updateWrongQuestion(Long id, WrongQuestionDTO.UpdateWrongQuestionRequest request);

    /**
     * 删除错题记录
     */
    void deleteWrongQuestion(Long id);

    /**
     * 分页查询错题
     */
    Page<WrongQuestionDTO> getWrongQuestions(Pageable pageable);

    /**
     * 根据学生ID分页查询错题
     */
    Page<WrongQuestionDTO> getWrongQuestionsByStudentId(Long studentId, Pageable pageable);

    /**
     * 根据条件查询错题
     */
    Page<WrongQuestionDTO> searchWrongQuestions(WrongQuestionDTO.WrongQuestionQueryRequest request, Pageable pageable);

    /**
     * 根据复习状态查询错题
     */
    Page<WrongQuestionDTO> getWrongQuestionsByStatus(Long studentId, WrongQuestion.ReviewStatus status, Pageable pageable);

    /**
     * 获取待复习的错题
     */
    List<WrongQuestionDTO> getPendingReviewQuestions(Long studentId, int limit);

    /**
     * 标记错题掌握状态
     */
    void markWrongQuestionMastered(WrongQuestionDTO.MarkMasteredRequest request);

    /**
     * 批量标记错题掌握状态
     */
    void batchMarkWrongQuestionsMastered(WrongQuestionDTO.BatchMarkMasteredRequest request);

    /**
     * 增加错误次数
     */
    void incrementWrongCount(Long studentId, Long questionId, String studentAnswer);

    /**
     * 获取学生错题统计
     */
    WrongQuestionDTO.WrongQuestionStatistics getStudentWrongQuestionStatistics(Long studentId);

    /**
     * 获取学科错题统计
     */
    List<WrongQuestionDTO.SubjectWrongStatistics> getSubjectWrongStatistics(Long studentId);

    /**
     * 获取最常错的题目类型
     */
    List<String> getMostWrongQuestionTypes(Long studentId, Long subjectId);

    /**
     * 获取复习建议
     */
    List<WrongQuestionDTO> getReviewSuggestions(Long studentId, int limit);

    /**
     * 重置错题状态
     */
    void resetWrongQuestionStatus(Long wrongQuestionId);

    /**
     * 批量删除错题记录
     */
    void batchDeleteWrongQuestions(Long[] wrongQuestionIds);

    /**
     * 检查是否已存在错题记录
     */
    boolean existsWrongQuestion(Long studentId, Long questionId);

    /**
     * 获取错题掌握率
     */
    Double getWrongQuestionMasteryRate(Long studentId, Long subjectId);

    /**
     * 获取最近错题
     */
    List<WrongQuestionDTO> getRecentWrongQuestions(Long studentId, int limit);
}
