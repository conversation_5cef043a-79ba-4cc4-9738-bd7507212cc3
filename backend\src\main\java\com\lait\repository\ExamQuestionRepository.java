package com.lait.repository;

import com.lait.entity.ExamQuestion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 考试题目关联数据访问层
 */
@Repository
public interface ExamQuestionRepository extends JpaRepository<ExamQuestion, Long> {

    /**
     * 根据考试ID查找题目
     */
    List<ExamQuestion> findByExamIdOrderByQuestionOrder(Long examId);

    /**
     * 根据题目ID查找考试
     */
    List<ExamQuestion> findByQuestionId(Long questionId);

    /**
     * 根据考试ID和题目ID查找关联
     */
    Optional<ExamQuestion> findByExamIdAndQuestionId(Long examId, Long questionId);

    /**
     * 统计考试题目数量
     */
    @Query("SELECT COUNT(eq) FROM ExamQuestion eq WHERE eq.examId = :examId")
    Long countByExamId(@Param("examId") Long examId);

    /**
     * 获取考试总分
     */
    @Query("SELECT SUM(eq.points) FROM ExamQuestion eq WHERE eq.examId = :examId")
    Integer sumPointsByExamId(@Param("examId") Long examId);

    /**
     * 删除考试的所有题目
     */
    @Modifying
    @Query("DELETE FROM ExamQuestion eq WHERE eq.examId = :examId")
    void deleteByExamId(@Param("examId") Long examId);

    /**
     * 删除特定的考试题目关联
     */
    @Modifying
    @Query("DELETE FROM ExamQuestion eq WHERE eq.examId = :examId AND eq.questionId = :questionId")
    void deleteByExamIdAndQuestionId(@Param("examId") Long examId, @Param("questionId") Long questionId);

    /**
     * 更新题目顺序
     */
    @Modifying
    @Query("UPDATE ExamQuestion eq SET eq.questionOrder = :order WHERE eq.examId = :examId AND eq.questionId = :questionId")
    void updateQuestionOrder(@Param("examId") Long examId, @Param("questionId") Long questionId, @Param("order") Integer order);

    /**
     * 获取考试中的最大题目顺序
     */
    @Query("SELECT MAX(eq.questionOrder) FROM ExamQuestion eq WHERE eq.examId = :examId")
    Integer getMaxQuestionOrder(@Param("examId") Long examId);

    /**
     * 检查题目是否已在考试中
     */
    boolean existsByExamIdAndQuestionId(Long examId, Long questionId);
}
