package com.lait.repository;

import com.lait.entity.FeishuDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 飞书文档数据访问层
 */
@Repository
public interface FeishuDocumentRepository extends JpaRepository<FeishuDocument, Long> {

    /**
     * 根据文档ID查找
     */
    Optional<FeishuDocument> findByDocId(String docId);

    /**
     * 根据文档Token查找
     */
    Optional<FeishuDocument> findByDocToken(String docToken);

    /**
     * 根据创建者查找文档
     */
    List<FeishuDocument> findByCreatorIdOrderByCreatedTimeDesc(Long creatorId);

    /**
     * 根据学科查找文档
     */
    List<FeishuDocument> findBySubjectIdOrderByCreatedTimeDesc(Long subjectId);

    /**
     * 根据文档类型查找
     */
    List<FeishuDocument> findByDocType(FeishuDocument.DocumentType docType);

    /**
     * 根据状态查找文档
     */
    List<FeishuDocument> findByStatus(FeishuDocument.DocumentStatus status);

    /**
     * 查找公开文档
     */
    List<FeishuDocument> findByIsPublicTrueAndStatusOrderByCreatedTimeDesc(FeishuDocument.DocumentStatus status);

    /**
     * 根据访问权限查找文档
     */
    List<FeishuDocument> findByAccessLevel(FeishuDocument.AccessLevel accessLevel);

    /**
     * 查找需要同步的文档
     */
    @Query("SELECT d FROM FeishuDocument d WHERE d.syncStatus IN ('PENDING', 'FAILED') OR " +
           "(d.syncStatus = 'SYNCED' AND d.lastSyncAt < :threshold)")
    List<FeishuDocument> findDocumentsNeedingSync(@Param("threshold") LocalDateTime threshold);

    /**
     * 分页查询文档
     */
    @Query("SELECT d FROM FeishuDocument d WHERE " +
           "(:title IS NULL OR d.title LIKE %:title%) AND " +
           "(:docType IS NULL OR d.docType = :docType) AND " +
           "(:status IS NULL OR d.status = :status) AND " +
           "(:creatorId IS NULL OR d.creatorId = :creatorId) AND " +
           "(:subjectId IS NULL OR d.subjectId = :subjectId) AND " +
           "(:category IS NULL OR d.category = :category) " +
           "ORDER BY d.createdTime DESC")
    Page<FeishuDocument> findDocumentsWithFilters(@Param("title") String title,
                                                  @Param("docType") FeishuDocument.DocumentType docType,
                                                  @Param("status") FeishuDocument.DocumentStatus status,
                                                  @Param("creatorId") Long creatorId,
                                                  @Param("subjectId") Long subjectId,
                                                  @Param("category") String category,
                                                  Pageable pageable);

    /**
     * 搜索文档内容
     */
    @Query("SELECT d FROM FeishuDocument d WHERE " +
           "(d.title LIKE %:keyword% OR d.content LIKE %:keyword% OR d.summary LIKE %:keyword%) AND " +
           "d.status = 'ACTIVE' " +
           "ORDER BY d.viewCount DESC, d.createdTime DESC")
    Page<FeishuDocument> searchDocuments(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 统计各类型文档数量
     */
    @Query("SELECT d.docType, COUNT(d) FROM FeishuDocument d GROUP BY d.docType")
    List<Object[]> countByDocType();

    /**
     * 统计各状态文档数量
     */
    @Query("SELECT d.status, COUNT(d) FROM FeishuDocument d GROUP BY d.status")
    List<Object[]> countByStatus();

    /**
     * 统计指定状态的文档数量
     */
    Long countByStatus(FeishuDocument.DocumentStatus status);

    /**
     * 统计指定同步状态的文档数量
     */
    Long countBySyncStatus(FeishuDocument.SyncStatus syncStatus);

    /**
     * 计算所有文档的查看次数总和
     */
    @Query("SELECT SUM(d.viewCount) FROM FeishuDocument d")
    Long sumViewCount();

    /**
     * 统计各学科文档数量
     */
    @Query("SELECT d.subjectId, COUNT(d) FROM FeishuDocument d GROUP BY d.subjectId")
    List<Object[]> countBySubject();

    /**
     * 更新文档查看次数
     */
    @Modifying
    @Query("UPDATE FeishuDocument d SET d.viewCount = d.viewCount + 1 WHERE d.id = :id")
    void incrementViewCount(@Param("id") Long id);

    /**
     * 更新同步状态
     */
    @Modifying
    @Query("UPDATE FeishuDocument d SET d.syncStatus = :syncStatus, d.lastSyncAt = :lastSyncAt WHERE d.id = :id")
    void updateSyncStatus(@Param("id") Long id,
                         @Param("syncStatus") FeishuDocument.SyncStatus syncStatus,
                         @Param("lastSyncAt") LocalDateTime lastSyncAt);

    /**
     * 检查文档ID是否存在
     */
    boolean existsByDocId(String docId);

    /**
     * 检查文档Token是否存在
     */
    boolean existsByDocToken(String docToken);

    /**
     * 获取热门文档
     */
    @Query("SELECT d FROM FeishuDocument d WHERE d.status = 'ACTIVE' AND d.isPublic = true " +
           "ORDER BY d.viewCount DESC, d.createdTime DESC")
    List<FeishuDocument> findPopularDocuments(Pageable pageable);

    /**
     * 获取最近更新的文档
     */
    @Query("SELECT d FROM FeishuDocument d WHERE d.status = 'ACTIVE' " +
           "ORDER BY d.updatedTime DESC")
    List<FeishuDocument> findRecentlyUpdatedDocuments(Pageable pageable);

    /**
     * 按查看次数降序排列获取文档
     */
    Page<FeishuDocument> findAllByOrderByViewCountDesc(Pageable pageable);

    /**
     * 按创建时间降序排列获取文档
     */
    List<FeishuDocument> findTop5ByOrderByCreatedTimeDesc();

    /**
     * 统计指定学科的文档数量
     */
    Long countBySubjectId(Long subjectId);
}
