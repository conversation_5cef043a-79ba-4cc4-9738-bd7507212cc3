package com.lait.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 飞书文档实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "feishu_documents")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FeishuDocument extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 文档标题
     */
    @Column(nullable = false, length = 200)
    private String title;

    /**
     * 飞书文档ID
     */
    @Column(nullable = false, unique = true, length = 100)
    private String docId;

    /**
     * 飞书文档Token
     */
    @Column(length = 200)
    private String docToken;

    /**
     * 文档类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private DocumentType docType;

    /**
     * 文档状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private DocumentStatus status;

    /**
     * 文档URL
     */
    @Column(length = 500)
    private String docUrl;

    /**
     * 文档内容 (缓存)
     */
    @Column(columnDefinition = "LONGTEXT")
    private String content;

    /**
     * 文档摘要
     */
    @Column(length = 1000)
    private String summary;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 创建者信息
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creatorId", insertable = false, updatable = false)
    private User creator;

    /**
     * 关联学科ID
     */
    private Long subjectId;

    /**
     * 关联学科信息
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subjectId", insertable = false, updatable = false)
    private Subject subject;

    /**
     * 文档分类
     */
    @Column(length = 100)
    private String category;

    /**
     * 标签 (JSON数组格式)
     */
    @Column(length = 500)
    private String tags;

    /**
     * 是否公开
     */
    @Column(nullable = false)
    private Boolean isPublic = false;

    /**
     * 访问权限
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private AccessLevel accessLevel;

    /**
     * 最后同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime lastSyncAt;

    /**
     * 同步状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private SyncStatus syncStatus;

    /**
     * 版本号
     */
    @Column(nullable = false)
    private Integer version = 1;

    /**
     * 查看次数
     */
    @Column(nullable = false)
    private Long viewCount = 0L;

    /**
     * 文档配置 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String config;

    /**
     * 设置文档URL，同时自动提取docToken
     */
    public void setDocUrl(String docUrl) {
        this.docUrl = docUrl;
        this.docToken = extractDocTokenFromUrl(docUrl);
    }

    /**
     * 从URL中提取docToken
     * 支持的URL格式：
     * - https://bytedance.feishu.cn/docs/doccnxxxxxx
     * - https://bytedance.feishu.cn/sheets/shtcnxxxxxx
     * - https://bytedance.feishu.cn/slides/sldcnxxxxxx
     * - https://bytedance.feishu.cn/mindnotes/bmncnxxxxxx
     * - https://bytedance.feishu.cn/base/bascnxxxxxx
     */
    private String extractDocTokenFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }

        try {
            // 移除查询参数和锚点
            String cleanUrl = url.split("\\?")[0].split("#")[0];

            // 提取最后一段作为token
            String[] parts = cleanUrl.split("/");
            if (parts.length > 0) {
                String lastPart = parts[parts.length - 1];
                // 验证token格式（通常以特定前缀开头）
                if (isValidDocToken(lastPart)) {
                    return lastPart;
                }
            }
        } catch (Exception e) {
            // 如果提取失败，返回null
            return null;
        }

        return null;
    }

    /**
     * 验证docToken格式是否有效
     */
    private boolean isValidDocToken(String token) {
        if (token == null || token.length() < 10) {
            return false;
        }

        // 飞书文档token通常以特定前缀开头
        return token.startsWith("doccn") ||  // 文档
               token.startsWith("shtcn") ||  // 表格
               token.startsWith("sldcn") ||  // 演示文稿
               token.startsWith("bmncn") ||  // 思维笔记
               token.startsWith("bascn") ||  // 多维表格
               token.startsWith("wikicn") || // 知识库
               token.startsWith("fldcn");    // 文件夹
    }

    /**
     * 文档类型枚举
     */
    public enum DocumentType {
        DOC("文档"),
        SHEET("表格"),
        SLIDE("演示文稿"),
        MINDMAP("思维导图"),
        BITABLE("多维表格");

        private final String description;

        DocumentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 文档状态枚举
     */
    public enum DocumentStatus {
        ACTIVE("正常"),
        ARCHIVED("已归档"),
        DELETED("已删除"),
        DRAFT("草稿");

        private final String description;

        DocumentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 访问权限枚举
     */
    public enum AccessLevel {
        PUBLIC("公开"),
        INTERNAL("内部"),
        PRIVATE("私有"),
        RESTRICTED("受限");

        private final String description;

        AccessLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        SYNCED("已同步"),
        PENDING("待同步"),
        SYNCING("同步中"),
        FAILED("同步失败");

        private final String description;

        SyncStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
